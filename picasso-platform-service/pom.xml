<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>com.fliggy</groupId>
        <artifactId>picasso-platform</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>picasso-platform-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>picasso-platform-service</name>

    <dependencies>
        <dependency>
            <groupId>com.fliggy</groupId>
            <artifactId>picasso-platform-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy</groupId>
            <artifactId>picasso-platform-dal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy</groupId>
            <artifactId>picasso-platform-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-alimonitor-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-alimonitor-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-hsf-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-tair-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-diamond-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-tddl-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-metaq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-sentinel-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.zaxxer</groupId>
                    <artifactId>HikariCP</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-acl-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- ODPS -->
        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-sdk-core</artifactId>
        </dependency>

        <!-- freemarker -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- schedulerx -->
        <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx2-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- dataworks -->
        <dependency>
            <groupId>com.alibaba.phoenix</groupId>
            <artifactId>phoenix-api-model</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>eagleeye-spring-boot-starter</artifactId>
                    <groupId>com.alibaba.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dataworks</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.dataworks</groupId>
            <artifactId>aliyun-java-sdk-dataworks</artifactId>
        </dependency>
        <!-- 弹内版本 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dataworks-inner</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.phoenix</groupId>
            <artifactId>phoenix-api-model</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>eagleeye-spring-boot-starter</artifactId>
                    <groupId>com.alibaba.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- redis -->
        <dependency>
            <groupId>com.aliyun.tair</groupId>
            <artifactId>tairjedis-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.fliggy</groupId>
            <artifactId>easy-flows</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.taobao.ateye</groupId>
            <artifactId>ateye-client</artifactId>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.iwork.shared</groupId>
            <artifactId>mc.api</artifactId>
        </dependency>

        <!-- ihr -->
        <dependency>
            <groupId>com.alibaba.ihr</groupId>
            <artifactId>amdplatform-service-api</artifactId>
        </dependency>

        <!-- uid -->
        <dependency>
            <groupId>com.taobao.common.uic.uic-common</groupId>
            <artifactId>common-uic-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>snappy-java</artifactId>
                    <groupId>org.xerial.snappy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>unitrouter</artifactId>
                    <groupId>com.alibaba.unit.rule</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fulllinkstresstesting</artifactId>
                    <groupId>com.taobao.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.security</groupId>
            <artifactId>security-all</artifactId>
        </dependency>

        <!-- olap -->
        <dependency>
            <groupId>com.fliggy.olap</groupId>
            <artifactId>trip-olap-backend-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fliggy</groupId>
            <artifactId>crowd-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fliggy</groupId>
            <artifactId>picasso-client</artifactId>
           <exclusions>
               <exclusion>
                   <groupId>com.fliggy</groupId>
                   <artifactId>picasso-common</artifactId>
               </exclusion>
           </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alitrip</groupId>
            <artifactId>portal-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mtop-common-service</artifactId>
                    <groupId>com.taobao.wireless</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mtop-core-domain</artifactId>
                    <groupId>com.taobao.wireless</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4-runtime</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.work.alipmc</groupId>
            <artifactId>alipmc-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.platform.shared</groupId>
                    <artifactId>fasttext.all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.taobao.security</groupId>
                    <artifactId>security</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fliggy.fceadmin</groupId>
            <artifactId>fceadmin-client-static-resource</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.trip.trippoi</groupId>
            <artifactId>fliggypoi-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.lindorm</groupId>
            <artifactId>lindorm-client</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-buc-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dingtalk.chatbot</groupId>
            <artifactId>dingtalk-chatbot-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dingtalk.open</groupId>
            <artifactId>dingtalk-openapi-sdk</artifactId>
        </dependency>
        <!--ha3-sql-->
        <dependency>
            <groupId>com.alibaba.search.ha3</groupId>
            <artifactId>ha3-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.search.ha3</groupId>
            <artifactId>ha3-client-protocol</artifactId>
        </dependency>
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>0.9.12</version>
        </dependency>
        <dependency>
            <groupId>org.apache.mina</groupId>
            <artifactId>mina-core</artifactId>
            <version>2.0.18</version>
        </dependency>

        <!-- 人群中心 -->
        <dependency>
            <groupId>com.alibaba.dt</groupId>
            <artifactId>aliac-client</artifactId>
        </dependency>

        <!-- 机票选品-行业接口 -->
        <dependency>
            <groupId>com.fliggy.magnet</groupId>
            <artifactId>magnet-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fliggy.magnet</groupId>
            <artifactId>magnet-domain</artifactId>
        </dependency>

        <!-- pokemon -->
        <dependency>
            <groupId>com.fliggy.pokemon</groupId>
            <artifactId>pokemon-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-sdk-udf</artifactId>
        </dependency>

        <!-- easy excel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <!-- juniversalchardet 用于检测文件编码 -->
        <dependency>
            <groupId>com.googlecode.juniversalchardet</groupId>
            <artifactId>juniversalchardet</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.taobao.travel</groupId>
            <artifactId>travelitems-client</artifactId>
            <version>2.01.90</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.taobao.travel</groupId>
            <artifactId>travelitems-common</artifactId>
            <version>2.01.90</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
        </dependency>
        
        <!-- JSqlParser for SQL parsing -->
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
        </dependency>

        <dependency>
            <groupId>com.taobao.itemcenter</groupId>
            <artifactId>itemcenter-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.itemcenter</groupId>
            <artifactId>itemcenter-service-model</artifactId>
        </dependency>
    </dependencies>
</project>
