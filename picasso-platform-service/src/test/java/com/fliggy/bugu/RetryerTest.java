package com.fliggy.bugu;

import java.io.IOException;
import java.util.Random;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;

/**
 * <AUTHOR>
 * @date 2020/12/1 下午5:16
 */
public class RetryerTest {

    public static void main(String[] args) {

        Random random = new Random();

        Callable<Integer> callable = new Callable<Integer>() {
            public Integer call() throws Exception {
                int i = random.nextInt(10);
                System.out.println("This is " + i);
                return i;
            }
        };

        Retryer<Integer> retryer = RetryerBuilder.<Integer>newBuilder()
            .retryIfResult(i -> i != 5)
            .retryIfExceptionOfType(IOException.class)
            .retryIfRuntimeException()
            .withWaitStrategy(WaitStrategies.fibonacciWait(100, 2, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.neverStop())
            .build();
        try {
            retryer.call(callable);
        } catch (RetryException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }

    }

}
