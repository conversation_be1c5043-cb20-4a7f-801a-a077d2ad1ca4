package com.fliggy.picasso.group.schedulerx.check;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.common.message.MessageExt;

import com.fliggy.picasso.dao.LabelUserResultDO;
import com.fliggy.picasso.mapper.picasso.LabelUserResultDAO;
import com.fliggy.picasso.service.groovy.GroovyScriptService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 标签对账重试消费者测试类
 * 
 * <AUTHOR>
 * @date 2025/5/29
 */
@RunWith(MockitoJUnitRunner.class)
public class LabelCheckRetryConsumerTest {
    
    @Mock
    private LabelUserResultDAO labelUserResultDAO;
    
    @Mock
    private GroovyScriptService groovyScriptService;
    
    @Mock
    private ConsumeConcurrentlyContext context;
    
    @InjectMocks
    private LabelCheckRetryConsumer consumer;
    
    @Before
    public void setUp() {
        // 初始化测试环境
    }
    
    @Test
    public void testConsumeMessageWithSuccessfulRetry() {
        // 准备测试数据
        MessageExt message = createTestMessage(createRetryMessage(1L, "test_label", "user_001", 1, "VIP"));
        List<MessageExt> messages = List.of(message);
        
        // 模拟数据库查询返回记录
        LabelUserResultDO record = createTestRecord();
        when(labelUserResultDAO.selectByPrimaryKey(1L)).thenReturn(record);
        
        // 模拟Groovy脚本执行成功
        when(groovyScriptService.executeOnlineScript(anyString(), anyMap())).thenReturn(true);
        
        ConsumeConcurrentlyStatus result = consumer.consumeMessage(messages, context);
        
        assertEquals("消费应该成功", ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(groovyScriptService, times(1)).executeOnlineScript(anyString(), anyMap());
        verify(labelUserResultDAO, times(1)).updateByPrimaryKeySelective(any(LabelUserResultDO.class));
    }
    
    @Test
    public void testConsumeMessageWithFailedRetry() {
        // 准备测试数据
        MessageExt message = createTestMessage(createRetryMessage(1L, "test_label", "user_001", 1, "VIP"));
        message.setReconsumeTimes(0); // 第一次重试
        List<MessageExt> messages = List.of(message);
        
        // 模拟数据库查询返回记录
        LabelUserResultDO record = createTestRecord();
        when(labelUserResultDAO.selectByPrimaryKey(1L)).thenReturn(record);
        
        // 模拟Groovy脚本执行失败
        when(groovyScriptService.executeOnlineScript(anyString(), anyMap())).thenReturn(false);
        
        ConsumeConcurrentlyStatus result = consumer.consumeMessage(messages, context);
        
        assertEquals("应该继续重试", ConsumeConcurrentlyStatus.RECONSUME_LATER, result);
        verify(groovyScriptService, times(1)).executeOnlineScript(anyString(), anyMap());
        verify(labelUserResultDAO, times(1)).updateByPrimaryKeySelective(any(LabelUserResultDO.class));
    }
    
    @Test
    public void testConsumeMessageWithMaxRetries() {
        // 准备测试数据
        MessageExt message = createTestMessage(createRetryMessage(1L, "test_label", "user_001", 3, "VIP"));
        message.setReconsumeTimes(3); // 达到最大重试次数
        List<MessageExt> messages = List.of(message);
        
        // 模拟数据库查询返回记录
        LabelUserResultDO record = createTestRecord();
        when(labelUserResultDAO.selectByPrimaryKey(1L)).thenReturn(record);
        
        // 模拟Groovy脚本执行失败
        when(groovyScriptService.executeOnlineScript(anyString(), anyMap())).thenReturn(false);
        
        ConsumeConcurrentlyStatus result = consumer.consumeMessage(messages, context);
        
        assertEquals("达到最大重试次数应该消费成功", ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(groovyScriptService, times(1)).executeOnlineScript(anyString(), anyMap());
        verify(labelUserResultDAO, times(1)).updateByPrimaryKeySelective(any(LabelUserResultDO.class));
    }
    
    @Test
    public void testConsumeMessageWithRecordNotFound() {
        // 准备测试数据
        MessageExt message = createTestMessage(createRetryMessage(999L, "test_label", "user_001", 1, "VIP"));
        List<MessageExt> messages = List.of(message);
        
        // 模拟数据库查询返回空
        when(labelUserResultDAO.selectByPrimaryKey(999L)).thenReturn(null);
        
        ConsumeConcurrentlyStatus result = consumer.consumeMessage(messages, context);
        
        assertEquals("记录不存在应该消费成功", ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(groovyScriptService, never()).executeOnlineScript(anyString(), anyMap());
        verify(labelUserResultDAO, never()).updateByPrimaryKeySelective(any(LabelUserResultDO.class));
    }
    
    @Test
    public void testConsumeMessageWithAlreadyPassed() {
        // 准备测试数据
        MessageExt message = createTestMessage(createRetryMessage(1L, "test_label", "user_001", 1, "VIP"));
        List<MessageExt> messages = List.of(message);
        
        // 模拟数据库查询返回已通过的记录
        LabelUserResultDO record = createTestRecord();
        record.setPass((byte) 1); // 已经通过
        when(labelUserResultDAO.selectByPrimaryKey(1L)).thenReturn(record);
        
        ConsumeConcurrentlyStatus result = consumer.consumeMessage(messages, context);
        
        assertEquals("已通过的记录应该消费成功", ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(groovyScriptService, never()).executeOnlineScript(anyString(), anyMap());
        verify(labelUserResultDAO, never()).updateByPrimaryKeySelective(any(LabelUserResultDO.class));
    }
    
    @Test
    public void testConsumeMessageWithInvalidFormat() {
        // 准备无效格式的消息
        MessageExt message = new MessageExt();
        message.setBody("invalid json".getBytes(StandardCharsets.UTF_8));
        List<MessageExt> messages = List.of(message);
        
        ConsumeConcurrentlyStatus result = consumer.consumeMessage(messages, context);
        
        assertEquals("无效格式应该消费成功", ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(groovyScriptService, never()).executeOnlineScript(anyString(), anyMap());
        verify(labelUserResultDAO, never()).selectByPrimaryKey(anyLong());
    }
    
    @Test
    public void testConsumeMessageWithGroovyException() {
        // 准备测试数据
        MessageExt message = createTestMessage(createRetryMessage(1L, "test_label", "user_001", 1, "VIP"));
        message.setReconsumeTimes(0);
        List<MessageExt> messages = List.of(message);
        
        // 模拟数据库查询返回记录
        LabelUserResultDO record = createTestRecord();
        when(labelUserResultDAO.selectByPrimaryKey(1L)).thenReturn(record);
        
        // 模拟Groovy脚本执行异常
        when(groovyScriptService.executeOnlineScript(anyString(), anyMap()))
            .thenThrow(new RuntimeException("Script execution failed"));
        
        ConsumeConcurrentlyStatus result = consumer.consumeMessage(messages, context);
        
        assertEquals("脚本异常应该继续重试", ConsumeConcurrentlyStatus.RECONSUME_LATER, result);
        verify(groovyScriptService, times(1)).executeOnlineScript(anyString(), anyMap());
    }
    
    @Test
    public void testConsumeMessageWithStringComparison() {
        // 准备测试数据
        MessageExt message = createTestMessage(createRetryMessage(1L, "test_label", "user_001", 1, "VIP"));
        List<MessageExt> messages = List.of(message);
        
        // 模拟数据库查询返回记录
        LabelUserResultDO record = createTestRecord();
        record.setResult("VIP");
        when(labelUserResultDAO.selectByPrimaryKey(1L)).thenReturn(record);
        
        // 模拟Groovy脚本返回匹配的字符串
        when(groovyScriptService.executeOnlineScript(anyString(), anyMap())).thenReturn("VIP");
        
        ConsumeConcurrentlyStatus result = consumer.consumeMessage(messages, context);
        
        assertEquals("字符串匹配应该消费成功", ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(groovyScriptService, times(1)).executeOnlineScript(anyString(), anyMap());
        verify(labelUserResultDAO, times(1)).updateByPrimaryKeySelective(any(LabelUserResultDO.class));
    }
    
    /**
     * 创建测试消息
     */
    private MessageExt createTestMessage(Map<String, Object> retryMessage) {
        MessageExt message = new MessageExt();
        message.setBody(JSON.toJSONString(retryMessage).getBytes(StandardCharsets.UTF_8));
        message.setReconsumeTimes(0);
        return message;
    }
    
    /**
     * 创建重试消息内容
     */
    private Map<String, Object> createRetryMessage(Long recordId, String labelCode, String userId, 
                                                   int retryCount, String originalResult) {
        Map<String, Object> retryMessage = new HashMap<>();
        retryMessage.put("recordId", recordId);
        retryMessage.put("labelCode", labelCode);
        retryMessage.put("userId", userId);
        retryMessage.put("retryCount", retryCount);
        retryMessage.put("originalResult", originalResult);
        retryMessage.put("timestamp", System.currentTimeMillis());
        return retryMessage;
    }
    
    /**
     * 创建测试记录
     */
    private LabelUserResultDO createTestRecord() {
        LabelUserResultDO record = new LabelUserResultDO();
        record.setId(1L);
        record.setLabelCode("test_label");
        record.setUserId("user_001");
        record.setResult("VIP");
        record.setPass((byte) 0);
        record.setRetry(0);
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        return record;
    }
} 