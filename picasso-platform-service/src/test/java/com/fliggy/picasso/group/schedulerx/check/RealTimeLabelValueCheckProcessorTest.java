package com.fliggy.picasso.group.schedulerx.check;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.dao.LabelUserResultDO;
import com.fliggy.picasso.dao.LabelUserResultParam;
import com.fliggy.picasso.mapper.picasso.LabelUserResultDAO;
import com.fliggy.picasso.msg.MetaqMessageSender;
import com.fliggy.picasso.service.groovy.GroovyScriptService;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 实时标签值对账处理器测试类
 * 
 * <AUTHOR>
 * @date 2025/5/29
 */
@RunWith(MockitoJUnitRunner.class)
public class RealTimeLabelValueCheckProcessorTest {
    
    @Mock
    private LabelUserResultDAO labelUserResultDAO;
    
    @Mock
    private GroovyScriptService groovyScriptService;
    
    @Mock
    private MetaqMessageSender labelCheckRetrySender;
    
    @Mock
    private JobContext jobContext;
    
    @InjectMocks
    private RealTimeLabelValueCheckProcessor processor;
    
    @Before
    public void setUp() {
        // 初始化处理器
        processor.init();
        
        // 设置JobContext默认行为，返回DateTime类型
        when(jobContext.getScheduleTime()).thenReturn(DateTime.now());
    }
    
    @Test
    public void testProcessWithNoRecords() throws Exception {
        // 模拟没有记录的情况
        when(labelUserResultDAO.selectByParam(any(LabelUserResultParam.class)))
            .thenReturn(new ArrayList<>());
        
        ProcessResult result = processor.process(jobContext);
        
        assertEquals("处理结果应该为成功", InstanceStatus.SUCCESS, result.getStatus());
        verify(labelUserResultDAO, times(1)).selectByParam(any(LabelUserResultParam.class));
        verify(groovyScriptService, never()).executeOnlineScript(anyString(), anyMap());
    }
    
    @Test
    public void testProcessWithSuccessfulCheck() throws Exception {
        // 准备测试数据
        List<LabelUserResultDO> records = createTestRecords();
        when(labelUserResultDAO.selectByParam(any(LabelUserResultParam.class)))
            .thenReturn(records);
        
        // 模拟Groovy脚本返回成功
        when(groovyScriptService.executeOnlineScript(anyString(), anyMap()))
            .thenReturn(true);
        
        ProcessResult result = processor.process(jobContext);
        
        assertEquals("处理结果应该为成功", InstanceStatus.SUCCESS, result.getStatus());
        verify(groovyScriptService, times(records.size())).executeOnlineScript(anyString(), anyMap());
        verify(labelUserResultDAO, times(records.size())).updateByPrimaryKeySelective(any(LabelUserResultDO.class));
        verify(labelCheckRetrySender, never()).sendMessage(anyString(), anyString());
    }
    
    @Test
    public void testProcessWithFailedCheck() throws Exception {
        // 准备测试数据
        List<LabelUserResultDO> records = createTestRecords();
        when(labelUserResultDAO.selectByParam(any(LabelUserResultParam.class)))
            .thenReturn(records);
        
        // 模拟Groovy脚本返回失败
        when(groovyScriptService.executeOnlineScript(anyString(), anyMap()))
            .thenReturn(false);
        
        // 模拟消息发送成功
        when(labelCheckRetrySender.sendMessage(anyString(), anyString())).thenReturn(true);
        
        ProcessResult result = processor.process(jobContext);
        
        assertEquals("处理结果应该为成功", InstanceStatus.SUCCESS, result.getStatus());
        verify(groovyScriptService, times(records.size())).executeOnlineScript(anyString(), anyMap());
        verify(labelUserResultDAO, times(records.size())).updateByPrimaryKeySelective(any(LabelUserResultDO.class));
        verify(labelCheckRetrySender, times(records.size())).sendMessage(anyString(), anyString());
    }
    
    @Test
    public void testProcessWithGroovyScriptException() throws Exception {
        // 准备测试数据
        List<LabelUserResultDO> records = createTestRecords();
        when(labelUserResultDAO.selectByParam(any(LabelUserResultParam.class)))
            .thenReturn(records);
        
        // 模拟Groovy脚本执行异常
        when(groovyScriptService.executeOnlineScript(anyString(), anyMap()))
            .thenThrow(new RuntimeException("Script execution failed"));
        
        // 模拟消息发送成功
        when(labelCheckRetrySender.sendMessage(anyString(), anyString())).thenReturn(true);
        
        ProcessResult result = processor.process(jobContext);
        
        assertEquals("处理结果应该为成功", InstanceStatus.SUCCESS, result.getStatus());
        verify(groovyScriptService, times(records.size())).executeOnlineScript(anyString(), anyMap());
        verify(labelCheckRetrySender, times(records.size())).sendMessage(anyString(), anyString());
    }
    
    @Test
    public void testProcessWithStringResult() throws Exception {
        // 准备测试数据
        List<LabelUserResultDO> records = createTestRecords();
        records.get(0).setResult("VIP");
        when(labelUserResultDAO.selectByParam(any(LabelUserResultParam.class)))
            .thenReturn(records);
        
        // 模拟Groovy脚本返回匹配的字符串
        when(groovyScriptService.executeOnlineScript(anyString(), anyMap()))
            .thenReturn("VIP");
        
        ProcessResult result = processor.process(jobContext);
        
        assertEquals("处理结果应该为成功", InstanceStatus.SUCCESS, result.getStatus());
        verify(groovyScriptService, times(records.size())).executeOnlineScript(anyString(), anyMap());
        verify(labelCheckRetrySender, never()).sendMessage(anyString(), anyString());
    }
    
    @Test
    public void testProcessWithJsonResult() throws Exception {
        // 准备测试数据
        List<LabelUserResultDO> records = createTestRecords();
        records.get(0).setResult("{\"status\":\"active\",\"level\":\"VIP\"}");
        when(labelUserResultDAO.selectByParam(any(LabelUserResultParam.class)))
            .thenReturn(records);
        
        // 模拟Groovy脚本返回匹配的对象
        when(groovyScriptService.executeOnlineScript(anyString(), anyMap()))
            .thenReturn(java.util.Map.of("status", "active", "level", "VIP"));
        
        ProcessResult result = processor.process(jobContext);
        
        assertEquals("处理结果应该为成功", InstanceStatus.SUCCESS, result.getStatus());
        verify(groovyScriptService, times(records.size())).executeOnlineScript(anyString(), anyMap());
        verify(labelCheckRetrySender, never()).sendMessage(anyString(), anyString());
    }
    
    /**
     * 创建测试记录
     */
    private List<LabelUserResultDO> createTestRecords() {
        List<LabelUserResultDO> records = new ArrayList<>();
        
        LabelUserResultDO record1 = new LabelUserResultDO();
        record1.setId(1L);
        record1.setLabelCode("test_label_1");
        record1.setUserId("user_001");
        record1.setResult("VIP");
        record1.setPass((byte) 0);
        record1.setRetry(0);
        record1.setGmtCreate(new Date());
        record1.setGmtModified(new Date());
        records.add(record1);
        
        LabelUserResultDO record2 = new LabelUserResultDO();
        record2.setId(2L);
        record2.setLabelCode("test_label_2");
        record2.setUserId("user_002");
        record2.setResult("NORMAL");
        record2.setPass((byte) 0);
        record2.setRetry(0);
        record2.setGmtCreate(new Date());
        record2.setGmtModified(new Date());
        records.add(record2);
        
        return records;
    }
} 