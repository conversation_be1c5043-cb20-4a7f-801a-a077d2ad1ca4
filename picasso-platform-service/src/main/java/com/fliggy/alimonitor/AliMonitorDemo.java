package com.fliggy.alimonitor;

import org.springframework.stereotype.Component;

/**
 * AliMonitor拦截的业务方法, 详细使用方法见
 * http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-alimonitor
 *
 * <AUTHOR>
 */
@Component
public class AliMonitorDemo {

    public String exception() throws Exception {
        throw new RuntimeException("monitored by alimonitor");
    }

    public String normal() {
        return "monitored by alimonitor";
    }
}
