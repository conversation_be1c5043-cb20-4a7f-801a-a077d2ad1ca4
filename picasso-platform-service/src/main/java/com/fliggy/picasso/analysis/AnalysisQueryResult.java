package com.fliggy.picasso.analysis;

import java.util.Map;

import com.fliggy.olap.client.domain.OlapQueryResult;
import com.google.common.collect.Maps;

/**
 * <AUTHOR>
 * @date 2021/3/24 下午5:27
 */
public class AnalysisQueryResult {

    private Map<String, OlapQueryResult> group2OlapQueryResult = Maps.newHashMap();

    public void addOlapQueryResult(String group, OlapQueryResult result) {
        group2OlapQueryResult.put(group, result);
    }

    /**
     * 获取指定人群的OlapQueryResult
     *
     * @return
     */
    public OlapQueryResult getGroupOlapQueryResult(String group) {
        return this.group2OlapQueryResult.getOrDefault(group, null);
    }

    /**
     * @return
     */
    public Map<String, OlapQueryResult> getResultMap() {
        return this.group2OlapQueryResult;
    }
}
