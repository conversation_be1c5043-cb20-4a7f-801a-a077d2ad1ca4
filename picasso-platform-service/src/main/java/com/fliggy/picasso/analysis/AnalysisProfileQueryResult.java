package com.fliggy.picasso.analysis;

import java.util.List;

import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/3/25 上午11:39
 */
@Data
public class AnalysisProfileQueryResult {

    private List<OlapPicassoDimension> dimensions;

    private List<SingleRow> results;

    /**
     * 多维度取决于dimensions中最早的数据时间，格式:yyyyMMdd
     */
    private String latestDimensionDate;

    public static class SingleRow {

        /**
         * 指标
         */
        @Getter
        private List<Measure> measures = Lists.newArrayList();

        /**
         * 维度
         */
        @Getter
        private List<DimensionVal> dimensionValList = Lists.newArrayList();

        /**
         *
         */
        @Getter
        @Setter
        private GroupVal group;

        public void addMeasure(Measure measure) {
            measures.add(measure);
        }

        public void addDimensionVal(DimensionVal dimensionVal) {
            dimensionValList.add(dimensionVal);
        }

    }

    @Data
    @AllArgsConstructor
    public static class GroupVal {
        private Long id;
        private String name;
        private Long crowdAmount;
    }

    @Data
    @AllArgsConstructor
    public static class Measure {
        private String code;
        private String name;
        private Number val;
    }

    @Data
    @AllArgsConstructor
    public static class DimensionVal {
        private String code;
        private String name;

        @Override
        public boolean equals(Object o) {
            if (this == o) { return true; }
            if (o == null || getClass() != o.getClass()) { return false; }
            DimensionVal that = (DimensionVal)o;
            return Objects.equal(code, that.code) &&
                Objects.equal(name, that.name);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(code, name);
        }
    }
}
