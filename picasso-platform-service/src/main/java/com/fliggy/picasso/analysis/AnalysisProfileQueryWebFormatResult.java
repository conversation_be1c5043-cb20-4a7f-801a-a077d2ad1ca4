package com.fliggy.picasso.analysis;

import java.util.List;
import java.util.Map;

import com.fliggy.olap.client.domain.OlapQueryResultMeta;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/3/25 上午11:39
 */
@Data
public class AnalysisProfileQueryWebFormatResult {

    private List<OlapPicassoDimension> dimensions;

    /**
     * 多维度取决于dimensions中最早的数据时间，格式:yyyyMMdd
     */
    private String latestDimensionDate;

    private List<Map<String, Object>> data;
    private List<OlapQueryResultMeta> meta;

}
