package com.fliggy.picasso.analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.dao.AnalysisV2DO;
import com.fliggy.picasso.freemarker.TemplateEnum;
import com.fliggy.picasso.freemarker.TemplateParam;
import com.fliggy.picasso.service.analysis.AnalysisV2Service;
import com.fliggy.picasso.utils.AnalysisUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.offline.OdpsService.LOW_PRIORITY;

/**
 * 人群分析-初始化服务
 * 如果是创建中，则生成sql并执行odps任务，拿到instantsId
 *
 * <AUTHOR>
 * Created on 2022/5/26 下午8:51
 */
@Component
public class AnalysisV2InitHandler extends AnalysisV2AbstractHandler<AnalysisV2DO, Boolean, AnalysisV2StatusEnum> {


    @Override
    protected Boolean doHandle(AnalysisV2DO analysisV2DO) {
        try {
            AnalysisV2HandleParam analysisV2HandleParam = analysisV2Service.initAnalysis(analysisV2DO);
            TemplateParam templateParam = analysisV2HandleParam.getTemplateParam();
            TemplateEnum templateEnum = analysisV2HandleParam.getTemplateEnum();
            String sql = templateReader.read(templateEnum, templateParam);
            String odpsTaskName = AnalysisUtils.getOdpsTaskName(analysisV2DO.getCrowdId());
            Pair<String, String> executePair = odpsService.asyncExecute(sql, odpsTaskName, LOW_PRIORITY);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(OdpsConstants.ODPS_INSTANT_ID, executePair.getLeft());
            jsonObject.put(OdpsConstants.ODPS_LOG_VIEW, executePair.getRight());
            jsonObject.put(OdpsConstants.ODPS_SQL, sql);
            jsonObject.put(OdpsConstants.ODPS_INSTANT_TASK_NAME, odpsTaskName);
            analysisV2DO.setStatus(AnalysisV2StatusEnum.RUNNING.getStatus());
            analysisV2DO.setExtInfo(jsonObject.toJSONString());
            analysisV2Service.updateSelective(analysisV2DO, AnalysisV2StatusEnum.INIT.getStatus());
            return true;
        } catch (Exception e) {
            throw createException("AnalysisV2InitHandler处理异常", e, "param", JSON.toJSONString(analysisV2DO));
        }
    }

    @Override
    public AnalysisV2StatusEnum typeOrStatus() {
        return AnalysisV2StatusEnum.INIT;
    }


}
