package com.fliggy.picasso.analysis;

import com.fliggy.picasso.exception.AnalysisException;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2022/5/26 下午3:01
 */
public enum AnalysisV2TypeEnum {

    INSIGHT_ANALYSIS("1", "洞察分析"),
    ;

    private String type;

    private String desc;

    private static Map<String, AnalysisV2TypeEnum> typeEnumMap = new HashMap<>(8);

    static {
        AnalysisV2TypeEnum[] values = AnalysisV2TypeEnum.values();
        for (AnalysisV2TypeEnum value : values) {
            typeEnumMap.put(value.getType(), value);
        }
    }

    public static AnalysisV2TypeEnum getByType(String type) {
        if (!typeEnumMap.containsKey(type)) {
            throw new AnalysisException("不存在该类型: " + type, null);
        }
        return typeEnumMap.get(type);
    }

    AnalysisV2TypeEnum(String status, String desc) {
        this.type = status;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }


}
