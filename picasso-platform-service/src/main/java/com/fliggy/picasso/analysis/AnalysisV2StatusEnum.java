package com.fliggy.picasso.analysis;

import com.fliggy.picasso.exception.AnalysisException;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2022/5/26 下午3:01
 */
public enum AnalysisV2StatusEnum {

    INIT("1", "创建中"),
    RUNNING("2", "执行中"),
    FINISH("3", "执行完成"),
    ERROR("4", "执行异常"),
    ;

    private String status;

    private String desc;

    private static Map<String, AnalysisV2StatusEnum> statusEnumMap = new HashMap<>(8);

    static {
        AnalysisV2StatusEnum[] values = AnalysisV2StatusEnum.values();
        for (AnalysisV2StatusEnum value : values) {
            statusEnumMap.put(value.getStatus(), value);
        }
    }

    public static AnalysisV2StatusEnum getByStatus(String status) {
        if (!statusEnumMap.containsKey(status)) {
            throw new AnalysisException("不存在该状态: " + status, null);
        }
        return statusEnumMap.get(status);
    }

    AnalysisV2StatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }


}
