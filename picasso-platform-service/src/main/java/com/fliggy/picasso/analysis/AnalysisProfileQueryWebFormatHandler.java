package com.fliggy.picasso.analysis;

import com.fliggy.olap.client.domain.OlapQueryResult;
import com.fliggy.olap.client.domain.OlapQueryResultMeta;
import com.fliggy.picasso.client.entity.CrowdMetaInfoSummary;
import com.fliggy.picasso.common.utils.NumberFormatUtils;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.service.enumvalue.EnumDimDataDTO;
import com.fliggy.picasso.service.enumvalue.EnumDimDataService;
import com.fliggy.picasso.service.enumvalue.EnumDimMetaInfoService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.fliggy.picasso.analysis.AnalysisConstants.*;
import static com.fliggy.picasso.common.Constant.SERVICE_LOG;

/**
 * <AUTHOR>
 * @date 2021/3/25 上午11:36
 */
@Component
public class AnalysisProfileQueryWebFormatHandler
    implements AnalysisQueryHandler<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult> {

    private static final Logger LOG = LoggerFactory.getLogger(SERVICE_LOG);

    @Autowired
    private LabelInfoService labelInfoService;

    @Autowired
    private EnumDimDataService enumDimDataService;

    @Autowired
    private EnumDimMetaInfoService enumDimMetaInfoService;

    @Autowired
    private AnalysisExecutor analysisExecutor;

    @Override
    public AnalysisQueryParam handleParam(AnalysisQueryParam analysisProfileQueryParam) {
        return analysisProfileQueryParam;
    }

    @Override
    public AnalysisProfileQueryWebFormatResult handleResult(AnalysisQueryResult analysisQueryResult,
                                                            AnalysisQueryParam analysisQueryParam) {

        // 获取人群信息
        List<CrowdMetaInfoSummary> groups = analysisQueryParam.getGroups();
        Map<Long, CrowdMetaInfoSummary> groupMetaInfoMap = toGroupMap(groups);

        // 获取标签信息
        List<String> dimensionCodes = analysisQueryParam.getDimensions();
        Map<String, LabelInfoDTO> dimensionMetaInfoMap = getDimensionMetaInfo(dimensionCodes);

        // 获取标签枚举值信息
        Map<String, Map<String, EnumDimDataDTO>> enumDataMap = getEnumData(dimensionMetaInfoMap);

        // 查询占比所需的分母人群数信息
        Map<Long, Long> userCntMap = getDimensionRollingUpCnt(analysisQueryParam);

        AnalysisProfileQueryWebFormatResult result = new AnalysisProfileQueryWebFormatResult();
        List<OlapPicassoDimension> dimensions = dimensionMetaInfoMap.values()
            .stream()
            .map(
                labelInfoDTO -> new OlapPicassoDimension(labelInfoDTO.getCode(), labelInfoDTO.getName(), labelInfoDTO.getSourceDataDs())
            )
            .collect(Collectors.toList());

        result.setDimensions(dimensions);
        result.setLatestDimensionDate(getLatestDimensionDateTimeFormat(dimensions));

        Map<String, OlapQueryResult> groupResultMap = analysisQueryResult.getResultMap();
        Map<String, OlapQueryResultMeta> metaMap = mergeOlapQueryResultMetas(groupResultMap.values(), userCntMap);
        List<Map<String, Object>> convertedData = mergeOlapQueryResultData(groupMetaInfoMap, enumDataMap,
            groupResultMap, userCntMap);

        // List<Map<String, Object>> sortedData = sortByDimension(convertedData, dimensionCodes, enumDataMap);
        result.setMeta(Lists.newArrayList(metaMap.values()));
        result.setData(convertedData);

        return result;
    }

    /**
     * @param analysisQueryParam
     * @return
     */
    private Map<Long, Long> getDimensionRollingUpCnt(AnalysisQueryParam analysisQueryParam) {
        // 擦除维度
        AnalysisQueryParam param = new AnalysisQueryParam();
        param.setMeasures(analysisQueryParam.getMeasures());
        param.setFilters(analysisQueryParam.getFilters());
        param.setLimit(analysisQueryParam.getLimit());
        param.setStart(analysisQueryParam.getStart());
        param.setEnd(analysisQueryParam.getEnd());
        param.setGroups(analysisQueryParam.getGroups());

        AnalysisQueryResult result = analysisExecutor.query(param);
        Map<Long, Long> userCntMap = Maps.newHashMap();
        if (result.getResultMap() == null || result.getResultMap().size() == 0) {
            return userCntMap;
        }
        result.getResultMap().forEach(
            (group, groupResult) -> {

                List<Map<String, Object>> data = groupResult.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    if (data.size() == 1) {
                        Long userCnt = (Long)data.get(0).get(OLAP_MEASURE_PICASSO_GROUP_USER_CNT);
                        userCntMap.put(Long.valueOf(group), userCnt);
                    } else {
                        LOG.error("Get Dimension-RollingUp User-Cnt Error! Result.size:" + data.size());
                    }
                } else {
                    LOG.error("Get Dimension-RollingUp User-Cnt Error! Data Is Empty!");
                }
            }
        );

        return userCntMap;
    }

    private Map<Long, CrowdMetaInfoSummary> toGroupMap(List<CrowdMetaInfoSummary> groups) {
        Map<Long, CrowdMetaInfoSummary> map = com.fliggy.picasso.common.utils.CollectionUtils
            .listToMap(groups, CrowdMetaInfoSummary::getId);
        return map;
    }

    /**
     * @param convertedData
     * @param dimensionCodes
     * @param enumDataMap
     * @return
     */
    private List<Map<String, Object>> sortByDimension(List<Map<String, Object>> convertedData,
                                                      List<String> dimensionCodes,
                                                      Map<String, Map<String, EnumDimDataDTO>> enumDataMap) {
        if (CollectionUtils.isEmpty(convertedData) || CollectionUtils.isEmpty(dimensionCodes)) {
            return convertedData;
        }

        Collections.sort(
            convertedData,
            // 按维度-枚举值ID大小排序
            (o1, o2) -> {
                if (o1 == null) {
                    return -1;
                }

                if (o2 == null) {
                    return 1;
                }

                for (String dim : dimensionCodes) {
                    if (enumDataMap.containsKey(dim)
                        && o1.containsKey(dim)
                        && o2.containsKey(dim)) {
                        Map<String, EnumDimDataDTO> enumDimDataDTOMap = enumDataMap.get(dim);
                        Object o1Value = o1.get(dim);
                        Object o2Value = o2.get(dim);

                        if (enumDimDataDTOMap.containsKey(o1Value)
                            && enumDimDataDTOMap.containsKey(o2Value)) {

                            EnumDimDataDTO o1EnumDimDataDTO = enumDimDataDTOMap.get(o1Value);
                            EnumDimDataDTO o2EnumDimDataDTO = enumDimDataDTOMap.get(o2Value);

                            if (o1EnumDimDataDTO != null && o2EnumDimDataDTO != null) {
                                return (int)(
                                    (long)o1EnumDimDataDTO.getEnumId() - (long)o2EnumDimDataDTO.getEnumId());
                            }
                        }
                    }
                }
                return 0;
            }
        );

        return convertedData;
    }

    /**
     * 只查询有枚举值的标签
     *
     * @param dimensionMetaInfoMap
     * @return
     */
    private Map<String, Map<String, EnumDimDataDTO>> getEnumData(Map<String, LabelInfoDTO> dimensionMetaInfoMap) {

        Map<String, Map<String, EnumDimDataDTO>> map = Maps.newHashMap();
        if (dimensionMetaInfoMap != null && !dimensionMetaInfoMap.isEmpty()) {

            for (LabelInfoDTO labelInfoDTO : dimensionMetaInfoMap.values()) {

                if (enumDimMetaInfoService.hasEnumValue(labelInfoDTO.getDataType())) {
                    List<EnumDimDataDTO> enumValues = enumDimDataService.listByDimMetaId(
                        labelInfoDTO.getDimEnumMetaId());

                    if (CollectionUtils.isNotEmpty(enumValues)) {
                        Map<String, EnumDimDataDTO> enumValueMap = Maps.newHashMap();
                        enumValues.forEach(
                            enumDimDataDTO -> {
                                enumValueMap.put(enumDimDataDTO.getEnumCode(), enumDimDataDTO);
                            }
                        );
                        map.put(labelInfoDTO.getCode(), enumValueMap);
                    }
                }
            }
        }

        return map;
    }

    private Map<String, OlapQueryResultMeta> getMetaMap(OlapQueryResult result) {
        Map<String, OlapQueryResultMeta> map = Maps.newHashMap();
        result.getMeta().forEach(
            meta -> {
                map.put(meta.getId(), meta);
            }
        );

        return map;
    }

    private List<Map<String, Object>> mergeOlapQueryResultData(Map<Long, CrowdMetaInfoSummary> groupMetaInfoMap,
                                                               Map<String, Map<String, EnumDimDataDTO>> enumDataMap,
                                                               Map<String, OlapQueryResult> groupResultMap,
                                                               Map<Long, Long> userCntMap) {
        List<Map<String, Object>> data = Lists.newArrayList();

        groupResultMap.forEach(
            (group, result) -> {

                List<Map<String, Object>> oneGroupData = result.getData();
                Map<String, OlapQueryResultMeta> metasMap = getMetaMap(result);

                if (CollectionUtils.isNotEmpty(oneGroupData)) {
                    oneGroupData.stream()
                        // 过滤维度为空的行
                        .filter(
                            columns -> {
                                for (Map.Entry<String, Object> entry :
                                    columns.entrySet()) {
                                    String k = entry.getKey();
                                    Object v = entry.getValue();
                                    OlapQueryResultMeta meta = metasMap.get(k);
                                    if (meta.getIsDim() && v == null) {
                                        return false;
                                    }
                                }
                                return true;
                            })
                        .forEach(
                            columns -> {
                                Map<String, Object> newColumnMap = Maps.newHashMap();

                                // 转换标签枚举
                                columns.forEach(
                                    (k, v) -> {
                                        OlapQueryResultMeta meta = metasMap.get(k);
                                        if (meta.getIsDim()) {
                                            // 前端格式约定：中文/英文两个字段返回，英文在传参中使用，添加"_code"后缀
                                            newColumnMap.put(k + "_code", v);
                                            // 若为可转标签则转换标签枚举值
                                            if (enumDataMap.containsKey(k)) {
                                                Map<String, EnumDimDataDTO> enumValueMap = enumDataMap.get(k);
                                                if (enumValueMap.containsKey(v)) {
                                                    newColumnMap.put(k, enumValueMap.get(v).getEnumDesc());
                                                } else {
                                                    newColumnMap.put(k, v);
                                                }
                                            } else {
                                                newColumnMap.put(k, v);
                                            }
                                        } else {
                                            newColumnMap.put(k, v);
                                        }
                                    }
                                );

                                // 转换人群名称
                                if (!groupMetaInfoMap.containsKey(Long.valueOf(group))) {
                                    throw new RuntimeException("Not Found Crowd Meta! CrowdID:" + group);
                                }
                                CrowdMetaInfoSummary crowdMetaInfoSummary = groupMetaInfoMap.get(Long.valueOf(group));
                                newColumnMap.put(COLUMN_GROUP, crowdMetaInfoSummary.getName());

                                // 添加占比
                                if (userCntMap.containsKey(Long.valueOf(group))
                                    && columns.containsKey(OLAP_MEASURE_PICASSO_GROUP_USER_CNT)) {
                                    Long userCnt = userCntMap.get(Long.valueOf(group));
                                    Long dimGroupUserCnt = (Long)columns.get(OLAP_MEASURE_PICASSO_GROUP_USER_CNT);
                                    newColumnMap.put(COLUMN_RATE,
                                        Double.valueOf(
                                            NumberFormatUtils.divide((dimGroupUserCnt) + "", userCnt + "", 4)));
                                    data.add(newColumnMap);
                                }
                            }
                        );
                }
            }
        );

        return data;
    }

    /**
     * @param values
     * @param userCntMap
     * @return
     */
    private Map<String, OlapQueryResultMeta> mergeOlapQueryResultMetas(Collection<OlapQueryResult> values,
                                                                       Map<Long, Long> userCntMap) {
        // 去重
        Map<String, OlapQueryResultMeta> id2MetaMap = Maps.newHashMap();
        values.forEach(
            result -> {
                List<OlapQueryResultMeta> metas = result.getMeta();
                metas.forEach(
                    meta -> {
                        if (!id2MetaMap.containsKey(meta.getId())) {
                            id2MetaMap.put(meta.getId(), meta);
                        }
                    }
                );

            }
        );

        // 前端兼容：只有当多人群时才增加该Meta
        if (values.size() > 1) {
            // 增加"诸葛人群"维度
            id2MetaMap.put(COLUMN_GROUP, new OlapQueryResultMeta(COLUMN_GROUP, COLUMN_GROUP_NAME, true, false));
        }

        if (userCntMap != null && userCntMap.size() != 0) {
            OlapQueryResultMeta meta = new OlapQueryResultMeta(COLUMN_RATE, COLUMN_RATE_NAME, false, false);
            meta.setIsRate(true);
            id2MetaMap.put(COLUMN_RATE, meta);
        }

        return id2MetaMap;
    }

    private static final String LATEST_DATE_FORMAT = "yyyyMMdd";

    /**
     * @param dimensions
     * @return
     */
    private String getLatestDimensionDateTimeFormat(List<OlapPicassoDimension> dimensions) {

        Date min = new Date();
        for (OlapPicassoDimension dimension : dimensions) {
            if (DateUtils.truncatedCompareTo(min, dimension.getDataTime(), Calendar.DATE) < 0) {
                min = dimension.getDataTime();
            }
        }

        return DateFormatUtils.format(min, LATEST_DATE_FORMAT);
    }

    /**
     * @param dimensions
     * @return
     */
    private Map<String, LabelInfoDTO> getDimensionMetaInfo(List<String> dimensions) {

        Map<String, LabelInfoDTO> map = Maps.newHashMap();
        dimensions.forEach(
            dimension -> {
                LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(dimension);
                if (labelInfoDTO == null) {
                    throw new RuntimeException("Unknown LabelCode:" + dimension);
                }
                map.put(dimension, labelInfoDTO);
            }
        );

        return map;
    }
}


