package com.fliggy.picasso.analysis;

import com.fliggy.olap.client.domain.OlapQueryResultMeta;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2022/5/26 下午2:08
 */
@Data
public class AnalysisV2HandleResult {

    List<AnalysisV2Result> analysisV2ResultList;

    /**
     * id列表用逗号分开(给前端展示取值用)  示例:10002,10005
     */
    private String analysisIds;

    /**
     * 结果数据
     */
    private List<Map<String, Object>> data;

    /**
     * 前端柱状图用到的meta结构
     */
    private List<OlapQueryResultMeta> meta;

    /**
     * 关联父分析和当前分析的所有TagInfo信息，用来给前端创建人群使用
     */
    private List<AnalysisV2Meta.TagInfo> parentTagInfoList;

    /**
     * 下钻标签路径
     */
    private String nestedLablePath;
}
