package com.fliggy.picasso.analysis;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import com.fliggy.olap.client.domain.DimSource;
import com.fliggy.olap.client.domain.OlapQueryResult;
import com.fliggy.olap.client.domain.OlapQueryResultMeta;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.service.enumvalue.EnumDimDataDTO;
import com.fliggy.picasso.service.enumvalue.EnumDimDataService;
import com.fliggy.picasso.service.enumvalue.EnumDimMetaInfoService;
import com.fliggy.picasso.service.enumvalue.LabelEnumValueService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/3/25 上午11:36
 */
@Component
public class AnalysisMeasureQueryHandler
    implements AnalysisQueryHandler<AnalysisQueryParam, OlapQueryResult> {

    @Autowired
    private LabelEnumValueService labelEnumValueService;

    @Autowired
    private EnumDimDataService enumDimDataService;

    @Autowired
    private EnumDimMetaInfoService enumDimMetaInfoService;

    @Autowired
    private LabelInfoService labelInfoService;

    @Override
    public AnalysisQueryParam handleParam(AnalysisQueryParam param) {
        return param;
    }

    @Override
    public OlapQueryResult handleResult(AnalysisQueryResult result, AnalysisQueryParam analysisQueryParam) {
        // 指标分析暂时只支持单人群，不再做额外格式转换沿用OLAP返回结果集和前端对应
        Map<String, OlapQueryResult> group2ResultMap = result.getResultMap();

        // TODO 完善
        if (group2ResultMap.size() != 1) {
            throw new RuntimeException("Measure Query Need One Group!");
        }

        Collection<OlapQueryResult> values = group2ResultMap.values();
        OlapQueryResult olapQueryResult = values.iterator().next();

        Map<String, OlapQueryResultMeta> metasMap = getMetaMap(olapQueryResult);

        // 查询维度枚举值
        Map<String, Map<String, EnumDimDataDTO>> enumDataMap = Maps.newHashMap();
        List<OlapQueryResultMeta> metas = olapQueryResult.getMeta();
        metas.forEach(
            olapQueryResultMeta -> {
                Map<String, EnumDimDataDTO> enumDimDataMap = Maps.newHashMap();

                // 维度类型且为诸葛标签
                if (olapQueryResultMeta.getIsDim()
                    && DimSource.ZHUGE == olapQueryResultMeta.getDimSource()) {

                    String labelCode = olapQueryResultMeta.getId();
                    LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(labelCode);

                    if (labelInfoDTO != null && enumDimMetaInfoService.hasEnumValue(labelInfoDTO.getDataType())) {
                        List<EnumDimDataDTO> enumValues = enumDimDataService.listByDimMetaId(
                            labelInfoDTO.getDimEnumMetaId());

                        enumValues.forEach(enumDimDataDTO -> {
                            enumDimDataMap.put(enumDimDataDTO.getEnumCode(), enumDimDataDTO);
                        });
                        enumDataMap.put(labelCode, enumDimDataMap);
                    }
                }
            }
        );

        if (olapQueryResult.getData() != null) {
            List<Map<String, Object>> convertedData = olapQueryResult.getData().stream()
                // 过滤维度为空的行
                .filter(
                    columns -> {
                        for (Entry<String, Object> entry :
                            columns.entrySet()) {
                            String k = entry.getKey();
                            Object v = entry.getValue();
                            OlapQueryResultMeta meta = metasMap.get(k);
                            if (meta.getIsDim() && v == null) {
                                return false;
                            }
                        }
                        return true;
                    }).collect(Collectors.toList());

            for (Map<String, Object> row : convertedData) {

                Map<String, Object> updateColumns = Maps.newHashMap();
                row.forEach(
                    (key, value) -> {
                        // 若为维度列，则判断是否替换展示中文名
                        if (enumDataMap.containsKey(key) && enumDataMap.get(key).containsKey(value)) {
                            updateColumns.put(key, enumDataMap.get(key).get(value).getEnumDesc());
                        }
                    }
                );

                row.putAll(updateColumns);
            }
            olapQueryResult.setData(convertedData);
        }

        return olapQueryResult;
    }

    private Map<String, OlapQueryResultMeta> getMetaMap(OlapQueryResult result) {
        Map<String, OlapQueryResultMeta> map = Maps.newHashMap();
        result.getMeta().forEach(
            meta -> {
                map.put(meta.getId(), meta);
            }
        );

        return map;
    }

    /**
     * 和OLAP协议转换封装
     *
     * @param dimSource
     * @return
     */
    private int dimSourceCode(DimSource dimSource) {

        int code;
        switch (dimSource) {
            case OLAP: {
                code = 0;
                break;
            }
            case ZHUGE: {
                code = 1;
                break;
            }
            default: {
                throw new RuntimeException("Not Support DimSource!");
            }
        }

        return code;
    }
}


