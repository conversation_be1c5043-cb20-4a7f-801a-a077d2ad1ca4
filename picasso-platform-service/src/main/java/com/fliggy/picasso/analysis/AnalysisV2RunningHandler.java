package com.fliggy.picasso.analysis;

import com.alibaba.fastjson.JSON;
import com.aliyun.odps.Instance;
import com.fliggy.picasso.dao.AnalysisV2DO;
import com.fliggy.picasso.offline.OdpsService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 洞察分析-运行中的任务 处理器
 *
 * <AUTHOR>
 * Created on 2022/5/26 下午8:51
 */
@Component
public class AnalysisV2RunningHandler extends AnalysisV2AbstractHandler<AnalysisV2DO, AnalysisV2HandleResult, AnalysisV2StatusEnum> {

    @Resource
    private AnalysisV2AbstractHandler<AnalysisV2DO, AnalysisV2HandleResult, AnalysisV2StatusEnum> analysisV2FinishHandler;


    @Override
    protected AnalysisV2HandleResult doHandle(AnalysisV2DO analysisV2DO) {
        try {
            String extInfo = analysisV2DO.getExtInfo();
            // 如果是运行中, 则检查instants 状态
            Instance.TaskStatus.Status status = odpsService.checkInstanceStatus(getInstantId(extInfo));
            if (Objects.isNull(status)) {
                throw new RuntimeException("odps instant status is null!");
            }
            boolean instanceTaskNotFinished = OdpsService.isInstanceTaskNotFinished(status);
            if (instanceTaskNotFinished) {
                return null;
            }
            return analysisV2FinishHandler.handle(analysisV2DO);
        } catch (Exception e) {
            throw createException("AnalysisV2RunningHandler处理异常!", e, "analysisV2DO", JSON.toJSONString(analysisV2DO));
        }
    }

    @Override
    public AnalysisV2StatusEnum typeOrStatus() {
        return AnalysisV2StatusEnum.RUNNING;
    }
}
