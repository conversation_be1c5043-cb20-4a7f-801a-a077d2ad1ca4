package com.fliggy.picasso.analysis;

import com.fliggy.olap.client.domain.OlapQueryResult;
import com.fliggy.olap.client.domain.OlapQueryResultMeta;
import com.fliggy.picasso.analysis.AnalysisProfileQueryResult.DimensionVal;
import com.fliggy.picasso.analysis.AnalysisProfileQueryResult.GroupVal;
import com.fliggy.picasso.analysis.AnalysisProfileQueryResult.Measure;
import com.fliggy.picasso.analysis.AnalysisProfileQueryResult.SingleRow;
import com.fliggy.picasso.client.entity.CrowdMetaInfoSummary;
import com.fliggy.picasso.client.service.CrowdMetaInfoService;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/3/25 上午11:36
 */
@Component
public class AnalysisProfileQueryHandler
    implements AnalysisQueryHandler<AnalysisQueryParam, AnalysisProfileQueryResult> {

    @Autowired
    private LabelInfoService labelInfoService;

    @Autowired
    private CrowdMetaInfoService crowdMetaInfoService;

    @Override
    public AnalysisQueryParam handleParam(AnalysisQueryParam analysisProfileQueryParam) {
        return analysisProfileQueryParam;
    }

    @Override
    public AnalysisProfileQueryResult handleResult(AnalysisQueryResult result,
                                                   AnalysisQueryParam analysisQueryParam) {

        // 获取人群信息
        List<CrowdMetaInfoSummary> groups = analysisQueryParam.getGroups();
        Map<Long, CrowdMetaInfoSummary> groupMetaInfoMap = com.fliggy.picasso.common.utils.CollectionUtils.listToMap(
            groups, CrowdMetaInfoSummary::getId);

        // 获取标签信息
        List<String> dimensionCodes = analysisQueryParam.getDimensions();
        Map<String, LabelInfoDTO> dimensionMetaInfoMap = getDimensionMetaInfo(dimensionCodes);

        AnalysisProfileQueryResult analysisProfileQueryResult = new AnalysisProfileQueryResult();
        List<OlapPicassoDimension> dimensions = dimensionMetaInfoMap.values()
            .stream()
            .map(
                labelInfoDTO -> new OlapPicassoDimension(labelInfoDTO.getCode(), labelInfoDTO.getName(), labelInfoDTO.getSourceDataDs())
            )
            .collect(Collectors.toList());
        analysisProfileQueryResult.setDimensions(dimensions);
        analysisProfileQueryResult.setLatestDimensionDate(getLatestDimensionDateTimeFormat(dimensions));

        Map<String, OlapQueryResult> groupResultMap = result.getResultMap();

        List<SingleRow> results = convertResults(groupMetaInfoMap, dimensionMetaInfoMap, groupResultMap);
        analysisProfileQueryResult.setResults(results);

        return analysisProfileQueryResult;
    }

    /**
     * 结果数据集，封装排序等
     */
    public static class SingleRowDataSet {

        private Map<SingleRowSign, SingleRow> map = Maps.newHashMap();

        @Data
        @AllArgsConstructor
        private static class SingleRowSign {
            private Set<DimensionVal> dimensionValSet;
        }
    }

    /**
     * @param groupMetaInfoMap
     * @param groupResultMap
     * @return
     */
    private List<SingleRow> convertResults(Map<Long, CrowdMetaInfoSummary> groupMetaInfoMap,
                                           Map<String, LabelInfoDTO> dimensionMetaInfoMap,
                                           Map<String, OlapQueryResult> groupResultMap) {

        List<SingleRow> rows = Lists.newArrayList();
        groupResultMap.forEach(
            (group, olapQueryResult) -> {
                // 获取人群信息
                CrowdMetaInfoSummary crowdMetaInfoSummary = groupMetaInfoMap.get(group);

                // 构造元数据ID索引Map
                Map<String, OlapQueryResultMeta> id2MataMap = getOlapMetaMap(olapQueryResult);

                List<Map<String, Object>> data = olapQueryResult.getData();

                if (CollectionUtils.isNotEmpty(data)) {
                    // 遍历结果集
                    data.forEach(
                        columns -> {
                            SingleRow singleRow = new SingleRow();
                            singleRow.setGroup(
                                new GroupVal(crowdMetaInfoSummary.getId(), crowdMetaInfoSummary.getName(),
                                    crowdMetaInfoSummary.getCrowdAmount())
                            );

                            columns.forEach((k, v) -> {
                                if (!id2MataMap.containsKey(k)) {
                                    throw new RuntimeException("OLAP Result Error! Not Found Meta By Id:" + k);
                                }

                                OlapQueryResultMeta meta = id2MataMap.get(k);
                                if (meta.getIsDim()) {
                                    if (!dimensionMetaInfoMap.containsKey(k)) {
                                        throw new RuntimeException("OLAP Result Error! Return No Need Dimension:" + k);
                                    }
                                    String name = dimensionMetaInfoMap.get(k).getName();
                                    singleRow.addDimensionVal(new DimensionVal(name, v == null ? null : v.toString()));
                                } else {
                                    // 若是指标类型同时计算占比
                                    String name = meta.getName();
                                    singleRow.addMeasure(new Measure(k, name, (Number)v));
                                    long val = (Long)v / crowdMetaInfoSummary.getCrowdAmount();
                                    singleRow.addMeasure(new Measure("rate", "占比", val));
                                }
                            });

                            rows.add(singleRow);
                        }
                    );
                }
            }
        );

        return rows;
    }

    /**
     * @param olapQueryResult
     * @return
     */
    private Map<String, OlapQueryResultMeta> getOlapMetaMap(OlapQueryResult olapQueryResult) {
        List<OlapQueryResultMeta> metas = olapQueryResult.getMeta();
        // 构造Metas的ID索引Map
        Map<String, List<OlapQueryResultMeta>> idGroupingMap = metas.stream()
            .collect(Collectors.groupingBy(OlapQueryResultMeta::getId));

        Map<String, OlapQueryResultMeta> id2MataMap = Maps.newHashMap();
        idGroupingMap.forEach(
            (id, list) -> {
                if (list.size() != 1) {
                    throw new RuntimeException("OLAP Result Error! Meta Repetition By Id:" + id);
                }

                id2MataMap.put(id, list.get(0));
            }
        );
        return id2MataMap;
    }

    private static final String LATEST_DATE_FORMAT = "yyyyMMdd";

    /**
     * @param dimensions
     * @return
     */
    private String getLatestDimensionDateTimeFormat(List<OlapPicassoDimension> dimensions) {

        Date min = new Date();
        for (OlapPicassoDimension dimension : dimensions) {
            if (DateUtils.truncatedCompareTo(min, dimension.getDataTime(), Calendar.DATE) < 0) {
                min = dimension.getDataTime();
            }
        }

        return DateFormatUtils.format(min, LATEST_DATE_FORMAT);
    }

    /**
     * @param dimensions
     * @return
     */
    private Map<String, LabelInfoDTO> getDimensionMetaInfo(List<String> dimensions) {

        Map<String, LabelInfoDTO> map = Maps.newHashMap();
        dimensions.forEach(
            dimension -> {
                LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(dimension);
                if (labelInfoDTO == null) {
                    throw new RuntimeException("Unknown LabelCode:" + dimension);
                }
                map.put(dimension, labelInfoDTO);
            }
        );

        return map;
    }
}


