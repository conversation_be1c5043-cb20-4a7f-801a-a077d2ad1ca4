package com.fliggy.picasso.analysis;

import com.alibaba.fastjson.JSON;

import com.fliggy.picasso.client.entity.analysis.AnalysisMetaDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Created on 2022/5/26 下午3:18
 */
@Data
public class AnalysisV2Meta implements Serializable {

    /**
     * 要分析的画像标签列表
     */
    private List<TagInfo> tagInfoList;

    /**
     * 排序方式 默认DESC
     */
    private String orderBy = "DESC";

    /**
     * limit count 默认100
     */
    private Integer limitCount = 100;

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class TagInfo {
        /**
         * 画像标签code
         */
        private String name;

        /**
         * 画像标签值
         */
        private String value;

        /**
         * 画像标签中文名
         */
        private String cnName;
    }

    public static AnalysisV2Meta convertFromDTO(AnalysisMetaDTO analysisMetaDTO) {
        if (analysisMetaDTO == null) {
            return null;
        }

        AnalysisV2Meta result = new AnalysisV2Meta();
        BeanUtils.copyProperties(analysisMetaDTO, result);
        if (CollectionUtils.isNotEmpty(analysisMetaDTO.getTagInfoList())) {
            List<TagInfo> tagInfos = new ArrayList<>();
            for (AnalysisMetaDTO.TagInfo tagInfo : analysisMetaDTO.getTagInfoList()) {
                tagInfos.add(convertTagInfo(tagInfo));
            }
            result.setTagInfoList(tagInfos);
        }
        return result;
    }

    private static TagInfo convertTagInfo(AnalysisMetaDTO.TagInfo tagInfo) {
        TagInfo result = new TagInfo();
        BeanUtils.copyProperties(tagInfo, result);
        return result;
    }
}
