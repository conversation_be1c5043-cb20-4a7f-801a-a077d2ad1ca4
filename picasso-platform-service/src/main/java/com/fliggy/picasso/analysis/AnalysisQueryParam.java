package com.fliggy.picasso.analysis;

import com.fliggy.picasso.client.entity.CrowdMetaInfoSummary;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/24 下午5:27
 */
@Data
public class AnalysisQueryParam {

    /**
     * 人群ID列表，可能多个
     */
    private List<CrowdMetaInfoSummary> groups;

    /**
     * 分组维度
     */
    private List<String> dimensions;

    /**
     * 查询指标
     */
    private List<String> measures;

    /**
     * 诸葛分析中各个过滤条件为AND的链接关系，这里用列表存储
     */
    private List<Filter> filters;

    private Date start;
    private Date end;

    private int limit = 100;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Filter {
        private String key;
        private Operator operator;
        private String value;
    }

    public enum Operator {

        /**
         *
         */
        EQUAL("="),

        ;

        Operator(String code) {
            this.code = code;
        }

        @Getter
        private final String code;

        private static final Map<String, Operator> CODE_MAP = Maps.newHashMap();

        static {
            for (Operator value : values()) {
                CODE_MAP.put(value.getCode(), value);
            }
        }

        public static Operator fromCode(String code) {
            return CODE_MAP.getOrDefault(code, null);
        }

    }

    public static final class AnalysisQueryParamBuilder {

        private final List<CrowdMetaInfoSummary> group = Lists.newArrayList();
        private final List<String> dimensions = Lists.newArrayList();
        private final List<String> measures = Lists.newArrayList();
        private final List<Filter> filters = Lists.newArrayList();
        private Date start;
        private Date end;

        private AnalysisQueryParamBuilder() {
        }

        public static AnalysisQueryParamBuilder anAnalysisQueryParam() {
            return new AnalysisQueryParamBuilder();
        }

        public AnalysisQueryParamBuilder addGroup(CrowdMetaInfoSummary group) {
            this.group.add(group);
            return this;
        }

        public AnalysisQueryParamBuilder addDimension(String dimension) {
            this.dimensions.add(dimension);
            return this;
        }

        public AnalysisQueryParamBuilder addMeasure(String measure) {
            this.measures.add(measure);
            return this;
        }

        public AnalysisQueryParamBuilder addFilter(Filter filter) {
            this.filters.add(filter);
            return this;
        }

        public AnalysisQueryParamBuilder addFilter(
                String key,
                Operator operator,
                String value
        ) {
            this.filters.add(new Filter(key, operator, value));
            return this;
        }

        public AnalysisQueryParamBuilder start(Date start) {
            this.start = start;
            return this;
        }

        public AnalysisQueryParamBuilder end(Date end) {
            this.end = end;
            return this;
        }

        public AnalysisQueryParam build() {
            AnalysisQueryParam analysisQueryParam = new AnalysisQueryParam();
            analysisQueryParam.setGroups(group);
            analysisQueryParam.setDimensions(dimensions);
            analysisQueryParam.setMeasures(measures);
            analysisQueryParam.setFilters(filters);
            analysisQueryParam.setStart(start);
            analysisQueryParam.setEnd(end);
            return analysisQueryParam;
        }
    }
}
