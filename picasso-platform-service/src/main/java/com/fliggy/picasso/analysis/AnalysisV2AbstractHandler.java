package com.fliggy.picasso.analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.metrics.StringUtils;
import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.common.odps.OdpsTaskNameGenerator;
import com.fliggy.picasso.exception.AnalysisException;
import com.fliggy.picasso.freemarker.TemplateReader;
import com.fliggy.picasso.offline.OdpsService;
import com.fliggy.picasso.service.analysis.AnalysisV2Service;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 洞察解析v2 处理器
 *
 * <AUTHOR>
 * Created on 2022/5/24 下午3:42
 */
@Slf4j
public abstract class AnalysisV2AbstractHandler<P, R, E> implements IAnalysisV2Handler<P, R, E> {

    @Resource
    protected AnalysisV2Service analysisV2Service;

    @Resource
    protected TemplateReader templateReader;

    @Resource
    protected OdpsService odpsService;


    @Override
    public R handle(P param) {
        try {
            // do check
            before(param);
            // do business
            R result = doHandle(param);
            // do result
            after(param, result);
            return result;
        } catch (Exception e) {
            log.error("AnalysisV2AbstractHandler error. param: {}", JSON.toJSON(param), e);
            return doException(param, e);
        }
    }

    private R doException(P param, Exception e) {
        return null;
    }

    protected abstract R doHandle(P p);

    protected void after(P p, R result) {
        log.info("{} 出参:{}", this.getClass().getSimpleName(), JSON.toJSONString(result));
    }

    protected void before(P p) {
        Preconditions.checkArgument(Objects.nonNull(p), "参数配置不能为空");
        log.info("{} 入参:{}", this.getClass().getSimpleName(), JSON.toJSONString(p));
    }

    protected AnalysisException createException(String msg, Exception e, String key, String value) {
        if (e instanceof AnalysisException){
            AnalysisException analysisException = new AnalysisException((AnalysisException)e, key, value);
            analysisException.setMsg(msg);
            return analysisException;
        }
        AnalysisException analysisException = new AnalysisException(msg, e);
        analysisException.setExtMsg(key, value);
        analysisException.setMsg(e.getMessage());
        return analysisException;
    }

    protected String getInstantId(String extInfo) {
        JSONObject jsonObject = JSON.parseObject(extInfo);
        String string = jsonObject.getString(OdpsConstants.ODPS_INSTANT_ID);
        if (StringUtils.isBlank(string)) {
            throw createException("extInfo instantId is blank!", null, "extInfo", extInfo);
        }
        return string;
    }


    protected String getTaskName(String extInfo) {
        JSONObject jsonObject = JSON.parseObject(extInfo);
        String string = jsonObject.getString(OdpsConstants.ODPS_INSTANT_TASK_NAME);
        if (StringUtils.isBlank(string)) {
            throw createException("extInfo instantTaskName is blank!", null, "extInfo", extInfo);
        }
        return string;
    }

    protected String getOdpsLogView(String extInfo) {
        JSONObject jsonObject = JSON.parseObject(extInfo);
        String string = jsonObject.getString(OdpsConstants.ODPS_LOG_VIEW);
        if (StringUtils.isBlank(string)) {
            throw createException("extInfo odpsLogView is blank!", null, "extInfo", extInfo);
        }
        return string;
    }


    protected JSONObject getExtInfoObj(String extInfo) {
        JSONObject jsonObject = JSON.parseObject(extInfo);
        if (Objects.isNull(jsonObject)) {
            throw createException("extInfo odpsLogView is blank!", null, "extInfo", extInfo);
        }
        return jsonObject;
    }
}
