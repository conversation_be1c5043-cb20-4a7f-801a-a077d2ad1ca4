package com.fliggy.picasso.analysis;

/**
 * <AUTHOR>
 * @date 2021/3/25 上午11:39
 */
public class AnalysisQueryEngine<T, K> {

    private final AnalysisQueryHandler<T, K> handler;
    private final AnalysisExecutor executor;

    public AnalysisQueryEngine(AnalysisQueryHandler<T, K> handler, AnalysisExecutor executor) {
        this.handler = handler;
        this.executor = executor;
    }

    public K query(T t) {

        AnalysisQueryParam analysisQueryParam = handler.handleParam(t);

        AnalysisQueryResult result = executor.query(analysisQueryParam);

        K res = handler.handleResult(result, t);

        return res;
    }
}
