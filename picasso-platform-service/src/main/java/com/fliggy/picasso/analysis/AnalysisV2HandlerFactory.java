package com.fliggy.picasso.analysis;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.exception.AnalysisException;
import com.fliggy.picasso.utils.PicassoSpringContextUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2022/5/26 下午9:02
 */
@Component
@DependsOn("picassoSpringContextUtil")
public class AnalysisV2HandlerFactory{

    private static Map<Object, IAnalysisV2Handler> analysisV2HandlerMap;

    @PostConstruct
    public void init() throws BeansException {
        Map<String, IAnalysisV2Handler> map = PicassoSpringContextUtils.getApplicationContext().getBeansOfType(IAnalysisV2Handler.class);
        analysisV2HandlerMap = new HashMap<>();
        map.forEach((key, value) ->{
            if (analysisV2HandlerMap.containsKey(value.typeOrStatus())){
                throw new AnalysisException("AnalysisV2Handler 类型有重复:" + JSON.toJSONString(value), null);
            }
            AnalysisV2StatusEnum analysisV2StatusEnum = (AnalysisV2StatusEnum) value.typeOrStatus();
            analysisV2HandlerMap.put(analysisV2StatusEnum, value);
        });
    }

    public static IAnalysisV2Handler getIAnalysisV2Handler(AnalysisV2StatusEnum analysisV2StatusEnum) {
        return analysisV2HandlerMap.get(analysisV2StatusEnum);
    }

}
