package com.fliggy.picasso.analysis;

import com.fliggy.picasso.dao.AnalysisV2DO;
import com.fliggy.picasso.freemarker.TemplateEnum;
import com.fliggy.picasso.freemarker.TemplateParam;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2022/5/26 下午2:15
 */
@Data
@Builder
public class AnalysisV2HandleParam {

    /**
     * 洞察分析DO
     */
    private AnalysisV2DO analysisV2DO;

    /**
     * 模板参数
     */
    private TemplateParam templateParam;

    /**
     * 模板路径
     */
    private TemplateEnum templateEnum;

}
