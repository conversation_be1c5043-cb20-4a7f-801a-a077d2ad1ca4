package com.fliggy.picasso.analysis;

import com.fliggy.picasso.entity.template.OdpsAnalysisChildSelectParam;
import com.fliggy.picasso.entity.template.OdpsAnalysisParentSelectParam;
import com.fliggy.picasso.freemarker.TemplateEnum;
import com.google.common.base.Preconditions;
import freemarker.template.Configuration;
import freemarker.template.DefaultObjectWrapper;
import freemarker.template.Template;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 洞察-父分析
 *
 * <AUTHOR>
 * Created on 2022/5/24 下午3:46
 */
@Service
public class AnalysisTest {

    public static void main(String[] args) {
        try {

            Configuration cfg = new Configuration(Configuration.VERSION_2_3_23);
            cfg.setDirectoryForTemplateLoading(new File("/Users/<USER>/IdeaProjects/picasso-platform/picasso-platform-service/src/main/resources/templates"));
            cfg.setObjectWrapper(new DefaultObjectWrapper(Configuration.VERSION_2_3_23));
            OdpsAnalysisParentSelectParam odpsParam = OdpsAnalysisParentSelectParam.builder()
                    .tagNames("eight_crowd,is_parenting")
                    .crowdId("78800")
                    .limitCount("100")
                    .sc("DESC")
                    .build();
            Template template = cfg.getTemplate(TemplateEnum.ODPS_ANALYSIS_PARENT_SELECT.getFileName());
            String parentSql = FreeMarkerTemplateUtils.processTemplateIntoString(template, odpsParam);
            System.out.println(parentSql);
            System.out.println("---------------------------------------------------------------------------------");

            List<AnalysisV2Meta.TagInfo> list =  new ArrayList<>();
            AnalysisV2Meta.TagInfo tagInfo = new AnalysisV2Meta.TagInfo("eight_crowd", "04_资深中产", "");
//            OdpsAnalysisChildSelectParam.TagInfo tagInfo2 = new OdpsAnalysisChildSelectParam.TagInfo("eight_crowd", "04_资深中产");
            list.add(tagInfo);
            OdpsAnalysisChildSelectParam childParam = OdpsAnalysisChildSelectParam.builder()
                    .childTagNames("is_parenting")
                    .crowdId("78800")
                    .limitCount("100")
                    .sc("DESC")
                    .parentTagList(list)
                    .build();
            Template template2 = cfg.getTemplate(TemplateEnum.ODPS_ANALYSIS_CHILD_SELECT.getFileName());
            String childSql = FreeMarkerTemplateUtils.processTemplateIntoString(template2, childParam);
            System.out.println(childSql);

        }catch (Exception e){
            e.printStackTrace();
        }
    }

    protected void before(AnalysisV2HandleParam analysisV2HandleParam) {
        Preconditions.checkArgument(Objects.nonNull(analysisV2HandleParam), "analysisV2HandleParam不能为空");
        Preconditions.checkArgument(Objects.nonNull(analysisV2HandleParam.getAnalysisV2DO()), "analysisV2DO不能为空");
        Preconditions.checkArgument(Objects.nonNull(analysisV2HandleParam.getTemplateEnum()), "templateEnum不能为空");
        Preconditions.checkArgument(Objects.nonNull(analysisV2HandleParam.getTemplateParam()), "templateParam不能为空");
    }
}
