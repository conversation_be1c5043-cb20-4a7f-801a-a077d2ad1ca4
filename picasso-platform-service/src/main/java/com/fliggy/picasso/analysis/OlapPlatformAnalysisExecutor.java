package com.fliggy.picasso.analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fliggy.olap.client.domain.ConditionParam;
import com.fliggy.olap.client.domain.OlapQueryParam;
import com.fliggy.olap.client.domain.OlapQueryResult;
import com.fliggy.olap.client.domain.TimeRange;
import com.fliggy.olap.client.service.OlapQueryService;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.eagleeye.EagleEye;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.fliggy.picasso.common.Constant.SERVICE_LOG;

/**
 * 多人群查询在OLAP服务侧未支持，在此封装一层以AnalysisQueryParam为标准的查询
 *
 * <AUTHOR>
 * @date 2021/3/24 下午5:47
 */
@Component
public class OlapPlatformAnalysisExecutor implements AnalysisExecutor {

    private static final Logger LOG = LoggerFactory.getLogger(SERVICE_LOG);

    @Autowired
    private OlapQueryService olapQueryService;

    @Override
    public AnalysisQueryResult query(AnalysisQueryParam param) {

        // (group -> OlapQueryParam)
        Map<String, OlapQueryParam> group2OlapQueryParamMap = toOlapQueryParam(param);

        Map<String, OlapQueryResult> group2OlapQueryResult = Maps.newHashMap();
        try {
            // TODO 并行改造
            group2OlapQueryParamMap.forEach(
                (group, olapQueryParam) -> {
                    OlapQueryResult olapQueryResult = olapQueryService.query(olapQueryParam);

                    LOG.info("Query Olap: TraceId:" + EagleEye.getTraceId() + " Param:" +
                        JSON.toJSONString(olapQueryParam, SerializerFeature.WriteMapNullValue) + "; result:" +
                        JSON.toJSONString(olapQueryResult, SerializerFeature.WriteMapNullValue));
                    // 检查返回结果
                    checkOlapQueryResult(olapQueryResult);

                    group2OlapQueryResult.put(group, olapQueryResult);
                }
            );

        } catch (Exception e) {
            LOG.error("Execute Olap Query Error!", e);
            throw new RuntimeException("Execute Olap Query Error!", e);
        }

        return toAnalysisQueryResult(group2OlapQueryResult);
    }

    public static final int QUERY_LIMIT = 100;
    public static final String OLAP_DIM_DS = "ds";
    public static final String OLAP_CONDITION_CONCAT_AND = "AND";
    public static final String OLAP_DIM_DS_FORMAT = "yyyyMMdd";

    private Map<String, OlapQueryParam> toOlapQueryParam(AnalysisQueryParam param) {

        if (CollectionUtils.isEmpty(param.getGroups())) {
            throw new ParamErrorException("Group In AnalysisQueryParam Can't Be Empty!");
        }

        Map<String, OlapQueryParam> group2ParamMap = Maps.newHashMap();
        param.getGroups().forEach(
            group -> {
                OlapQueryParam olapQueryParam = buildOlapQueryParam(group.getId().toString(), param);
                group2ParamMap.put(group.getId().toString(), olapQueryParam);
            }
        );

        return group2ParamMap;
    }

    /**
     * @param group 人群ID
     * @param param 标准分析查询参数
     */
    private OlapQueryParam buildOlapQueryParam(String group, AnalysisQueryParam param) {
        OlapQueryParam olapQueryParam = new OlapQueryParam();
        olapQueryParam.setPicassoGroup(group);
        olapQueryParam.setMeasures(param.getMeasures());
        olapQueryParam.setDimensions(param.getDimensions());
//        OrderParam orderParam = new OrderParam();
//        orderParam.setKey(OLAP_MEASURE_PICASSO_GROUP_USER_CNT);
//        orderParam.setSort("DESC");
//        olapQueryParam.setOrders(Collections.singletonList(orderParam));

        if (CollectionUtils.isNotEmpty(param.getFilters())) {
            List<ConditionParam> children = Lists.newArrayList();
            param.getFilters().forEach(
                filter -> {
                    ConditionParam condition = new ConditionParam();
                    condition.setKey(filter.getKey());
                    condition.setOperator(filter.getOperator().getCode());
                    condition.setValue(filter.getValue());
                    condition.setMeasure(false);
                    children.add(condition);
                }
            );
            ConditionParam condition = new ConditionParam();
            condition.setConcat(OLAP_CONDITION_CONCAT_AND);
            condition.setChildren(children);
            olapQueryParam.setFilter(condition);
        }

        // 查询人数指标无需传TimeRange
        if (param.getStart() != null && param.getEnd() != null) {
            TimeRange timeRange = new TimeRange();
            String start = DateFormatUtils.format(param.getStart(), OLAP_DIM_DS_FORMAT);
            String end = DateFormatUtils.format(param.getEnd(), OLAP_DIM_DS_FORMAT);
            timeRange.setDim(OLAP_DIM_DS);
            timeRange.setStart(start);
            timeRange.setEnd(end);
            olapQueryParam.setTimeRange(timeRange);
        }

        olapQueryParam.setLimit(param.getLimit());

        return olapQueryParam;
    }

    /**
     * 转换多人群OLAP查询结果
     *
     * @param olapQueryResult
     * @return
     */
    private AnalysisQueryResult toAnalysisQueryResult(Map<String, OlapQueryResult> olapQueryResult) {
        AnalysisQueryResult analysisQueryResult = new AnalysisQueryResult();

        olapQueryResult.forEach(
            (group, result) -> {
                analysisQueryResult.addOlapQueryResult(group, result);
            }
        );

        return analysisQueryResult;
    }

    /**
     * @param olapQueryResult
     */
    private void checkOlapQueryResult(OlapQueryResult olapQueryResult) {

    }
}
