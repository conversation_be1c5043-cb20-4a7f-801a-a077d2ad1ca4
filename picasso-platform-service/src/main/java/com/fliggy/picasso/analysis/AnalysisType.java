package com.fliggy.picasso.analysis;

import java.util.Map;

import com.fliggy.picasso.service.analysis.AnalysisStatus;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/3/24 下午9:51
 */
@AllArgsConstructor
public enum AnalysisType {

    /**
     * 画像分析
     */
    PROFILE("PROFILE"),

    /**
     * 指标分析
     */
    MEASURE("MEASURE");

    @Getter
    private String name;


    private static final Map<String, AnalysisType> CODE_MAP = Maps.newHashMap();

    static {
        for (AnalysisType value : values()) {
            CODE_MAP.put(value.getName(), value);
        }
    }

    public static AnalysisType fromCode(String code) {
        return CODE_MAP.getOrDefault(code, null);
    }

}
