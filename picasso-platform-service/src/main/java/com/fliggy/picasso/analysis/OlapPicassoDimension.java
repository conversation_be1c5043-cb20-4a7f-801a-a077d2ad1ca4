package com.fliggy.picasso.analysis;

import java.util.Date;

import lombok.Getter;

/**
 * 相比OlapDimension增加了数据时间属性
 *
 * <AUTHOR>
 * @date 2021/3/29 下午5:46
 */
public class OlapPicassoDimension extends OlapDimension {

    @Getter
    private Date dataTime;

    public OlapPicassoDimension(String code, String name, Date dataTime) {
        super(code, name);
        this.dataTime = dataTime;
    }
}
