package com.fliggy.picasso.analysis;

import com.alibaba.fastjson.JSON;

import com.aliyun.odps.Column;
import com.aliyun.odps.Instance;
import com.aliyun.odps.Instance.TaskStatus.Status;
import com.aliyun.odps.data.Record;
import com.fliggy.olap.client.domain.OlapQueryResultMeta;
import com.fliggy.picasso.alarm.dingtalk.DingDingNotifySender;
import com.fliggy.picasso.analysis.AnalysisV2Meta.TagInfo;
import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.enums.group.GroupStatusEnum;
import com.fliggy.picasso.dao.AnalysisV2DO;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.exception.AnalysisException;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.fliggy.picasso.service.enumvalue.EnumDimDataDTO;
import com.fliggy.picasso.service.enumvalue.EnumDimDataService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.utils.AnalysisUtils;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.util.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.fliggy.picasso.analysis.AnalysisConstants.*;
import static com.fliggy.picasso.common.Constant.LINE_FEED;

/**
 * <AUTHOR>
 * Created on 2022/5/26 下午8:51
 */
@Component
public class AnalysisV2FinishHandler extends AnalysisV2AbstractHandler<AnalysisV2DO, AnalysisV2HandleResult, AnalysisV2StatusEnum> {

    private static final Logger log = LoggerFactory.getLogger(AnalysisV2FinishHandler.class);

    @Resource
    private DingDingNotifySender dingDingNotifySender;

    @Resource
    private CrowdService crowdService;

    @Resource
    private LabelInfoService labelInfoService;

    @Resource
    private EnumDimDataService enumDimDataService;

    @Switch(description = "允许洞察分析ODPS任务失败次数")
    public int allowAnalysisFailTimes = 3;

    @Override
    protected AnalysisV2HandleResult doHandle(AnalysisV2DO analysisV2DO) {
        // 分析任务状态检查
        if (AnalysisV2StatusEnum.FINISH.getStatus().equals(analysisV2DO.getStatus())) {
            return JSON.parseObject(analysisV2DO.getResult(), AnalysisV2HandleResult.class);
        }

        // odps执行状态检查
        String extInfo = analysisV2DO.getExtInfo();
        Instance.TaskStatus.Status status = odpsService.checkInstanceStatus(getInstantId(extInfo));
        if (!Instance.TaskStatus.Status.SUCCESS.equals(status)) {
            if (!Status.WAITING.equals(status) && !Status.RUNNING.equals(status)) {
                failCountIncrement(analysisV2DO);
            }
            return null;
        }

        // 人群状态预校验
        CrowdMetaInfoDO crowdMetaInfoDO = crowdService.queryById(analysisV2DO.getCrowdId());
        if (Objects.isNull(crowdMetaInfoDO)) {
            throw createException("人群信息不存在", null, "crowdId", String.valueOf(analysisV2DO.getCrowdId()));
        }

        // 人群状态校验：只有 SUCCESS 状态的人群才能进行分析
        if (!GroupStatusEnum.SUCCESS.equals(crowdMetaInfoDO.getCrowdStatus())) {
            log.warn("人群还在构建中，暂时无法进行分析. crowdId: {}, status: {}", 
                     analysisV2DO.getCrowdId(), crowdMetaInfoDO.getCrowdStatus().getDesc());
            return null; // 返回 null，等待下次重试
        }

        // 解析标签、枚举值信息
        Map<String, Map<String, String>> enumDataMap = getEnumCodeAndDescMap(AnalysisUtils.getAnalysisTagList(analysisV2DO));
        AnalysisV2Meta analysisV2Meta = JSON.parseObject(analysisV2DO.getMeta(), AnalysisV2Meta.class);
        Map<String, TagInfo> tagInfoMap = analysisV2Meta.getTagInfoList().stream()
                .filter(r -> StringUtils.isNotBlank(r.getName()))
                .collect(Collectors.toMap(TagInfo::getName, r -> r));

        try {
            // odps执行结果解析
            List<Record> asyncExecuteResult = odpsService.getAsyncExecuteResult(getInstantId(extInfo), getTaskName(extInfo));
            if (CollectionUtils.isEmpty(asyncExecuteResult)) {
                throw new RuntimeException("odps record list is empty!" + extInfo);
            }
            List<Map<String, Object>> odpsParseResult = parseOdpsResult(asyncExecuteResult, analysisV2DO, enumDataMap);
            if (CollectionUtils.isEmpty(odpsParseResult)) {
                throw new AnalysisException("AnalysisV2HandleResult.doHandle failed, empty result", null);
            }

            // 结果处理
            AnalysisV2HandleResult analysisV2HandleResult = new AnalysisV2HandleResult();
            analysisV2HandleResult.setData(odpsParseResult);
            // 追加父分析id和name路径（前端展示下钻路径使用）
            List<AnalysisV2Meta.TagInfo> parentTagInfoList = new ArrayList<>();
            StringBuilder namePath = new StringBuilder();
            getParentTagInfoList(analysisV2DO.getId(), parentTagInfoList, namePath);
            // namePath 加上当前的标签名
            addCurLabelName(namePath, tagInfoMap);
            analysisV2HandleResult.setParentTagInfoList(parentTagInfoList);
            analysisV2HandleResult.setNestedLablePath(namePath.toString());
            // 追加分析路径

            // 构建前端要的dim数据
            List<OlapQueryResultMeta> meta = buildQueryResultMeta(tagInfoMap);
            analysisV2HandleResult.setMeta(meta);
            analysisV2DO.setResult(JSON.toJSONString(analysisV2HandleResult));
            analysisV2DO.setStatus(AnalysisV2StatusEnum.FINISH.getStatus());
            analysisV2Service.updateSelective(analysisV2DO, AnalysisV2StatusEnum.RUNNING.getStatus());
            return analysisV2HandleResult;

        } catch (Exception e) {
            failCountIncrement(analysisV2DO);
            throw createException("AnalysisV2FinishHandler处理异常!", e, "analysisV2DO", JSON.toJSONString(analysisV2DO));
        }
    }

    /**
     * 解析odps分析结果
     *
     * @param odpsRecords  odps分析结果
     * @param analysisV2DO 分析任务
     * @param dimEnumMap   标签枚举值
     * @return 分析结果
     */
    private List<Map<String, Object>> parseOdpsResult(List<Record> odpsRecords, AnalysisV2DO analysisV2DO, Map<String, Map<String, String>> dimEnumMap) {
        if (CollectionUtils.isEmpty(odpsRecords) || Objects.isNull(analysisV2DO)) {
            throw new IllegalArgumentException("AnalysisV2FinishHandler.parseOdpsResult failed, invalid params.");
        }

        CrowdMetaInfoDO crowdMetaInfoDO = crowdService.queryById(analysisV2DO.getCrowdId());
        if (Objects.isNull(crowdMetaInfoDO)) {
            throw new RuntimeException("人群信息查询失败!" + crowdMetaInfoDO);
        }

        List<Map<String, Object>> list = new ArrayList<>();
        // 遍历 tagInfoMap 从record里拿值
        for (Record record : odpsRecords) {
            try {
                Map<String, Object> singleData = parseSingleRecord(record, analysisV2DO, crowdMetaInfoDO, dimEnumMap);
                if (MapUtils.isNotEmpty(singleData)) {
                    list.add(singleData);
                }
            } catch (Exception e) {
                // do nothing
            }
        }

        return list;
    }

    /**
     * 解析单行结果
     *
     * @param odpsRecord      odps单行结果
     * @param analysisV2DO    分析任务
     * @param crowdMetaInfoDO 人群信息
     * @param dimEnumMap      标签枚举值
     * @return 解析结果
     */
    private Map<String, Object> parseSingleRecord(Record odpsRecord, AnalysisV2DO analysisV2DO, CrowdMetaInfoDO crowdMetaInfoDO
            , Map<String, Map<String, String>> dimEnumMap) {

        List<String> tagList = AnalysisUtils.getAnalysisTagList(analysisV2DO);
        boolean isBatchAnalysis = AnalysisUtils.isBatchAnalysis(analysisV2DO);
        String groupName = generateGroupName(crowdMetaInfoDO);
        Map<String, Object> result = Maps.newHashMap();
        if (Objects.isNull(odpsRecord) || CollectionUtils.isEmpty(tagList) || StringUtils.isBlank(groupName)) {
            return result;
        }
        if (!isCurrentAnalysisRecord(odpsRecord, tagList, isBatchAnalysis)) {
            return result;
        }

        result.put(COLUMN_GROUP, groupName);
        for (String tagName : tagList) {
            String tagValue = odpsRecord.getString(tagName);
            result.put(tagName, convertTagValue(tagName, tagValue, dimEnumMap));
        }

        long userCnt = 0L;
        try {
            userCnt = Long.parseLong(odpsRecord.getString(COLUMN_ANALYSIS_COUNT));
        } catch (Exception e) {
            // do nothing
        }
        result.put(OLAP_MEASURE_PICASSO_GROUP_USER_CNT, userCnt);

        // 查询人群数量并计算占比
        if (Objects.nonNull(crowdMetaInfoDO.getCrowdAmount()) && crowdMetaInfoDO.getCrowdAmount() != 0) {
            BigDecimal rateBd = BigDecimal.valueOf(userCnt)
                    .divide(BigDecimal.valueOf(crowdMetaInfoDO.getCrowdAmount()), 5, BigDecimal.ROUND_DOWN);
            result.put(COLUMN_RATE, Double.parseDouble(rateBd.toString()));
        }

        return result;
    }

    private String generateGroupName(CrowdMetaInfoDO crowdMetaInfoDO) {
        if (Objects.isNull(crowdMetaInfoDO)) {
            return null;
        }
        return String.format("%s-%s", crowdMetaInfoDO.getId(), crowdMetaInfoDO.getCrowdName());
    }

    /**
     * 判断是否为当前分析的列
     * 批量分析时，只有部分行属于当前你分析
     *
     * @param odpsRecord      odps数据
     * @param tagList
     * @param isBatchAnalysis
     * @return
     */
    private boolean isCurrentAnalysisRecord(Record odpsRecord, List<String> tagList, boolean isBatchAnalysis) {
        if (Objects.isNull(odpsRecord) || CollectionUtils.isEmpty(tagList)) {
            return false;
        }
        if (!isBatchAnalysis) {
            return true;
        }

        // 批量分析，只取当前tag不为空，且其他tag均为空的列
        for (Column column : odpsRecord.getColumns()) {
            String columnName = column.getName();
            // 跳过统计列
            if (COLUMN_ANALYSIS_COUNT.equalsIgnoreCase(columnName)) {
                continue;
            }

            // 批量分析时，必须该分组所有列值不为空，其他均为空
            String tagValue = odpsRecord.getString(columnName);
            boolean containsTag = tagList.stream().anyMatch(columnName::equalsIgnoreCase);
            if (containsTag) {
                if (OdpsConstants.NULL.equals(tagValue)) {
                    return false;
                }
            } else {
                if (!OdpsConstants.NULL.equals(tagValue)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 通过标签code(如果是枚举类型)获取枚举code和枚举描述的映射map
     *
     * @param tagList 标签列表
     * @return 标签下枚举值信息
     */
    private Map<String, Map<String, String>> getEnumCodeAndDescMap(List<String> tagList) {
        Map<String, Map<String, String>> map = new HashMap<>();
        for (String tag : tagList) {
            // 如果是枚举类型， 将英文标识转换为中文
            LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(tag);
            if (Objects.nonNull(labelInfoDTO) && Objects.nonNull(labelInfoDTO.getDimEnumMetaId()) && labelInfoDTO.getDimEnumMetaId() != -1) {
                List<EnumDimDataDTO> enumValues = enumDimDataService.listByDimMetaId(labelInfoDTO.getDimEnumMetaId());
                if (CollectionUtils.isEmpty(enumValues)) {
                    // 不需要转换
                } else {
                    // 将名字替换
                    Map<String, String> enumCodeAndDescMap = enumValues.stream().filter(p -> p.getDeleted().intValue() == 0)
                            .collect(Collectors.toMap(EnumDimDataDTO::getEnumCode, EnumDimDataDTO::getEnumDesc, (v1, v2) -> v1));
                    map.put(tag, enumCodeAndDescMap);
                }
            }
        }
        return map;
    }

    /**
     * 将枚举code转为枚举名称
     *
     * @param tagName     标签名
     * @param tagValue    标签值
     * @param enumDataMap 标签对应的枚举值map
     * @return 标签值名称
     */
    private String convertTagValue(String tagName, String tagValue, Map<String, Map<String, String>> enumDataMap) {
        if (OdpsConstants.NULL.equals(tagValue) || StringUtils.isBlank(tagValue)) {
            return "未知";
        }
        // 如果是枚举类型， 将英文标识转换为中文
        if (MapUtils.isNotEmpty(enumDataMap) && MapUtils.isNotEmpty(enumDataMap.get(tagName))) {
            Map<String, String> enumAndDescMap = enumDataMap.get(tagName);
            if (enumAndDescMap.containsKey(tagValue)) {
                return enumAndDescMap.get(tagValue);
            }
        }
        return tagValue;
    }

    private void addCurLabelName(StringBuilder namePath, Map<String, AnalysisV2Meta.TagInfo> tagInfoMap) {
        for (Map.Entry<String, AnalysisV2Meta.TagInfo> tagInfoEntry : tagInfoMap.entrySet()) {
            String key = tagInfoEntry.getKey();
            AnalysisV2Meta.TagInfo value = tagInfoEntry.getValue();
            LabelInfoDTO byLabelCode = labelInfoService.findByLabelCode(key);
            if (Objects.nonNull(byLabelCode)) {
                value.setCnName(byLabelCode.getName());
                namePath.append(byLabelCode.getName());
                namePath.append("+");
            } else {
                namePath.append("不存在的标签.");
            }
        }
        // 删除最后一个加号
        namePath.deleteCharAt(namePath.length() - 1);
    }

    /**
     * dimSource: "ZHUGE"
     * id: "birth_province"
     * isDim: true
     * isRate: false
     * isTime: false
     * name: "出生所在省份"
     *
     * @param tagInfoMap
     * @return
     */
    private List<OlapQueryResultMeta> buildQueryResultMeta(Map<String, AnalysisV2Meta.TagInfo> tagInfoMap) {
        List<OlapQueryResultMeta> resultMetaList = new ArrayList<>();
        for (Map.Entry<String, AnalysisV2Meta.TagInfo> entry : tagInfoMap.entrySet()) {
            AnalysisV2Meta.TagInfo tagInfo = entry.getValue();
            OlapQueryResultMeta queryResultMeta = new OlapQueryResultMeta();
            queryResultMeta.setId(tagInfo.getName());
            queryResultMeta.setName(tagInfo.getCnName());
            queryResultMeta.setIsDim(true);
            queryResultMeta.setIsTime(false);
            queryResultMeta.setIsRate(false);
            resultMetaList.add(queryResultMeta);
        }
        // 有固定的两个meta - 诸葛人群占比
        OlapQueryResultMeta queryResultRateMeta = new OlapQueryResultMeta();
        queryResultRateMeta.setId(COLUMN_RATE);
        queryResultRateMeta.setName(COLUMN_RATE_NAME);
        queryResultRateMeta.setIsDim(false);
        queryResultRateMeta.setIsTime(false);
        queryResultRateMeta.setIsRate(false);
        resultMetaList.add(queryResultRateMeta);

        // 有固定的两个meta - 诸葛人群用户数
        OlapQueryResultMeta queryResultCntMeta = new OlapQueryResultMeta();
        queryResultCntMeta.setId(OLAP_MEASURE_PICASSO_GROUP_USER_CNT);
        queryResultCntMeta.setName("诸葛人群用户数");
        queryResultCntMeta.setIsDim(false);
        queryResultCntMeta.setIsTime(false);
        queryResultCntMeta.setIsRate(false);
        resultMetaList.add(queryResultCntMeta);
        return resultMetaList;
    }

    private List<AnalysisV2Meta.TagInfo> getParentTagInfoList(Long id, List<AnalysisV2Meta.TagInfo> parentTagInfoList, StringBuilder namePath) {
        AnalysisV2DO analysisV2DO = analysisV2Service.findById(id);
        if (Objects.isNull(analysisV2DO)) {
            return parentTagInfoList;
        }
        if (StringUtils.isBlank(analysisV2DO.getParentMeta()) || "null".equals(analysisV2DO.getParentMeta())) {
            return parentTagInfoList;
        }
        AnalysisV2Meta parentAnalysisV2Meta = JSON.parseObject(analysisV2DO.getParentMeta(), AnalysisV2Meta.class);
        if (Objects.isNull(parentAnalysisV2Meta)) {
            return parentTagInfoList;
        }
        List<AnalysisV2Meta.TagInfo> tagInfoList = parentAnalysisV2Meta.getTagInfoList();
        if (!CollectionUtils.isEmpty(tagInfoList)) {
            String lableCode = tagInfoList.get(0).getName();
            LabelInfoDTO byLabelCode = labelInfoService.findByLabelCode(lableCode);
            namePath.insert(0, String.format("%s=%s -> ", byLabelCode.getName(), tagInfoList.get(0).getValue()));
            parentTagInfoList.addAll(tagInfoList);
        }

        // 遍历所有的父分析
        if (Objects.nonNull(analysisV2DO.getParentId()) && analysisV2DO.getParentId() != 0) {
            getParentTagInfoList(analysisV2DO.getParentId(), parentTagInfoList, namePath);
        }
        return parentTagInfoList;
    }


    private void failCountIncrement(AnalysisV2DO analysisV2DO) {
        int odpsFailTimes = Objects.isNull(analysisV2DO.getFailTimes()) ? Byte.valueOf((byte) 0) : analysisV2DO.getFailTimes();
        if (odpsFailTimes > allowAnalysisFailTimes) {
            sendAnalysisMsg(analysisV2DO);
        } else {
            analysisV2DO.setFailTimes((byte) ++odpsFailTimes);
            analysisV2Service.updateSelective(analysisV2DO, AnalysisV2StatusEnum.RUNNING.getStatus());
        }
    }


    @Override
    public AnalysisV2StatusEnum typeOrStatus() {
        return AnalysisV2StatusEnum.FINISH;
    }


    private void sendAnalysisMsg(AnalysisV2DO analysisV2DO) {
        String msg = "洞察分析ODPS执行异常：" + LINE_FEED +
                String.format("分析ID：%s", analysisV2DO.getId()) + LINE_FEED +
                String.format("分析name：%s", analysisV2DO.getName()) + LINE_FEED +
                "ODPS任务状态异常：" + LINE_FEED +
                String.format("instantId: %s", getInstantId(analysisV2DO.getExtInfo())) + LINE_FEED +
                String.format("logView: %s", getOdpsLogView(analysisV2DO.getExtInfo())) + LINE_FEED;
        dingDingNotifySender.sendDingDingGroupMsg(msg);
    }
}
