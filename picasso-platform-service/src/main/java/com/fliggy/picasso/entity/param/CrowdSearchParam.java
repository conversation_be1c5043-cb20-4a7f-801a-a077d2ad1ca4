package com.fliggy.picasso.entity.param;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import cn.hutool.core.util.NumberUtil;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoSearch;
import com.fliggy.picasso.common.enums.ProfileCodeEnum;
import com.fliggy.picasso.common.enums.crowd.CrowdCircleTypeEnum;
import com.fliggy.picasso.common.enums.crowd.CrowdClassifyTabEnum;
import com.fliggy.picasso.common.enums.crowd.CrowdTimeTypeEnum;
import com.fliggy.picasso.common.enums.crowd.CrowdTypeEnum;
import com.fliggy.picasso.common.enums.crowd.CrowdValidityTypeEnum;
import com.fliggy.picasso.common.enums.group.GroupStatusEnum;
import com.fliggy.picasso.common.enums.group.ProfileEnum;
import com.fliggy.picasso.config.switcher.CrowdSwitchConfig;
import com.fliggy.picasso.group.crowd.dag.galaxy.utils.DateUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Data
public class CrowdSearchParam {

    private String empId;

    /**
     * 需要搜索的负责人用户工号
     */
    private String searchEmpId;

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 分组内人群id列表
     */
    private List<Long> ids;

    /**
     * 分类tab
     */
    private CrowdClassifyTabEnum classifyTab;

    /**
     * 人群信息：人群id、人群名称
     */
    private String content;

    /**
     * 人群类型，即画像类型，淘宝id/设备id
     */
    private List<ProfileCodeEnum> profileCodeList;

    /**
     * 圈选方式
     */
    private List<CrowdCircleTypeEnum> circleTypeList;

    /**
     * 人群时效
     */
    private List<CrowdTimeTypeEnum> timeTypeList;

    /**
     * 人群状态
     */
    private List<GroupStatusEnum> crowdStatusList;

    /**
     * 有效期类型
     */
    private CrowdValidityTypeEnum validityType;

    private Integer pageNo;
    private Integer pageSize;

    public CrowdMetaInfoSearch convert2CrowdMetaInfoSearch() {
        CrowdMetaInfoSearch search = new CrowdMetaInfoSearch();
        
        // 处理官方人群分类
        if (Objects.equals(this.classifyTab, CrowdClassifyTabEnum.OFFICIAL_CROWDS)) {
            // 如果是官方人群，使用配置的官方人群ID列表
            if (CollectionUtils.isNotEmpty(CrowdSwitchConfig.officialCrowds)) {
                List<Long> officialCrowdIds = new ArrayList<>(CrowdSwitchConfig.officialCrowds);
                // 如果已有ID过滤条件，取交集；否则直接使用官方人群ID
                if (CollectionUtils.isNotEmpty(this.ids)) {
                    officialCrowdIds.retainAll(this.ids);
                }
                search.setIds(officialCrowdIds);
            } else {
                // 如果官方人群列表为空，设置一个不存在的ID，避免查询到数据
                search.setIds(Lists.newArrayList(-1L));
            }
        } else if (CollectionUtils.isNotEmpty(this.ids)) {
            search.setIds(this.ids);
        }
        if (StringUtils.isNotBlank(this.content)) {
            if (NumberUtil.isNumber(this.content)) {
                Long contentId = Long.valueOf(this.content);
                List<Long> searchIds = Optional.ofNullable(search.getIds()).orElse(Lists.newArrayList());
                
                // 如果是官方人群搜索，需要检查ID是否在官方人群列表中
                if (Objects.equals(this.classifyTab, CrowdClassifyTabEnum.OFFICIAL_CROWDS)) {
                    if (CollectionUtils.isNotEmpty(CrowdSwitchConfig.officialCrowds) && 
                        CrowdSwitchConfig.officialCrowds.contains(contentId)) {
                        searchIds.add(contentId);
                    } else {
                        // 如果ID不在官方人群中，清空已有ID，只保留不存在的ID，确保返回空结果
                        searchIds.clear();
                        searchIds.add(-1L);
                    }
                } else {
                    searchIds.add(contentId);
                }
                search.setIds(searchIds);
            } else {
                search.setContent(this.content);
            }
        }
        if (StringUtils.isNotBlank(this.searchEmpId)) {
            search.setSearchEmpId(this.searchEmpId);
        }
        if (CollectionUtils.isNotEmpty(this.profileCodeList)) {
            List<String> profileTypeList = new ArrayList<>();
            for (ProfileCodeEnum profileCode : this.profileCodeList) {
                // 历史原因，后续再调整
                if (Objects.equals(profileCode, ProfileCodeEnum.USER)) {
                    profileTypeList.add(ProfileEnum.TAOBAO_USER.getCode());
                }
                if (Objects.equals(profileCode, ProfileCodeEnum.DEVICE)) {
                    profileTypeList.add(ProfileEnum.DEVICE.getCode());
                }
            }
            search.setProfileTypeList(profileTypeList);
        }
        if (CollectionUtils.isNotEmpty(this.circleTypeList)) {
            List<String> list = new ArrayList<>();
            for (CrowdCircleTypeEnum circleType : this.circleTypeList) {
                if (Objects.equals(circleType, CrowdCircleTypeEnum.LABEL_CROWD)) {
                    list.add(CrowdTypeEnum.LABEL_CROWD.getName());
                } else if (Objects.equals(circleType, CrowdCircleTypeEnum.IMPORT_CROWD)) {
                    list.addAll(Lists.newArrayList(Arrays.asList(CrowdTypeEnum.ODPS_TABLE_CROWD.getName(),
                        CrowdTypeEnum.ODPS_SQL_CROWD.getName(), CrowdTypeEnum.FILE_CROWD.getName())));
                } else if (Objects.equals(circleType, CrowdCircleTypeEnum.OPERATE_CROWD)) {
                    list.addAll(Lists.newArrayList(CrowdTypeEnum.OPERATE_CROWD.getName(), CrowdTypeEnum.OPERATE_EXPRESSION_CROWD.getName()));
                } else if (Objects.equals(circleType, CrowdCircleTypeEnum.DYNAMIC_CROWD)) {
                    list.add(CrowdTypeEnum.DYNAMIC_CROWD.getName());
                }
            }
            search.setCrowdTypeList(list);
        } else {
            List<String> crowdTypeList = Arrays.asList(CrowdTypeEnum.LABEL_CROWD.getName(), CrowdTypeEnum.DYNAMIC_CROWD.getName(),
                    CrowdTypeEnum.ODPS_TABLE_CROWD.getName(), CrowdTypeEnum.ODPS_SQL_CROWD.getName(),
                    CrowdTypeEnum.FILE_CROWD.getName(), CrowdTypeEnum.OPERATE_CROWD.getName(), CrowdTypeEnum.OPERATE_EXPRESSION_CROWD.getName());
            search.setCrowdTypeList(crowdTypeList);
        }
        if (CollectionUtils.isNotEmpty(this.timeTypeList)) {
            List<Byte> list = new ArrayList<>();
            for (CrowdTimeTypeEnum timeType : this.timeTypeList) {
                if (Objects.equals(timeType, CrowdTimeTypeEnum.OFFLINE)) {
                    list.add((byte) 0);
                } else if (Objects.equals(timeType, CrowdTimeTypeEnum.ONLINE)) {
                    list.addAll(Arrays.asList((byte) 1, (byte) 2));
                }
             }
            search.setRealTimeList(list);
        }
        if (CollectionUtils.isNotEmpty(this.crowdStatusList)) {
            search.setCrowdStatusList(this.crowdStatusList.stream().map(GroupStatusEnum::getName).collect(Collectors.toList()));
        }
        if (Objects.nonNull(this.validityType)) {
            if (Objects.equals(this.validityType, CrowdValidityTypeEnum.VALID)) {
                search.setExpiredDateStart(new Date());
            } else if (Objects.equals(this.validityType, CrowdValidityTypeEnum.EXPIRED)) {
                search.setExpiredDateEnd(new Date());
            } else if (Objects.equals(this.validityType, CrowdValidityTypeEnum.EXPIRE_IN_7DAYS)) {
                search.setExpiredDateStart(new Date());
                search.setExpiredDateEnd(DateUtil.getAfterDay(new Date(), 7));
            }
        }

        int pageNo = Objects.isNull(this.pageNo) ? 1 : this.pageNo;
        int pageSize = Objects.isNull(this.pageSize) ? 10 : this.pageSize;
        int pageIndex = (pageNo - 1) * pageSize;
        search.setPageIndex(pageIndex);
        search.setPageSize(pageSize);
        return search;
    }
}
