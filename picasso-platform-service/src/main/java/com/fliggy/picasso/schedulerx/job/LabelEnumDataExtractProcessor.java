package com.fliggy.picasso.schedulerx.job;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.odps.Instance;
import com.aliyun.odps.data.Record;
import com.fliggy.picasso.action.OdpsProfileHa3FormatAction;
import com.fliggy.picasso.alarm.dingcard.DingTalkEventTypeEnum;
import com.fliggy.picasso.alarm.dingcard.DingTalkNotifyManager;
import com.fliggy.picasso.alarm.dingcard.DingTalkReq;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.common.domain.crowd.record.AsyncTaskExtInfo;
import com.fliggy.picasso.common.domain.crowd.record.AsyncTaskRecordVO;
import com.fliggy.picasso.common.domain.label.LabelDataConfig;
import com.fliggy.picasso.common.enums.AsyncTaskRecordTypeEnum;
import com.fliggy.picasso.common.enums.AsyncTaskStatusEnum;
import com.fliggy.picasso.common.enums.label.LabelDataDescEnum;
import com.fliggy.picasso.common.enums.label.LabelStatusEnum;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.common.utils.NumberUtils;
import com.fliggy.picasso.offline.OdpsService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.record.AsyncTaskRecordService;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.fliggy.picasso.common.Constant.DTS_LOG;
import static com.fliggy.picasso.common.constants.OdpsConstants.ODPS_EXECUTE_PROJECT;

@Component
public class LabelEnumDataExtractProcessor extends MapJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DTS_LOG);

    @Switch(description = "枚举值提取任务失败告警接收人")
    public String labelEnumDataExtractFailAlarmEmpId = "395824,224231";

    @Resource
    private AsyncTaskRecordService asyncTaskRecordService;
    @Resource
    private LabelInfoService labelInfoService;
    @Resource
    private OdpsService odpsService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String level1Dispatch = "Level1Dispatch";
        String taskName = context.getTaskName();
        Object task = context.getTask();
        try {
            if (isRootTask(context)) {
                List<AsyncTaskRecordVO> asyncTaskRecordVOList = null;
                String param = StringUtils.isNotEmpty(context.getInstanceParameters()) ? context.getInstanceParameters() : context.getJobParameters();
                if (StringUtils.isNotBlank(param)) {
                    asyncTaskRecordVOList = asyncTaskRecordService.queryByIds(NumberUtils.split(param));
                } else {
                    List<AsyncTaskStatusEnum> statusList = Arrays.asList(AsyncTaskStatusEnum.INIT, AsyncTaskStatusEnum.RUNNING);
                    asyncTaskRecordVOList = asyncTaskRecordService.queryByTypeAndStatusList(AsyncTaskRecordTypeEnum.TABLE_ENUM_EXTRACT, statusList);
                }

                if (CollectionUtils.isNullOrEmpty(asyncTaskRecordVOList)) {
                    return new ProcessResult(true);
                }
                return map(asyncTaskRecordVOList, level1Dispatch);
            } else if (level1Dispatch.equals(taskName)) {
                AsyncTaskRecordVO asyncTaskRecordVO = (AsyncTaskRecordVO) task;
                if (Objects.equals(asyncTaskRecordVO.getTaskStatus(), AsyncTaskStatusEnum.INIT)) {
                    boolean startTask = startEnumDataExtractTask(asyncTaskRecordVO);
                    return new ProcessResult(startTask);
                }

                if (Objects.equals(asyncTaskRecordVO.getTaskStatus(), AsyncTaskStatusEnum.RUNNING)) {
                    boolean statusTask = statusEnumDataExtractTask(asyncTaskRecordVO);
                    return new ProcessResult(statusTask);
                }
                return new ProcessResult(true);
            } else {
                return new ProcessResult(false);
            }
        } catch (Exception e) {
            log.error("LabelEnumDataExtractProcessor process error", e);
            return new ProcessResult(false, e.getMessage());
        }
    }

    /**
     * 枚举值提取任务状态处理
     * @param taskVO
     * @return
     */
    private boolean statusEnumDataExtractTask(AsyncTaskRecordVO taskVO) {
        if (Objects.isNull(taskVO) || StringUtils.isEmpty(taskVO.getTaskId())) {
            return false;
        }
        Instance.TaskStatus.Status status = odpsService.checkInstanceStatus(taskVO.getTaskId());
        if (Objects.isNull(status)) {
            return false;
        }

        switch (status) {
            case WAITING:
            case RUNNING:
            case SUSPENDED:
                return true;
            case SUCCESS:
                return handleSuccess(taskVO);
            case FAILED:
            case CANCELLED:
                return handleFailed(taskVO);
        }
        return false;
    }

    /**
     * 成功状态处理
     * @param taskVO
     * @return
     */
    private boolean handleSuccess(AsyncTaskRecordVO taskVO) {
        if (Objects.isNull(taskVO) || Objects.isNull(taskVO.getEntity())
                || Objects.isNull(taskVO.getTaskId()) || Objects.isNull(taskVO.getExtInfo())) {
            return false;
        }

        taskVO.setGmtModified(new Date());
        taskVO.setTaskStatus(AsyncTaskStatusEnum.SUCCESS);
        int update = asyncTaskRecordService.update(taskVO);
        if (update <= 0) {
            return false;
        }

        String taskName = taskVO.getExtInfo().getTaskName();
        List<Record> recordList = odpsService.getAsyncExecuteResult(taskVO.getTaskId(), taskName);
        return upsertLabelEnumExtract(taskVO.getEntity(), recordList);
    }

    private boolean upsertLabelEnumExtract(String labelCode, List<Record> recordList) {
        LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(labelCode);
        if (Objects.isNull(labelInfoDTO)) {
            return false;
        }

        LabelDataConfig dataConfig = labelInfoDTO.getDataConfig();
        if (Objects.isNull(dataConfig) || !Objects.equals(dataConfig.getDataDesc(), LabelDataDescEnum.AUTO_CONFIG)) {
            return false;
        }

        Map<String, String> dataValueMap = Maps.newHashMap();
        for (Record record : recordList) {
            String enumCode = record.getString("field");
            dataValueMap.put(enumCode, enumCode);
        }
        dataConfig.setDataValue(dataValueMap);

        LabelInfoDTO updateDTO = new LabelInfoDTO();
        updateDTO.setId(labelInfoDTO.getId());
        updateDTO.setStatus(LabelStatusEnum.ACTIVATE);
        updateDTO.setDataConfig(dataConfig);
        return labelInfoService.updateSelectiveById(labelInfoDTO.getId(), updateDTO) > 0;
    }

    /**
     * 失败状态处理
     * @param taskVO
     * @return
     */
    private boolean handleFailed(AsyncTaskRecordVO taskVO) {
        if (Objects.isNull(taskVO)) {
            return false;
        }

        taskVO.setGmtModified(new Date());
        taskVO.setTaskStatus(AsyncTaskStatusEnum.FAILED);
        return asyncTaskRecordService.update(taskVO) > 0;
    }

    private void sendDingMsg(String labelName, String labelCode) {
        if (StringUtils.isEmpty(labelEnumDataExtractFailAlarmEmpId)) {
            return;
        }
        if (StringUtils.isEmpty(labelName) && StringUtils.isEmpty(labelCode)) {
            return;
        }
        String alarmMsg = String.format("###  诸葛枚举值提取失败告警:  \n  %s(%s)枚举值提取失败，请及时处理。" , labelName, labelCode);
        DingTalkReq dingTalkReq = DingTalkReq.builder().empId(labelEnumDataExtractFailAlarmEmpId).msg(alarmMsg).title("诸葛枚举值提取失败告警").build();
        DingTalkNotifyManager.sendDingMsg(dingTalkReq, DingTalkEventTypeEnum.ODPS_ALARM);
    }

    /**
     * 开始枚举值提取任务
     * @param asyncTaskRecordVO
     * @return
     */
    private boolean startEnumDataExtractTask(AsyncTaskRecordVO asyncTaskRecordVO) {
        if (Objects.isNull(asyncTaskRecordVO) || StringUtils.isEmpty(asyncTaskRecordVO.getEntity())) {
            return true;
        }

        // start task
        Instance instance = null;
        String taskName = odpsService.getTaskName("EnumExtractTask_");
        try {
            String sql = generateSql(asyncTaskRecordVO.getEntity());
            if (StringUtils.isEmpty(sql)) {
                return false;
            }
            instance = odpsService.startOdpsTask(ODPS_EXECUTE_PROJECT, sql, taskName, null);
        } catch (Exception e) {
            log.error("startEnumDataExtractTask error", e);
            AsyncTaskRecordVO updateVO = new AsyncTaskRecordVO();
            updateVO.setId(asyncTaskRecordVO.getId());
            updateVO.setTaskStatus(AsyncTaskStatusEnum.FAILED);
            updateVO.setErrMsg(e.getMessage());
            int update = asyncTaskRecordService.update(updateVO);
            return update > 0;
        }
        if (Objects.isNull(instance) || Objects.isNull(instance.getId())) {
            return false;
        }

        // update status
        AsyncTaskRecordVO updateVO = new AsyncTaskRecordVO();
        updateVO.setId(asyncTaskRecordVO.getId());
        updateVO.setTaskId(instance.getId());
        updateVO.setTaskStatus(AsyncTaskStatusEnum.RUNNING);
        AsyncTaskExtInfo extInfo = new AsyncTaskExtInfo();
        extInfo.setTaskName(taskName);
        updateVO.setExtInfo(extInfo);
        int update = asyncTaskRecordService.update(updateVO);
        return update > 0;
    }

    private String generateSql(String labelCode) {
        LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(labelCode);
        if (Objects.isNull(labelInfoDTO) || Objects.isNull(labelInfoDTO.getSourceConfig())) {
            return null;
        }
        JSONObject sourceConfig = labelInfoDTO.getSourceConfig();
        String project = (String) sourceConfig.get("project");
        String tableName = (String) sourceConfig.get("table");
        String field = (String) sourceConfig.get("field");
        if (StringUtils.isEmpty(project) || StringUtils.isEmpty(tableName) || StringUtils.isEmpty(field)) {
            return null;
        }

        String guid = project + OdpsConstants.DOT + tableName;
        String sqlFormat = "select %s as field,count(*) as cnt from %s where ds=MAX_PT('%s') and %s is not null group by %s order by cnt desc limit 1000;";
        return String.format(sqlFormat, field, guid, guid, field, field);
    }
}
