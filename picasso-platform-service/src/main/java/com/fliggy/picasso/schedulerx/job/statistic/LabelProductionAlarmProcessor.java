package com.fliggy.picasso.schedulerx.job.statistic;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapReduceJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.alarm.dingtalk.DingDingNotifySender;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.constants.DingTalkConstants;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.entity.bo.LabelStatisticsBO;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.label.LabelStatisticsService;
import com.taobao.ateye.annotation.Switch;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;

/**
 * 离线标签产出监控告警任务
 * 监控离线标签是否产出，未产出则告警
 */
@Component
public class LabelProductionAlarmProcessor extends MapReduceJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);
    
    @Switch(name = "画像调度时间，默认14点")
    private static int PROFILE_RUN_DISPATCH_HOUR = 14;

    @Resource
    private LabelInfoService labelInfoService;
    @Resource
    private LabelStatisticsService labelStatisticsService;
    @Resource
    private DingDingNotifySender dingDingNotifySender;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String dispatchName = "label_production_dispatch_level1";
        String taskName = context.getTaskName();
        
        try {
            if (isRootTask(context)) {
                List<LabelInfoDTO> labelList;
                String paramStr = StringUtils.isBlank(context.getInstanceParameters()) ? context.getJobParameters() : context.getInstanceParameters();
                if (StringUtils.isNotBlank(paramStr)) {
                    labelList = labelInfoService.findByCodes(Arrays.asList(paramStr.split(",")));
                } else {
                    labelList = labelInfoService.listOfflineStandardLabels();
                }

                if (CollectionUtils.isEmpty(labelList)) {
                    return new ProcessResult(true);
                }
                return map(labelList, dispatchName);
            } else if (taskName.equals(dispatchName)) {
                LabelInfoDTO labelDTO = (LabelInfoDTO) context.getTask();
                if (Objects.isNull(labelDTO)) {
                    return new ProcessResult(true);
                }
                
                // 检查标签是否产出
                LabelProductionInfo productionInfo = checkLabelProduction(labelDTO);
                return new ProcessResult(true, JSON.toJSONString(productionInfo));
            } else {
                return new ProcessResult(true);
            }
        } catch (Exception e) {
            log.error("离线标签产出监控检查失败", e);
            return new ProcessResult(false, e.getMessage());
        }
    }
    
    @Override
    public ProcessResult reduce(JobContext context) {
        Map<Long, String> taskResults = context.getTaskResults();
        if (taskResults == null || taskResults.isEmpty()) {
            return new ProcessResult(true);
        }
        
        List<LabelProductionInfo> alarmLabels = new ArrayList<>();
        for (String result : taskResults.values()) {
            if (StringUtils.isEmpty(result)) {
                continue;
            }
            LabelProductionInfo productionInfo = JSON.parseObject(result, LabelProductionInfo.class);
            if (Objects.isNull(productionInfo)) {
                continue;
            }
            
            // 收集需要告警的标签（未产出或晚产出）
            if (!productionInfo.isNormal()) {
                alarmLabels.add(productionInfo);
            }
        }
        
        // 发送告警
        if (CollectionUtils.isNotEmpty(alarmLabels)) {
            String yesterday = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
            sendAlarm(alarmLabels, yesterday);
        }
        return new ProcessResult(true);
    }

    /**
     * 检查标签是否产出
     */
    private LabelProductionInfo checkLabelProduction(LabelInfoDTO labelDTO) {
        String yesterday = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
        
        LabelProductionInfo productionInfo = new LabelProductionInfo();
        productionInfo.setLabelCode(labelDTO.getCode());
        productionInfo.setLabelName(labelDTO.getName());
        productionInfo.setTargetDate(yesterday);
        productionInfo.setNormal(true);

        // 查询昨天的标签产出记录
        LabelStatisticsBO yesterdayStat = labelStatisticsService.queryByLabelCodeAndDate(labelDTO.getCode(), yesterday);
        if (Objects.isNull(yesterdayStat) || StringUtils.isEmpty(yesterdayStat.getOutputTime()) || Objects.isNull(yesterdayStat.getCoverAmount())) {
            productionInfo.setNormal(false);
            productionInfo.setAlarmReason("当天未产出");
            return productionInfo;
        }
        
        // 判断产出量是否为0
        Long coverAmount = yesterdayStat.getCoverAmount();
        if (coverAmount == 0) {
            productionInfo.setNormal(false);
            productionInfo.setAlarmReason("当天未产出（覆盖量为0）");
            return productionInfo;
        }
        
        // 判断产出时间是否在画像调度前
        String outputTime = yesterdayStat.getOutputTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.HMS_FORMAT);
        LocalTime time = LocalTime.parse(outputTime, formatter);
        if (time.getHour() >= PROFILE_RUN_DISPATCH_HOUR) {
            productionInfo.setNormal(false);
            productionInfo.setAlarmReason("画像调度前未产出（产出时间：" + outputTime + "）");
        }
        return productionInfo;
    }

    /**
     * 发送告警
     */
    private void sendAlarm(List<LabelProductionInfo> alarmLabels, String targetDate) {
        StringBuilder content = new StringBuilder();
        content.append("### 【诸葛离线标签监控-产出时间异常告警】\n\n");
        content.append("以下标签 ").append(targetDate).append("分区未及时产出，请关注：\n\n");
        
        content.append("| 标签code | 标签名称 | 告警原因 |\n");
        content.append("| --- | --- | --- |\n");
        
        for (LabelProductionInfo labelInfo : alarmLabels) {
            content.append("| ").append(labelInfo.getLabelCode()).append(" | ")
                   .append(labelInfo.getLabelName()).append(" | ")
                   .append(labelInfo.getAlarmReason()).append(" |\n");
        }
        
        content.append("\n---\n\n");
        content.append("[诸葛平台](https://zhuge.alibaba-inc.com)");
        
        // 发送钉钉告警
        dingDingNotifySender.sendDingTalkGroupMsg(
                "诸葛离线标签产出时间异常告警",
                content.toString(),
                DingTalkConstants.OFFLINE_LABEL_ALARM_ACCESS_TOKEN);
        
        log.info("标签产出告警，content: {}", content.toString());
    }

    /**
     * 标签产出信息
     */
    @Data
    public static class LabelProductionInfo {
        /**
         * 标签编码
         */
        private String labelCode;
        
        /**
         * 标签名称
         */
        private String labelName;
        
        /**
         * 目标日期
         */
        private String targetDate;

        /**
         * 是否正常产出（正常产出且在画像调度前）
         */
        private boolean normal = true;
        
        /**
         * 告警原因
         */
        private String alarmReason;
    }
} 