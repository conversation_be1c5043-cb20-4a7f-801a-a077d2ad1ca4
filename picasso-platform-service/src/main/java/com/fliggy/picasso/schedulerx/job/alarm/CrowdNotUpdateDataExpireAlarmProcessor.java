package com.fliggy.picasso.schedulerx.job.alarm;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.alarm.roar.RoarCenterService;
import com.fliggy.picasso.alarm.roar.domain.RoarAlarmInfo;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoQuery;
import com.fliggy.picasso.common.enums.alarm.AlarmSceneTypeEnum;
import com.fliggy.picasso.common.enums.alarm.RoarAlarmTypeEnum;
import com.fliggy.picasso.common.enums.group.GroupMatchTypeEnum;
import com.fliggy.picasso.common.enums.group.GroupStatusEnum;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.config.SwitchConfig;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class CrowdNotUpdateDataExpireAlarmProcessor extends JavaProcessor {

    @Switch(description = "不更新人群数据即将过期告警天数")
    public static int aboutExpireDays = 365;

    @Resource
    private CrowdService crowdService;
    @Resource
    private RoarCenterService roarCenterService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        // 不更新有效人群数据即将到期
        CrowdMetaInfoQuery query = new CrowdMetaInfoQuery();
        query.setNeedUpdate((byte)0);
        query.setCrowdStatus(GroupStatusEnum.SUCCESS);
        query.setBizDateMax(DateUtils.getDateStrYYYYMMDD(-1 * aboutExpireDays));
        query.setDeleted((byte)0);
        query.setExpiredDateStart(new Date());
        query.setMatchTypeList(Arrays.asList(GroupMatchTypeEnum.SNAPSHOT_MATCH, GroupMatchTypeEnum.OPERATE_RULE_AND_SNAPSHOT_MATCH));
        List<CrowdMetaInfoDO> notUpdateCrowdDOList = crowdService.listQuery(query, 1, 10000);
        if (CollectionUtils.isEmpty(notUpdateCrowdDOList)) {
            return new ProcessResult(true);
        }

        // 按告警人聚合
        Map<String, Set<Long>> aboutExpireCrowdReceiverMap = getAboutExpireReceiverMap(notUpdateCrowdDOList);
        if (aboutExpireCrowdReceiverMap.isEmpty()) {
            return new ProcessResult(true);
        }

        // 发送告警
        sendCrowdExpireAlarm(aboutExpireCrowdReceiverMap);
        return new ProcessResult(true);
    }

    private Map<String, Set<Long>> getAboutExpireReceiverMap(List<CrowdMetaInfoDO> notUpdateCrowdDOList) {
        Map<String, Set<Long>> result = new HashMap<>();
        for (CrowdMetaInfoDO crowdMetaInfoDO : notUpdateCrowdDOList) {
            List<String> receivers = getReceivers(crowdMetaInfoDO);
            if (CollectionUtils.isEmpty(receivers)) {
                continue;
            }
            for (String receiver : receivers) {
                result.computeIfAbsent(receiver, k -> new HashSet<>()).add(crowdMetaInfoDO.getId());
            }
        }
        return result;
    }

    private List<String> getReceivers(CrowdMetaInfoDO crowdMetaInfoDO) {
        if (SwitchConfig.COMMON_DEBUG_SWITCH) {
            return Lists.newArrayList("395824");
        }
        if (Objects.isNull(crowdMetaInfoDO.getCreator()) && CollectionUtils.isEmpty(crowdMetaInfoDO.getOperator())) {
            return Lists.newArrayList();
        }

        List<String> result = Lists.newArrayList();
        if (Objects.nonNull(crowdMetaInfoDO.getCreator())) {
            result.add(crowdMetaInfoDO.getCreator().getEmpId());
        }
        if (CollectionUtils.isNotEmpty(crowdMetaInfoDO.getOperator())) {
            result.addAll(crowdMetaInfoDO.getOperator().stream().map(Employee::getEmpId).collect(Collectors.toList()));
        }
        return result;
    }

    private void sendCrowdExpireAlarm(Map<String, Set<Long>> aboutExpireCrowdReceiverMap) {
        for (Map.Entry<String, Set<Long>> entry : aboutExpireCrowdReceiverMap.entrySet()) {
            if (StringUtils.isEmpty(entry.getKey()) || CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            List<Long> crowdIds = new ArrayList<>(entry.getValue());

            RoarAlarmInfo alarmInfo = new RoarAlarmInfo();
            alarmInfo.setReceiver(entry.getKey());
            alarmInfo.setCrowdIds(crowdIds);
            alarmInfo.setTitle("诸葛人群-不更新人群数据即将过期");
            alarmInfo.setContent(getAlarmContent(crowdIds));
            alarmInfo.setSceneType(AlarmSceneTypeEnum.CROWD_NOT_UPDATE_DATA_EXPIRE);
            alarmInfo.setAlarmType(RoarAlarmTypeEnum.DING_TALK);
            alarmInfo.setMsgType("markdown");
            roarCenterService.sendAlarm(alarmInfo);
        }
    }

    private String getAlarmContent(List<Long> crowdIds) {
        String contentFormat = "## 【诸葛人群-不更新人群数据即将过期】\n"
            + "您的诸葛不更新人群快照包已存放%s天，即将因为底层存储时间的限制而导致无法执行匹配，请及时处理。\n"
            + "若人群不再使用，请下线。若还需要使用，请点击更多 -> 重跑，让人群生成新快照。\n"
            + "* 人群ids：%s  \n"
            + "\n"
            + "---\n"
            + "\n"
            + "[诸葛平台](%s)";
        String crowdIdsStr = crowdIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        return String.format(contentFormat, aboutExpireDays, crowdIdsStr,
            "dingtalk://dingtalkclient/page/link?pc_slide=false&url=https://zhuge.alibaba-inc.com%23/zhuge/crowd-stategy/gather-person");
    }
}