package com.fliggy.picasso.schedulerx.job.alarm;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.common.Constant;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.enums.alarm.AlarmSceneTypeEnum;
import com.fliggy.picasso.common.enums.alarm.RoarAlarmTypeEnum;
import com.fliggy.picasso.common.enums.group.GroupStatusEnum;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.entity.alarm.CrowdMetaInfoWithAlarmInfos;
import com.fliggy.picasso.entity.bo.CrowdAlarmInfoBO;
import com.fliggy.picasso.entity.bo.CrowdMonitorConfigBO;
import com.fliggy.picasso.entity.bo.MonitorExtInfo;
import com.fliggy.picasso.service.alarm.CrowdAlarmFactory;
import com.fliggy.picasso.service.alarm.CrowdMonitorConfigService;
import com.fliggy.picasso.service.alarm.impl.AbstractCrowdAlarmServiceImpl;
import com.fliggy.picasso.service.crowd.CrowdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

@Component
public class CrowdBaseLineAlarmProcessor extends JavaProcessor {

    @Resource
    private CrowdService crowdService;
    @Resource
    private CrowdMonitorConfigService crowdMonitorConfigService;
    @Resource
    private CrowdAlarmFactory crowdAlarmFactory;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String scheduleTime = StringUtils.isNotBlank(context.getInstanceParameters()) ? context.getInstanceParameters() :
            context.getScheduleTime().toString(DateUtils.YMDHM_FORMAT2);
        String[] timeArr = scheduleTime.split(" ");
        if (timeArr.length != 2) {
            return new ProcessResult(true);
        }
        String time = timeArr[1];
        String bizdate = DateTime.now().minusDays(1).toString(Constant.YYYYMMDD);

        // 所有配置了基线告警的人群
        List<CrowdMonitorConfigBO> monitorConfigBOList = crowdMonitorConfigService.queryBySceneType(
            AlarmSceneTypeEnum.CROWD_BASE_LINE);
        if (CollectionUtils.isEmpty(monitorConfigBOList)) {
            return new ProcessResult(true);
        }
        Map<Long, CrowdMonitorConfigBO> validMonitorConfigMap = new HashMap<>();
        for (CrowdMonitorConfigBO configBO : monitorConfigBOList) {
            if (Objects.isNull(configBO) || !configBO.getIsValid() || CollectionUtils.isEmpty(configBO.getAlarmTypes())
                || CollectionUtils.isEmpty(configBO.getReceiverTypes())) {
                continue;
            }
            MonitorExtInfo extInfo = configBO.getExtInfo();
            if (Objects.isNull(extInfo) || StringUtils.isEmpty(extInfo.getBaseLineTime())
                || !Objects.equals(extInfo.getBaseLineTime(), time)) {
                continue;
            }
            validMonitorConfigMap.put(configBO.getCrowdId(), configBO);
        }
        if (validMonitorConfigMap.isEmpty()) {
            return new ProcessResult(true);
        }

        // 未在指定时间完成构建的人群
        List<CrowdMetaInfoDO> crowdMetaInfoDOList = crowdService.queryByIds(new ArrayList<>(validMonitorConfigMap.keySet()));
        if (CollectionUtils.isEmpty(crowdMetaInfoDOList)) {
            return new ProcessResult(true);
        }
        List<CrowdMetaInfoWithAlarmInfos> notCompleteCrowdDOList = new ArrayList<>();
        for (CrowdMetaInfoDO crowdMetaInfoDO : crowdMetaInfoDOList) {
            if (crowdMetaInfoDO.doesRealTime() || !crowdMetaInfoDO.doesNeedUpdate() || !crowdMetaInfoDO.isValid()) {
                continue;
            }
            if (StringUtils.isNotBlank(crowdMetaInfoDO.getBizDate()) && Objects.equals(crowdMetaInfoDO.getBizDate(), bizdate)
                && GroupStatusEnum.SUCCESS.equals(crowdMetaInfoDO.getCrowdStatus())) {
                continue;
            }

            CrowdMonitorConfigBO config = validMonitorConfigMap.get(crowdMetaInfoDO.getId());
            if (config == null || CollectionUtils.isEmpty(config.getAlarmTypes()) || CollectionUtils.isEmpty(config.getReceiverTypes())) {
                continue;
            }
            CrowdMetaInfoWithAlarmInfos crowdMetaInfoWithAlarmInfos = new CrowdMetaInfoWithAlarmInfos(crowdMetaInfoDO, config.getAlarmTypes());
            crowdMetaInfoWithAlarmInfos.setBaseLineTime(time);
            notCompleteCrowdDOList.add(crowdMetaInfoWithAlarmInfos);
        }

        // 按接收人聚合
        Map<String, Map<RoarAlarmTypeEnum, List<Long>>> baseLineReceiverMap = getCrowdReceiverMap(notCompleteCrowdDOList);
        if (baseLineReceiverMap.isEmpty()) {
            return new ProcessResult(true);
        }

        // 发送告警
        sendCrowdBaseLineAlarm(baseLineReceiverMap, time);
        return new ProcessResult(true);
    }

    private Map<String, Map<RoarAlarmTypeEnum, List<Long>>> getCrowdReceiverMap(List<CrowdMetaInfoWithAlarmInfos> crowdWithAlarmList) {
        Map<String, Map<RoarAlarmTypeEnum, List<Long>>> result = new HashMap<>();
        for (CrowdMetaInfoWithAlarmInfos crowdWithAlarm : crowdWithAlarmList) {
            CrowdMetaInfoDO crowdMetaInfoDO = crowdWithAlarm.getCrowdMetaInfoDO();
            List<RoarAlarmTypeEnum> alarmTypes = crowdWithAlarm.getAlarmTypes();
            if (Objects.isNull(crowdMetaInfoDO) || CollectionUtils.isEmpty(alarmTypes) || StringUtils.isEmpty(crowdWithAlarm.getBaseLineTime())) {
                continue;
            }

            List<Employee> alarmReceivers = crowdMonitorConfigService.getAlarmReceivers(crowdMetaInfoDO, AlarmSceneTypeEnum.CROWD_BASE_LINE);
            for (Employee receiver : alarmReceivers) {
                if (receiver == null || receiver.getEmpId() == null) {
                    continue;
                }

                for (RoarAlarmTypeEnum alarmType : alarmTypes) {
                    result.computeIfAbsent(receiver.getEmpId(), k -> new HashMap<>())
                        .computeIfAbsent(alarmType, k -> new ArrayList<>())
                        .add(crowdMetaInfoDO.getId());
                }
            }
        }
        return result;
    }

    private void sendCrowdBaseLineAlarm(Map<String, Map<RoarAlarmTypeEnum, List<Long>>> baseLineReceiverMap, String time) {
        if (baseLineReceiverMap.isEmpty()) {
            return;
        }

        AbstractCrowdAlarmServiceImpl alarmService = crowdAlarmFactory.choose(AlarmSceneTypeEnum.CROWD_BASE_LINE);
        if (Objects.isNull(alarmService)) {
            return;
        }
        for (Map.Entry<String, Map<RoarAlarmTypeEnum, List<Long>>> entry : baseLineReceiverMap.entrySet()) {
            String empId = entry.getKey();
            if (StringUtils.isEmpty(empId) || entry.getValue().isEmpty()) {
                continue;
            }

            for (Map.Entry<RoarAlarmTypeEnum, List<Long>> alarmTypeEntry : entry.getValue().entrySet()) {
                RoarAlarmTypeEnum alarmType = alarmTypeEntry.getKey();
                if (alarmType == null || alarmTypeEntry.getValue().isEmpty()) {
                    continue;
                }

                CrowdAlarmInfoBO crowdAlarmInfoBO = new CrowdAlarmInfoBO();
                crowdAlarmInfoBO.setSceneType(AlarmSceneTypeEnum.CROWD_BASE_LINE);
                crowdAlarmInfoBO.setAlarmType(alarmType);
                crowdAlarmInfoBO.setCrowdIds(alarmTypeEntry.getValue());
                crowdAlarmInfoBO.setReceiver(empId);
                crowdAlarmInfoBO.setBaseLineTime(time);
                alarmService.sendAlarm(crowdAlarmInfoBO);
            }
        }
    }
}
