package com.fliggy.picasso.schedulerx.job.profile;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.common.domain.profile.ProfileDagConfigVO;
import com.fliggy.picasso.common.enums.group.ProfileEnum;
import com.fliggy.picasso.common.enums.profile.ProfileDagStatusEnum;
import com.fliggy.picasso.group.crowd.dag.galaxy.utils.DateUtil;
import com.fliggy.picasso.service.profile.ProfileDagNodeConfigService;
import com.taobao.ateye.annotation.Switch;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class ProfileDagResultMonitorProcessor extends JavaProcessor {

    @Resource
    private ProfileDagNodeConfigService profileDagNodeConfigService;

    @Switch(description = "今日需要运行和监控的画像类型")
    public String needRunProfileTypes = "galaxy_glb,galaxy_lp,galaxy_bnb,galaxy_spu,galaxy_poi,galaxy_hotel,galaxy_shop,galaxy_item,tbup";

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        List<ProfileEnum> needRunProfileTypes = getNeedRunProfileTypes();
        List<ProfileDagConfigVO> profileDagConfigVOList = profileDagNodeConfigService.queryAllLatestConfigs();
        for (ProfileEnum profileEnum : needRunProfileTypes) {
            for (ProfileDagConfigVO dagConfig: profileDagConfigVOList) {
                if (!profileEnum.getId().equals(dagConfig.getProfileId())) {
                    continue;
                }

                if (!DateUtil.isToday(dagConfig.getGmtCreate())) {
                    return new ProcessResult(false, "今日画像[" + profileEnum.getDesc() + "]调度未执行");
                }

                ProfileDagStatusEnum dagStatus = dagConfig.getDagStatus();
                if (ProfileDagStatusEnum.isInit(dagStatus) || ProfileDagStatusEnum.isRunning(dagStatus)) {
                    return new ProcessResult(false, "今日画像[" + profileEnum.getDesc() + "]调度未完成");
                }

                if (ProfileDagStatusEnum.isFailed(dagStatus)) {
                    return new ProcessResult(false, "今日画像[" + profileEnum.getDesc() + "]调度失败");
                }

                break;
            }
        }
        return new ProcessResult(true);
    }

    /**
     * 获取需要运行和监控的画像类型
     */
    private List<ProfileEnum> getNeedRunProfileTypes() {
        List<ProfileEnum> profileEnumList = new ArrayList<>();
        List<String> needRunProfileTypeList = Arrays.asList(needRunProfileTypes.split(","));
        needRunProfileTypeList.forEach(profileCode -> {
            ProfileEnum profileEnum = ProfileEnum.fromCode(profileCode);
            if (profileEnum != null) {
                profileEnumList.add(profileEnum);
            }
        });
        return profileEnumList;
    }

}
