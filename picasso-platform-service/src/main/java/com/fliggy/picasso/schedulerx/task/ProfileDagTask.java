package com.fliggy.picasso.schedulerx.task;

import com.fliggy.picasso.common.enums.group.ProfileEnum;
import com.fliggy.picasso.common.enums.profile.ProfileDagStatusEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProfileDagTask {

    private ProfileEnum profile;

    private ProfileDagStatusEnum dagStatus;

    private String errorMsg;

    public ProfileDagTask(ProfileEnum profile, ProfileDagStatusEnum dagStatus, String errorMsg) {
        this.profile = profile;
        this.dagStatus = dagStatus;
        this.errorMsg = errorMsg;
    }
}
