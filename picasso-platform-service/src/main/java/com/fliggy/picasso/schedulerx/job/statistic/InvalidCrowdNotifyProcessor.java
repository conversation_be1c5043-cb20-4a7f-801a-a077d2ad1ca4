package com.fliggy.picasso.schedulerx.job.statistic;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.alarm.dingcard.*;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.enums.statistic.StatisticEntityTypeEnum;
import com.fliggy.picasso.common.statistic.OnlineStatisticInfoVO;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.service.crowd.impl.CrowdServiceImpl;
import com.fliggy.picasso.service.statistic.OnlineStatisticInfoService;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

import static com.fliggy.picasso.common.Constant.DTS_LOG;

@Component
public class InvalidCrowdNotifyProcessor extends JavaProcessor {

    private static final Logger log = LoggerFactory.getLogger(DTS_LOG);

    @Switch(description = "无效人群通知白名单")
    public String invalidCrowdNotifyWhiteList = "";
    @Switch(description = "是否调试发送钉钉消息")
    public boolean isDebugSendDingMsg = false;
    @Switch(description = "调试发送钉钉消息接收人")
    public String debugDingMsgReceiverEmpId = "395824";

    @Resource
    private OnlineStatisticInfoService onlineStatisticInfoService;
    @Resource
    private CrowdServiceImpl crowdService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            List<OnlineStatisticInfoVO> statisticInfos = onlineStatisticInfoService.findAllValidHeavyStatisticInfo(StatisticEntityTypeEnum.TAOBAO_USER);
            if (CollectionUtils.isNullOrEmpty(statisticInfos)) {
                return new ProcessResult(true);
            }

            // 过滤白名单，提取人群id
            List<Long> whiteList = null;
            if (StringUtils.isEmpty(invalidCrowdNotifyWhiteList)) {
                whiteList = new ArrayList<>();
            } else {
                whiteList = new ArrayList<>(Arrays.asList(invalidCrowdNotifyWhiteList.split(",")))
                        .stream().map(Long::parseLong).collect(Collectors.toList());
            }
            List<Long> finalWhiteList = whiteList;
            List<Long> crowdIdList = statisticInfos.stream()
                    .filter(info -> Objects.nonNull(info.getEntityId()) && !finalWhiteList.contains(info.getEntityId()))
                    .map(OnlineStatisticInfoVO::getEntityId).collect(Collectors.toList());

            // 获取人群id和负责人关系map，合并通知
            List<CrowdMetaInfoDO> crowdMetaInfoDOList = crowdService.queryByIds(crowdIdList);
            Map<String, List<Long>> crowdOwnerMap = crowdMetaInfoDOList.stream().collect(Collectors.groupingBy(
                    crowd -> crowd.getCreator().getEmpId(),
                    Collectors.mapping(CrowdMetaInfoDO::getId, Collectors.toList())));

            // 通知负责人下线人群
            crowdOwnerMap.forEach(this::sendDingMsg);
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("InvalidCrowdNotifyProcessor error", e);
            return new ProcessResult(false, e.getMessage());
        }
    }

    private void sendDingMsg(String empId, List<Long> crowdIds) {
        if (StringUtils.isEmpty(empId) || CollectionUtils.isNullOrEmpty(crowdIds)) {
            return;
        }

        if (isDebugSendDingMsg) {
            empId = debugDingMsgReceiverEmpId;
        }

        String msg = String.format("###  诸葛无效大人群告警:  \n  您有几个大人群(量级超5亿)，近一个月内线上无请求流量，请及时操作人群下线或缩短人群过期时间。  \n  人群id：**%s**  \n  加白请到诸葛答疑群@优比"
                , JSON.toJSONString(crowdIds));
        DingTalkReq dingTalkReq = DingTalkReq.builder().empId(empId).msg(msg).title("诸葛大人群下线通知").build();
        DingTalkNotifyManager.sendDingMsg(dingTalkReq, DingTalkEventTypeEnum.INVALID_CROWD);
    }

    @AteyeInvoker(description = "发送钉钉消息", paraDesc = "empId&crowdIds")
    public void sendDingMsgAteye(String empId, String crowdIds) {
        List<Long> crowdIdList = new ArrayList<>(Arrays.asList(crowdIds.split(",")))
                .stream().map(Long::valueOf).collect(Collectors.toList());
        sendDingMsg(empId, crowdIdList);
    }
}
