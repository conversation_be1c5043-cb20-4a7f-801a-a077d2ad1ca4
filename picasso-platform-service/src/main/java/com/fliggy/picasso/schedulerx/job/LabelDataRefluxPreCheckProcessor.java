package com.fliggy.picasso.schedulerx.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.alarm.dingtalk.DingDingNotifyBuilder;
import com.fliggy.picasso.alarm.dingtalk.DingDingNotifySender;
import com.fliggy.picasso.alarm.mail.MailService;
import com.fliggy.picasso.alarm.mail.domain.MailMessageDO;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.enums.label.LabelStatusEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.common.odps.OdpsGuid;
import com.fliggy.picasso.common.utils.ContextUtils;
import com.fliggy.picasso.offline.OdpsMetaService;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.fliggy.picasso.service.employee.EmployeeCommonService;
import com.fliggy.picasso.service.label.LabelInfoParameter;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoService;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.fliggy.picasso.common.Constant.*;

/**
 * 标签数据回流时的巡检任务。诸葛中的标签数据回流(odps/hologres)时，需要保证已上线的所有离线标签所在的odps源表数据是OK的。
 * 当有个别质量参差不齐的都上线时，及时识别出产出异常的标签，将其下线，并通过钉钉通知标签owner，督促其及时跟进保障处理数据产出任务。
 *
 * <AUTHOR>
 */
@Component
public class LabelDataRefluxPreCheckProcessor extends JavaProcessor {
    private static final Logger logger = LoggerFactory.getLogger(SERVICE_LOG);

    @Switch(description = "诸葛标签管理员列表")
    public static String ZHUGE_ADMIN_EMPID_LIST = "395824";

    private final PhysicalProfileInfoService physicalProfileInfoService;

    @Autowired
    private LabelInfoService labelInfoService;
    private final DingDingNotifySender dingDingNotifySender;
    private final MailService mailService;
    private final EmployeeCommonService employeeCommonService;
    private final OdpsMetaService odpsMetaService;
    private final CrowdService crowdService;

    @Autowired
    public LabelDataRefluxPreCheckProcessor(PhysicalProfileInfoService physicalProfileInfoService,
                                            DingDingNotifySender dingDingNotifySender,
                                            MailService mailService,
                                            EmployeeCommonService employeeCommonService,
                                            OdpsMetaService odpsMetaService, CrowdService crowdService) {
        this.physicalProfileInfoService = physicalProfileInfoService;
        this.dingDingNotifySender = dingDingNotifySender;
        this.mailService = mailService;
        this.employeeCommonService = employeeCommonService;
        this.odpsMetaService = odpsMetaService;
        this.crowdService = crowdService;
    }

    @Switch(name = "offlineLabelWhenException", description = "离线odps标签数据分区异常时是否下线标签")
    private boolean offlineLabelWhenException = true;

    @Switch(description = "标签数据延迟产出告警发出n天后下线标签，默认为2")
    private int reserveProcessDays = 2;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {

            Map<PhysicalProfileInfoDTO, List<LabelInfoDTO>> refluxProfileAndLabels = physicalProfileInfoService
                    .queryRefluxProfileAndLabels();

            refluxProfileAndLabels.forEach(
                    (profile, labels) -> {
                        Map<OdpsGuid, List<LabelInfoDTO>> odpsGuidListMap = LabelInfoService.groupLabelsByGuid(labels);
                        checkLabelDataIsDelay(odpsGuidListMap);
                    }
            );

            return new ProcessResult(true);
        } catch (Exception e) {
            logger.error("Failed check reflux labels", e);
            return new ProcessResult(false);
        }
    }

    /**
     * 检查标签数据是否延迟产出： 日更新：T-1~T-2分区不存在，则告警 周更新：T-1~T-7分区不存在，则告警
     * <p>
     * 在上面的基础上，如果继续延迟两天则下线标签 以上均发送钉钉告警（个人&群）&邮件
     *
     * @param odpsGuidToLabels ODPS表和标签的映射关系
     */
    private void checkLabelDataIsDelay(Map<OdpsGuid, List<LabelInfoDTO>> odpsGuidToLabels) {
        if (MapUtils.isEmpty(odpsGuidToLabels)) {
            return;
        }

        Map<OdpsGuid, List<LabelInfoDTO>> alarmLabels = Maps.newHashMap();
        Map<OdpsGuid, List<LabelInfoDTO>> offlineLabels = Maps.newHashMap();

        for (Map.Entry<OdpsGuid, List<LabelInfoDTO>> entry : odpsGuidToLabels.entrySet()) {
            int alarmThreshold = 1;
            int offlineThreshold = alarmThreshold + reserveProcessDays;

            OdpsGuid odpsGuid = entry.getKey();
            String latestUpdatePeriod = getLatestUpdatePeriod(entry.getValue());
            int delay = odpsMetaService.getDelayDaysFromNow(odpsGuid);

            if (delay < 0) {
                offlineLabels.put(odpsGuid, entry.getValue());
            }

            if (delay == 0) {
                continue;
            }

            switch (latestUpdatePeriod) {
                case DAY:
                    if (delay >= alarmThreshold && delay < offlineThreshold) {
                        alarmLabels.put(odpsGuid, entry.getValue());
                    } else {
                        offlineLabels.put(odpsGuid, entry.getValue());
                    }
                    break;
                case WEEK:
                    alarmThreshold = 6;
                    offlineThreshold = alarmThreshold + reserveProcessDays;
                    if (delay >= alarmThreshold && delay < offlineThreshold) {
                        alarmLabels.put(odpsGuid, entry.getValue());
                    } else if (delay >= offlineThreshold) {
                        offlineLabels.put(odpsGuid, entry.getValue());
                    }
                    break;
                default:
                    throw new ParamErrorException(String.format("检查标签数据是否延迟产出出错：不支持的更新周期[%s]", latestUpdatePeriod));
            }
        }
        alarmLabel(alarmLabels);
        offlineLabel(offlineLabels);
    }

    /**
     * 告警异常标签
     *
     * @param labels 待告警标签
     */
    private void alarmLabel(Map<OdpsGuid, List<LabelInfoDTO>> labels) {
        buildNotify(ALARM, labels);
    }

    /**
     * 下线异常标签，如未关联活跃人群则直接下线
     *
     * @param labels 待下线标签列表
     */
    private void offlineLabel(Map<OdpsGuid, List<LabelInfoDTO>> labels) {
        if (offlineLabelWhenException) {
            for (Map.Entry<OdpsGuid, List<LabelInfoDTO>> entry : labels.entrySet()) {
                List<LabelInfoDTO> labelList = entry.getValue();
                for (LabelInfoDTO labelInfoDTO : labelList) {
                    if (CollectionUtils.isEmpty(isActivateCrowdUse(labelInfoDTO))) {
                        labelInfoService.updateStatus(labelInfoDTO.getId(), LabelStatusEnum.OFFLINE);
                    }
                }
            }
        }

        buildNotify(OFFLINE, labels);
    }

    @AteyeInvoker(description = "手动检查所有标签关联人群")
    public void checkAllActivateLabel() {
        LabelInfoParameter parameter = new LabelInfoParameter();
        List<LabelInfoDTO> labelList = labelInfoService.query(parameter);
        for (LabelInfoDTO label : labelList) {
            logger.info("[LABEL CROWD RELATION] {}({})[{}]: {}",label.getName(), label.getCode(), label.getStatus(), isActivateCrowdUse(label));
        }
    }

    /**
     * 手动判断标签是否关联活跃（未删除、未过期）人群
     *
     * @param code 标签code
     * @return 关联的人群列表
     */
    @AteyeInvoker(description = "手动判断标签是否关联活跃（未删除、未过期）人群", paraDesc = "标签code")
    public List<Long> isActivateCrowdUseManually(String code) {
        LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(code);
        if (labelInfoDTO == null) {
            Ateye.out.println(String.format("标签[%s]不存在！", code));
            return new ArrayList<>();
        }

        return isActivateCrowdUse(labelInfoDTO);
    }

    /**
     * 检查是否关联活跃人群
     *
     * @param labelInfoDTO 标签元数据
     * @return
     */
    private List<Long> isActivateCrowdUse(LabelInfoDTO labelInfoDTO) {
        List<Long> result = new ArrayList<>();
        if (labelInfoDTO == null) {
            return result;
        }

        CrowdMetaInfoDO query = new CrowdMetaInfoDO();
        query.setDeleted((byte) 0);

        List<CrowdMetaInfoDO> crowds = crowdService.pageQuery(new CrowdMetaInfoDO(), 1, Integer.MAX_VALUE);
        if (CollectionUtils.isEmpty(crowds)) {
            return result;
        }


        for (CrowdMetaInfoDO crowdMetaInfoDO : crowds) {
            if (crowdMetaInfoDO.useLabel(labelInfoDTO.getCode())) {
                result.add(crowdMetaInfoDO.getId());
            }
        }

        return result;
    }

    /**
     * 构建通知信息（钉钉&邮件）
     *
     * @param operateType 操作类型
     * @param validLabels 异常标签列表
     */
    private void buildNotify(String operateType, Map<OdpsGuid, List<LabelInfoDTO>> validLabels) {
        Map<String, Map<String, String>> ownerInfo = buildOwnerToLabelInfo(validLabels);
        alarm(ownerInfo, operateType);
        sendNotifyToDingdingGroup(validLabels, operateType);
    }

    /**
     * 生成责任人和其负责的标签之间的映射关系
     *
     * @param validLabels 异常标签列表
     * @return {key: empId, value: {key: labelName, value: odpsGuid}}
     */
    private Map<String, Map<String, String>> buildOwnerToLabelInfo(Map<OdpsGuid, List<LabelInfoDTO>> validLabels) {
        Map<String, Map<String, String>> result = Maps.newHashMap();
        if (MapUtils.isEmpty(validLabels)) {
            return result;
        }

        List<LabelInfoDTO> validLabelList = new ArrayList<>();
        for (Map.Entry<OdpsGuid, List<LabelInfoDTO>> entry : validLabels.entrySet()) {
            validLabelList.addAll(entry.getValue());
        }

        return buildOwnerInfo(validLabelList);
    }

    private Map<String, Map<String, String>> buildOwnerInfo(List<LabelInfoDTO> labelList) {
        Map<String, Map<String, String>> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(labelList)) {
            return result;
        }

        for (LabelInfoDTO labelInfoDTO : labelList) {
            String labelNameCn = labelInfoDTO.getName();
            String sourceGuid = labelInfoDTO.getOdpsSourceConfig().getGuid().getGuid();
            if (Objects.nonNull(labelInfoDTO.getCreator())) {
                String creator = labelInfoDTO.getCreator().getEmpId();
                buildOwnerInfo(result, creator, labelNameCn, sourceGuid);
            }
            if (Objects.nonNull(labelInfoDTO.getDataOwner())) {
                String dataOwner = labelInfoDTO.getDataOwner().getEmpId();
                buildOwnerInfo(result, dataOwner, labelNameCn, sourceGuid);
            }
            if (Objects.nonNull(labelInfoDTO.getBizOwner())) {
                String bizOwner = labelInfoDTO.getBizOwner().getEmpId();
                buildOwnerInfo(result, bizOwner, labelNameCn, sourceGuid);
            }
            if (Objects.nonNull(labelInfoDTO.getQualityOwner())) {
                String qualityOwner = labelInfoDTO.getQualityOwner().getEmpId();
                buildOwnerInfo(result, qualityOwner, labelNameCn, sourceGuid);
            }
        }

        return result;
    }

    private void buildOwnerInfo(Map<String, Map<String, String>> ownerInfo, String owner, String labelName,
                                String sourceGuid) {
        Map<String, String> labelInfo;
        if (ownerInfo.containsKey(owner)) {
            labelInfo = ownerInfo.get(owner);
            if (!labelInfo.containsKey(labelName)) {
                labelInfo.put(labelName, sourceGuid);
            }
            ownerInfo.put(owner, labelInfo);
        } else {
            labelInfo = Maps.newHashMap();
            labelInfo.put(labelName, sourceGuid);

        }
        ownerInfo.put(owner, labelInfo);
    }

    /**
     * 获取同标签表中最慢的更新周期
     *
     * @param labels 标签列表
     * @return 更新周期
     */
    private String getLatestUpdatePeriod(List<LabelInfoDTO> labels) {
        for (LabelInfoDTO label : labels) {
            if (label.getUpdatePeriod().equals(WEEK)) {
                return WEEK;
            }
        }
        return DAY;
    }

    private void alarm(Map<String, Map<String, String>> ownerInfo, String operateType) {
        if (MapUtils.isEmpty(ownerInfo)) {
            return;
        }

        for (Map.Entry<String, Map<String, String>> entry : ownerInfo.entrySet()) {
            String empId = entry.getKey();

            StringBuilder alarmMsg = new StringBuilder();
            if (ALARM.equals(operateType)) {
                alarmMsg.append(
                        String.format("系统检测您负责的以下诸葛标签，已延迟产出，请尽快处理，若继续延迟产出，%d天后系统将自动下线该标签。%n", reserveProcessDays));
            } else {
                alarmMsg.append("系统检测您负责的以下诸葛标签产出已严重延迟，标签已下线，请恢复数据后联系管理员重新上架。\n");
            }
            for (Map.Entry<String, String> label2Guid : entry.getValue().entrySet()) {
                alarmMsg.append(String.format("%s: %s%n", label2Guid.getKey(), label2Guid.getValue()));
            }

            DingDingNotifyBuilder.newBuilder()
                    .setReceivers(Lists.newArrayList(empId))
                    .setUrl(ZHUGE_LABEL_PAGE)
                    .setTitle("标签数据产出时效校验")
                    .setContent(alarmMsg.toString())
                    .appendToFormMap("执行节点：", ContextUtils.getHostName())
                    .send();

            sendNotifyByMail(empId, alarmMsg.toString());
        }
    }

    private void sendNotifyByMail(String empId, String msg) {
        if (StringUtils.isBlank(empId) || StringUtils.isBlank(msg)) {
            return;
        }

        List<String> receiversMailAddressList = new ArrayList<>();
        String ownerMailAddress = employeeCommonService.getUserMailAddress(empId);
        if (StringUtils.isNotBlank(ownerMailAddress)) {
            receiversMailAddressList.add(ownerMailAddress);
        }

        for (String admin : ZHUGE_ADMIN_EMPID_LIST.split(COMMA)) {
            String adminMailAddress = employeeCommonService.getUserMailAddress(admin);
            if (StringUtils.isNotBlank(adminMailAddress) && !receiversMailAddressList.contains(adminMailAddress)) {
                receiversMailAddressList.add(adminMailAddress);
            }
        }

        msg += String.format("%s%s%s%s诸葛平台%s系统发出，请勿回复。", LINE_FEED, LINE_FEED, LINE_FEED, LINE_FEED, LINE_FEED);

        MailMessageDO message = new MailMessageDO();
        message.setContent(msg);
        message.setSubject("诸葛标签告警");
        message.setSender(ZHUGE_MAIL);
        message.setReceivers(receiversMailAddressList);
        mailService.sendMail(message);
    }

    private void sendNotifyToDingdingGroup(Map<OdpsGuid, List<LabelInfoDTO>> exceptOdpsGuidToLabels,
                                           String operateType) {
        StringBuilder msg = new StringBuilder();
        if (MapUtils.isNotEmpty(exceptOdpsGuidToLabels)) {
            if (ALARM.equals(operateType)) {
                msg.append("以下标签已触发延迟告警，若继续延迟产出，则将下线：").append(LINE_FEED);
            } else {
                msg.append("以下标签已被下线，请关注：").append(LINE_FEED);
            }

            for (Map.Entry<OdpsGuid, List<LabelInfoDTO>> entry : exceptOdpsGuidToLabels.entrySet()) {
                msg.append(String.format("ODPS表:%s, 标签：", entry.getKey()));

                List<LabelInfoDTO> labelDos = entry.getValue();
                Set<String> propertyNames = labelDos.stream().map(LabelInfoDTO::getName).collect(
                        Collectors.toSet());
                msg.append(Joiner.on(COMMA).join(propertyNames)).append(LINE_FEED);
            }
        } else {
            if (ALARM.equals(operateType)) {
                msg.append("暂无延迟产出预警标签。");
            } else {
                msg.append("暂无延迟产出下线标签。");
            }
        }

        dingDingNotifySender.sendDingDingGroupMsg(msg.toString());
    }
}
