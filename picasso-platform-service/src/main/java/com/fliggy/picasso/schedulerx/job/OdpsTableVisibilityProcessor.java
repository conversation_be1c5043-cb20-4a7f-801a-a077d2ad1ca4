package com.fliggy.picasso.schedulerx.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.common.domain.CrowdExportTaskDO;
import com.fliggy.picasso.common.domain.crowd.export.CrowdExportExtInfo;
import com.fliggy.picasso.common.enums.OdpsTableVisibilityUpdateType;
import com.fliggy.picasso.common.enums.crowd.CrowdExportTaskStatus;
import com.fliggy.picasso.common.enums.crowd.CrowdIdMappingTaskStatusEnum;
import com.fliggy.picasso.entity.vo.CrowdIdMappingTaskVO;
import com.fliggy.picasso.group.crowd.dag.galaxy.utils.DateUtil;
import com.fliggy.picasso.openapi.AliyunOpenApiService;
import com.fliggy.picasso.service.crowd.CrowdIdMappingTaskService;
import com.fliggy.picasso.service.crowd.export.CrowdExportService;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;

/**
 * <AUTHOR>
 */
@Component
public class OdpsTableVisibilityProcessor extends MapJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);

    @Resource
    private AliyunOpenApiService aliyunOpenApiService;

    @Resource
    private CrowdExportService crowdExportService;
    @Resource
    private CrowdIdMappingTaskService crowdIdMappingTaskService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String taskName = context.getTaskName();
        Object task = context.getTask();
        try {
            String level1Dispatch = "Level1Dispatch";
            List<OdpsTableVisibilityTask> taskList = null;
            if (isRootTask(context)) {
                String param = StringUtils.isNotEmpty(context.getInstanceParameters()) ? context.getInstanceParameters() : context.getJobParameters();
                if (StringUtils.isNotEmpty(param)) {
                    taskList = new ArrayList<>();
                    String[] params = param.split("-");
                    if (OdpsTableVisibilityUpdateType.CROWD_EXPORT.getCode().equals(params[0])) {
                        CrowdExportTaskDO taskDO = crowdExportService.queryById(Long.parseLong(params[1]));
                        taskList.add(new OdpsTableVisibilityTask(OdpsTableVisibilityUpdateType.CROWD_EXPORT, taskDO, getCrowdExportOdpsTableName(taskDO)));
                    } else if(OdpsTableVisibilityUpdateType.ID_MAPPING.getCode().equals(params[0])) {
                        CrowdIdMappingTaskVO taskVO = crowdIdMappingTaskService.queryById(Long.parseLong(params[1]));
                        taskList.add(new OdpsTableVisibilityTask(OdpsTableVisibilityUpdateType.ID_MAPPING, taskVO, getCrowdIdMappingOdpsTableName(taskVO)));
                    }else {
                        return new ProcessResult(false, "参数错误：不支持的任务类型" + param);
                    }
                } else {
                    taskList = generateAllNeedUpdateVisibilityTask();
                }

                if (CollectionUtils.isEmpty(taskList)) {
                    return new ProcessResult(true);
                }

                log.info(String.format("【odps表可见性更新】分布式调度任务数：%s", taskList.size()));
                return map(taskList, level1Dispatch);
            } else if (level1Dispatch.equals(taskName)) {
                OdpsTableVisibilityTask odpsTableVisibilityTask = (OdpsTableVisibilityTask) task;
                boolean visible = updateTableVisibility(odpsTableVisibilityTask.getOdpsTableName());
                if (visible) {
                    updateVisibilityRecord(odpsTableVisibilityTask);
                }
                return new ProcessResult(true);
            } else {
                log.error(String.format("【odps表可见性更新】不支持的任务类型: %s", taskName));
                return new ProcessResult(false, "不支持的任务类型" + taskName);
            }
        } catch (Exception ex) {
            log.error("【odps表可见性更新】更新表可见性失败", ex);
            return new ProcessResult(false, ex.getMessage());
        }
    }

    @Data
    @AllArgsConstructor
    public static class OdpsTableVisibilityTask {
        private OdpsTableVisibilityUpdateType updateType;
        private Object task;
        private String odpsTableName;
    }

    private List<OdpsTableVisibilityTask> generateAllNeedUpdateVisibilityTask() {
        List<OdpsTableVisibilityTask> needUpdateVisibilityTasks = new ArrayList<>();
        List<CrowdExportTaskDO> crowdExportTaskDOList = crowdExportService.queryByStatus(CrowdExportTaskStatus.SUCCESS);
        if (CollectionUtils.isEmpty(crowdExportTaskDOList)) {
            return needUpdateVisibilityTasks;
        }

        crowdExportTaskDOList.forEach(task -> {
            if (Objects.nonNull(task.getExtInfo())) {
                CrowdExportExtInfo crowdExportExtInfo = JSON.parseObject(task.getExtInfo(), CrowdExportExtInfo.class);
                if (DateUtil.isToday(task.getGmtModified()) && !crowdExportExtInfo.isVisible()) {
                    needUpdateVisibilityTasks.add(new OdpsTableVisibilityTask(OdpsTableVisibilityUpdateType.CROWD_EXPORT, task, getCrowdExportOdpsTableName(task)));
                }
            }
        });

        List<CrowdIdMappingTaskVO> idMappingTaskVOList = crowdIdMappingTaskService.queryByStatus(Collections.singletonList(CrowdIdMappingTaskStatusEnum.SUCCESS));
        if (CollectionUtils.isEmpty(idMappingTaskVOList)) {
            return needUpdateVisibilityTasks;
        }

        idMappingTaskVOList.forEach(task -> {
            if (Objects.nonNull(task.getExtInfo())) {
                CrowdIdMappingTaskVO.CrowdIdMappingTaskExtInfo crowdIdMappingTaskExtInfo = task.getExtInfo();
                if (DateUtil.isToday(task.getGmtModified()) &&!crowdIdMappingTaskExtInfo.isVisible()) {
                    needUpdateVisibilityTasks.add(new OdpsTableVisibilityTask(OdpsTableVisibilityUpdateType.ID_MAPPING, task, getCrowdIdMappingOdpsTableName(task)));
                }
            }
        });
        return needUpdateVisibilityTasks;
    }

    private String getCrowdExportOdpsTableName(CrowdExportTaskDO taskDO) {
        return crowdExportService.getExportTableName(taskDO.getExportCrowdId());
    }

    private String getCrowdIdMappingOdpsTableName(CrowdIdMappingTaskVO taskVO) {
        return crowdIdMappingTaskService.getOdpsTableName(taskVO);
    }

    @AteyeInvoker(description = "手动更新表可见性", paraDesc = "tableName")
    public boolean updateTableVisibility(String tableName) {
        String guid = "odps." + tableName;
        boolean visible = aliyunOpenApiService.updateMetaTable(guid, 1);
        log.info("【odps表可见性更新】更新表可见性，table：{}, visible result: {}", guid, visible);
        return visible;
    }

    private void updateVisibilityRecord(OdpsTableVisibilityTask odpsTableVisibilityTask) {
        if (OdpsTableVisibilityUpdateType.CROWD_EXPORT == odpsTableVisibilityTask.getUpdateType()) {
            CrowdExportTaskDO taskDO = (CrowdExportTaskDO) odpsTableVisibilityTask.getTask();
            CrowdExportExtInfo crowdExportExtInfo = JSON.parseObject(taskDO.getExtInfo(), CrowdExportExtInfo.class);
            crowdExportExtInfo.setVisible(true);

            CrowdExportTaskDO update = new CrowdExportTaskDO();
            update.setId(taskDO.getId());
            update.setExtInfo(JSON.toJSONString(crowdExportExtInfo));
            crowdExportService.update(update);
        } else if (OdpsTableVisibilityUpdateType.ID_MAPPING == odpsTableVisibilityTask.getUpdateType()) {
            CrowdIdMappingTaskVO taskVO = (CrowdIdMappingTaskVO) odpsTableVisibilityTask.getTask();
            taskVO.getExtInfo().setVisible(true);

            CrowdIdMappingTaskVO update = new CrowdIdMappingTaskVO();
            update.setId(taskVO.getId());
            update.setExtInfo(taskVO.getExtInfo());
            crowdIdMappingTaskService.update(update);
        } else {
            throw new RuntimeException("不支持的任务类型" + odpsTableVisibilityTask.getUpdateType());
        }
    }
}
