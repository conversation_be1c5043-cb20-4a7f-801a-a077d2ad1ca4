package com.fliggy.picasso.schedulerx.job.alarm;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoQuery;
import com.fliggy.picasso.common.domain.crowd.build.CrowdBuildDagConfigDO;
import com.fliggy.picasso.common.enums.group.GroupBuildDagStatusEnum;
import com.fliggy.picasso.common.enums.group.GroupStatusEnum;
import com.fliggy.picasso.common.enums.group.ProfileEnum;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.config.SwitchConfig;
import com.fliggy.picasso.group.crowd.dag.CrowdBuildDagConfigService;
import com.fliggy.picasso.service.crowd.CrowdCircleService;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.taobao.ateye.annotation.AteyeInvoker;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DTS_LOG;

/**
 * 人群连续7天失败，升级处理成不更新人群
 */
@Component
public class CrowdFailedUpgradeProcessor extends MapJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DTS_LOG);

    @Resource
    private CrowdService crowdService;
    @Resource
    private CrowdCircleService crowdCircleService;
    @Resource
    private CrowdBuildDagConfigService crowdBuildDagConfigService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String level1Dispatch = "Level1Dispatch";
        String taskName = context.getTaskName();
        Object task = context.getTask();
        try {
            if (isRootTask(context)) {
                List<Long> crowdIdList = null;
                String param = StringUtils.isNotEmpty(context.getInstanceParameters()) ?
                    context.getInstanceParameters() : context.getJobParameters();
                if (StringUtils.isNotBlank(param)) {
                    crowdIdList = Arrays.stream(param.split(",")).map(Long::parseLong).collect(Collectors.toList());
                } else {
                    crowdIdList = queryOfflineErrorCrowds();
                }

                if (CollectionUtils.isEmpty(crowdIdList)) {
                    return new ProcessResult(true);
                }
                return map(crowdIdList, level1Dispatch);
            } else if (level1Dispatch.equals(taskName)) {
                Long crowdId = (Long) task;
                if (isFailedIn3Days(crowdId)) {
                    return new ProcessResult(crowdCircleService.updateUpdateType(crowdId, false));
                }
                return new ProcessResult(true);
            } else {
                return new ProcessResult(false);
            }
        } catch (Exception e) {
            log.error("CrowdErrorUpgradeProcessor process error", e);
            return new ProcessResult(false, e.getMessage());
        }
    }

    /**
     * 连续7天失败，没有成功过
     * @param crowdId
     * @return
     */
    @AteyeInvoker(description = "连续7天失败，没有成功过", paraDesc = "crowdId")
    public boolean isFailedIn7Days(Long crowdId) {
        List<CrowdBuildDagConfigDO> dagConfigDOList = crowdBuildDagConfigService.queryByCrowdIdAndProfileId(
            crowdId, ProfileEnum.TAOBAO_USER.getId());
        if (CollectionUtils.isEmpty(dagConfigDOList)) {
            return false;
        }

        // 遍历近7天，逐天检查是否构建失败
        for (int i = 0; i < 7; i++) {
            String dayStr = DateUtils.getDateStr(-i);

            // 筛选出当天的DAG配置
            List<CrowdBuildDagConfigDO> dailyConfigList = dagConfigDOList.stream()
                .filter(configDO -> DateUtils.formatDateToStr(configDO.getGmtCreate(), DateUtils.YMD_FORMAT).equals(dayStr))
                .collect(Collectors.toList());

            // 如果当天没有配置，或者当天有成功的构建，则返回false
            if (CollectionUtils.isEmpty(dailyConfigList) ||
                dailyConfigList.stream().anyMatch(configDO -> configDO.getDagStatus() == GroupBuildDagStatusEnum.SUCCESS)) {
                return false;
            }
        }
        return true;
    }


    public boolean isFailedIn3Days(Long crowdId) {
        List<CrowdBuildDagConfigDO> dagConfigDOList = crowdBuildDagConfigService.queryByCrowdIdAndProfileId(
            crowdId, ProfileEnum.TAOBAO_USER.getId());
        if (CollectionUtils.isEmpty(dagConfigDOList)) {
            return false;
        }

        String day2Before = DateUtils.getDateStr(-2);
        List<CrowdBuildDagConfigDO> day2BeforeConfigList = dagConfigDOList.stream().filter(
            configDO -> DateUtils.formatDateToStr(configDO.getGmtCreate(), DateUtils.YMD_FORMAT)
                .equals(day2Before)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(day2BeforeConfigList)) {
            return false;
        }
        if (day2BeforeConfigList.stream().anyMatch(configDO -> configDO.getDagStatus() == GroupBuildDagStatusEnum.SUCCESS)) {
            return false;
        }

        String yesterday = DateUtils.getDateStr(-1);
        List<CrowdBuildDagConfigDO> yesterdayConfigList = dagConfigDOList.stream().filter(
            configDO -> DateUtils.formatDateToStr(configDO.getGmtCreate(), DateUtils.YMD_FORMAT)
                .equals(yesterday)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(yesterdayConfigList)) {
            return false;
        }
        if (yesterdayConfigList.stream().anyMatch(configDO -> configDO.getDagStatus() == GroupBuildDagStatusEnum.SUCCESS)) {
            return false;
        }

        String today = DateUtils.getDateStr(0);
        List<CrowdBuildDagConfigDO> todayConfigList = dagConfigDOList.stream().filter(
            configDO -> DateUtils.formatDateToStr(configDO.getGmtCreate(), DateUtils.YMD_FORMAT)
                .equals(today)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(todayConfigList)) {
            return false;
        }
        return todayConfigList.stream().noneMatch(configDO -> configDO.getDagStatus() == GroupBuildDagStatusEnum.SUCCESS);
    }

    /**
     * 查询所有离线并且状态可能为失败的人群，失败人群自动重跑
     * @return
     */
    private List<Long> queryOfflineErrorCrowds() {
        CrowdMetaInfoQuery query = new CrowdMetaInfoQuery();
        if (SwitchConfig.COMMON_DEBUG_SWITCH) {
            query.setCreator(SwitchConfig.ALARM_DEBUG_CREATOR_NAME);
        }
        query.setDeleted((byte)0);
        query.setExpiredDateStart(new Date());
        query.setRealTime((byte)0);
        query.setCrowdStatusList(Arrays.asList(GroupStatusEnum.INIT, GroupStatusEnum.RUNNING, GroupStatusEnum.ERROR));
        return crowdService.queryAllIdByInfoQuery(query);
    }
}
