package com.fliggy.picasso.schedulerx.job.alarm;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoQuery;
import com.fliggy.picasso.common.enums.alarm.AlarmSceneTypeEnum;
import com.fliggy.picasso.common.enums.alarm.RoarAlarmTypeEnum;
import com.fliggy.picasso.common.enums.crowd.CrowdTypeEnum;
import com.fliggy.picasso.common.odps.OdpsGuid;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.config.SwitchConfig;
import com.fliggy.picasso.entity.alarm.CrowdMetaInfoWithAlarmInfos;
import com.fliggy.picasso.entity.bo.CrowdAlarmInfoBO;
import com.fliggy.picasso.entity.bo.CrowdMonitorConfigBO;
import com.fliggy.picasso.offline.OdpsMetaService;
import com.fliggy.picasso.service.alarm.CrowdAlarmFactory;
import com.fliggy.picasso.service.alarm.CrowdMonitorConfigService;
import com.fliggy.picasso.service.alarm.impl.AbstractCrowdAlarmServiceImpl;
import com.fliggy.picasso.service.crowd.CrowdCircleService;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class CrowdBuildErrorAlarmProcessor extends JavaProcessor {

    @Resource
    private CrowdService crowdService;
    @Resource
    private CrowdCircleService crowdCircleService;
    @Resource
    private OdpsMetaService odpsMetaService;
    @Resource
    private CrowdMonitorConfigService crowdMonitorConfigService;
    @Resource
    private CrowdAlarmFactory crowdAlarmFactory;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        // 查询线上有效标签/组合人群
        CrowdMetaInfoQuery query = new CrowdMetaInfoQuery();
        if (SwitchConfig.COMMON_DEBUG_SWITCH) {
            query.setCreator(SwitchConfig.ALARM_DEBUG_CREATOR_NAME);
        }
        query.setNeedUpdate((byte)1);
        query.setDeleted((byte)0);
        query.setExpiredDateStart(new Date());
        query.setCrowdTypeList(Arrays.asList(CrowdTypeEnum.LABEL_CROWD, CrowdTypeEnum.OPERATE_CROWD, CrowdTypeEnum.OPERATE_EXPRESSION_CROWD));
        List<CrowdMetaInfoDO> needCheckCrowdDOList = crowdService.listQuery(query, 1, 10000);
        Map<CrowdMetaInfoDO, String> labelOrOperateCrowdErrorMap = getLabelOrOperateCrowdErrorMap(needCheckCrowdDOList);

        // 查询线上有效odps日更新人群
        query = new CrowdMetaInfoQuery();
        if (SwitchConfig.COMMON_DEBUG_SWITCH) {
            query.setCreator(SwitchConfig.ALARM_DEBUG_CREATOR_NAME);
        }
        query.setNeedUpdate((byte)1);
        query.setDeleted((byte)0);
        query.setExpiredDateStart(new Date());
        query.setCrowdTypeList(Collections.singletonList(CrowdTypeEnum.ODPS_TABLE_CROWD));
        List<CrowdMetaInfoDO> odpsCrowdDOList = crowdService.listQuery(query, 1, 10000);
        Map<CrowdMetaInfoDO, String> odpsCrowdErrorMap = getOdpsCrowdErrorMap(odpsCrowdDOList);

        // 合并、过滤
        Map<CrowdMetaInfoDO, String> crowdErrorMap = mergeCrowdErrorMap(labelOrOperateCrowdErrorMap, odpsCrowdErrorMap);
        if (crowdErrorMap.isEmpty()) {
            return new ProcessResult(true);
        }
        List<CrowdMetaInfoWithAlarmInfos> validCrowdErrorList = filterNonAlarmConfigCrowd(crowdErrorMap);
        if (validCrowdErrorList.isEmpty()) {
            return new ProcessResult(true);
        }

        // 按接收人聚合
        Map<String, Map<RoarAlarmTypeEnum, Map<Long, String>>> crowdErrorReceiverMap = getCrowdReceiverMap(validCrowdErrorList);
        if (crowdErrorReceiverMap.isEmpty()) {
            return new ProcessResult(true);
        }

        // 发送告警
        sendCrowdBuildErrorAlarm(crowdErrorReceiverMap);
        return new ProcessResult(true);
    }

    private Map<CrowdMetaInfoDO, String> mergeCrowdErrorMap(Map<CrowdMetaInfoDO, String> labelOrOperateCrowdErrorMap, Map<CrowdMetaInfoDO, String> odpsCrowdErrorMap) {
        if (labelOrOperateCrowdErrorMap.isEmpty() && odpsCrowdErrorMap.isEmpty()) {
            return Maps.newHashMap();
        }
        Map<Long, String> labelOrOperateCrowdIdMap = labelOrOperateCrowdErrorMap.entrySet().stream().collect(
            Collectors.toMap(entry -> entry.getKey().getId(), Entry::getValue));
        Set<Long> labelOrOperateCrowdIds = labelOrOperateCrowdIdMap.keySet();
        Set<Long> needMergeCrowdIds = odpsCrowdErrorMap.keySet().stream().map(CrowdMetaInfoDO::getId)
            .filter(labelOrOperateCrowdIds::contains).collect(Collectors.toSet());

        Map<CrowdMetaInfoDO, String> result = Maps.newHashMap();
        for (Map.Entry<CrowdMetaInfoDO, String> entry : labelOrOperateCrowdErrorMap.entrySet()) {
            CrowdMetaInfoDO crowdMetaInfoDO = entry.getKey();
            if (Objects.isNull(crowdMetaInfoDO) || Objects.isNull(crowdMetaInfoDO.getId()) || needMergeCrowdIds.contains(crowdMetaInfoDO.getId())) {
                continue;
            }
            result.put(crowdMetaInfoDO, entry.getValue());
        }
        for (Map.Entry<CrowdMetaInfoDO, String> entry : odpsCrowdErrorMap.entrySet()) {
            CrowdMetaInfoDO crowdMetaInfoDO = entry.getKey();
            if (Objects.isNull(crowdMetaInfoDO) || Objects.isNull(crowdMetaInfoDO.getId())) {
                continue;
            }

            // 合并告警内容
            Long crowdId = crowdMetaInfoDO.getId();
            if (needMergeCrowdIds.contains(crowdId)) {
                result.put(crowdMetaInfoDO, labelOrOperateCrowdIdMap.get(crowdId) + ";" + entry.getValue());
            } else {
                result.put(crowdMetaInfoDO, entry.getValue());
            }
        }
        return result;
    }

    private List<CrowdMetaInfoWithAlarmInfos> filterNonAlarmConfigCrowd(Map<CrowdMetaInfoDO, String> crowdErrorMap) {
        if (crowdErrorMap.isEmpty()) {
            return Lists.newArrayList();
        }

        List<Long> crowdIds = crowdErrorMap.keySet().stream().map(CrowdMetaInfoDO::getId).collect(Collectors.toList());
        if (crowdIds.isEmpty()) {
            return Lists.newArrayList();
        }
        List<CrowdMonitorConfigBO> configBOList = crowdMonitorConfigService.queryByCrowdIdsAndSceneTypesWithDefault(crowdIds, AlarmSceneTypeEnum.CROWD_BUILD_ERROR);
        if (CollectionUtils.isEmpty(configBOList)) {
            return Lists.newArrayList();
        }
        Map<Long, CrowdMonitorConfigBO> configBOMap = configBOList.stream().filter(config -> Objects.nonNull(config) && config.getIsValid())
            .collect(Collectors.toMap(CrowdMonitorConfigBO::getCrowdId, config -> config));
        if (configBOMap.isEmpty()) {
            return Lists.newArrayList();
        }

        List<CrowdMetaInfoWithAlarmInfos> result = new ArrayList<>();
        for (Map.Entry<CrowdMetaInfoDO, String> entry : crowdErrorMap.entrySet()) {
            CrowdMetaInfoDO crowdMetaInfoDO = entry.getKey();
            String errMsg = entry.getValue();

            CrowdMonitorConfigBO config = configBOMap.get(crowdMetaInfoDO.getId());
            if (config == null || CollectionUtils.isEmpty(config.getAlarmTypes()) || CollectionUtils.isEmpty(config.getReceiverTypes())) {
                continue;
            }
            CrowdMetaInfoWithAlarmInfos crowdMetaInfoWithAlarmInfos = new CrowdMetaInfoWithAlarmInfos(crowdMetaInfoDO, config.getAlarmTypes());
            crowdMetaInfoWithAlarmInfos.setErrMsg(errMsg);
            result.add(crowdMetaInfoWithAlarmInfos);
        }
        return result;
    }

    private Map<CrowdMetaInfoDO, String> getOdpsCrowdErrorMap(List<CrowdMetaInfoDO> odpsCrowdDOList) {
        Map<CrowdMetaInfoDO, String> odpsCrowdErrorMap = Maps.newHashMap();
        for (CrowdMetaInfoDO crowdMetaInfoDO : odpsCrowdDOList) {
            if (Objects.isNull(crowdMetaInfoDO) || Objects.isNull(crowdMetaInfoDO.getExtInfo())) {
                continue;
            }
            JSONObject extJson = JSON.parseObject(crowdMetaInfoDO.getExtInfo());
            if (Objects.isNull(extJson) || !extJson.containsKey("ODPS_TABLE")) {
                continue;
            }
            String odpsTable = extJson.getString("ODPS_TABLE");
            if (Objects.isNull(odpsTable)) {
                continue;
            }

            try {
                String maxPtString = odpsMetaService.getMaxPtString(OdpsGuid.newOdpsGuid(odpsTable));
                Date maxPtDate = DateUtils.getDateByStrToFormat(DateUtils.YMD_FORMAT02, maxPtString);
                if (DateUtils.getIntervalDays(maxPtDate, new Date()) > 3) {
                    odpsCrowdErrorMap.put(crowdMetaInfoDO, "底表3天未产出");
                }
            } catch (Exception e) {
                odpsCrowdErrorMap.put(crowdMetaInfoDO, "底表数据为空");
            }
        }
        return odpsCrowdErrorMap;
    }

    private Map<CrowdMetaInfoDO, String> getLabelOrOperateCrowdErrorMap(List<CrowdMetaInfoDO> needCheckCrowdDOList) {
        Map<CrowdMetaInfoDO, String> labelOrOperateCrowdErrorMap = Maps.newHashMap();
        for (CrowdMetaInfoDO crowdMetaInfoDO : needCheckCrowdDOList) {
            if (Objects.isNull(crowdMetaInfoDO) || Objects.isNull(crowdMetaInfoDO.getId())) {
                continue;
            }

            try {
                if (Objects.equals(crowdMetaInfoDO.getCrowdType(), CrowdTypeEnum.LABEL_CROWD)) {
                    crowdCircleService.checkLabelCrowdValidation(crowdMetaInfoDO.getId());
                } else {
                    crowdCircleService.checkCrowdValidation(crowdMetaInfoDO.getId());
                }
            } catch (Exception e) {
                labelOrOperateCrowdErrorMap.put(crowdMetaInfoDO, e.getMessage());
            }
        }
        return labelOrOperateCrowdErrorMap;
    }

    private Map<String, Map<RoarAlarmTypeEnum, Map<Long, String>>> getCrowdReceiverMap(List<CrowdMetaInfoWithAlarmInfos> crowdWithAlarmList) {
        Map<String, Map<RoarAlarmTypeEnum, Map<Long, String>>> result = new HashMap<>();
        for (CrowdMetaInfoWithAlarmInfos crowdWithAlarm : crowdWithAlarmList) {
            CrowdMetaInfoDO crowdMetaInfoDO = crowdWithAlarm.getCrowdMetaInfoDO();
            List<RoarAlarmTypeEnum> alarmTypes = crowdWithAlarm.getAlarmTypes();
            if (Objects.isNull(crowdMetaInfoDO) || CollectionUtils.isEmpty(alarmTypes)) {
                continue;
            }

            List<Employee> alarmReceivers = crowdMonitorConfigService.getAlarmReceivers(crowdMetaInfoDO, AlarmSceneTypeEnum.CROWD_BUILD_ERROR);
            for (Employee receiver : alarmReceivers) {
                if (receiver == null || receiver.getEmpId() == null) {
                    continue;
                }

                for (RoarAlarmTypeEnum alarmType : alarmTypes) {
                    result.computeIfAbsent(receiver.getEmpId(), k -> new HashMap<>())
                        .computeIfAbsent(alarmType, k -> new HashMap<>())
                        .put(crowdMetaInfoDO.getId(), crowdWithAlarm.getErrMsg());
                }
            }
        }
        return result;
    }

    private void sendCrowdBuildErrorAlarm(Map<String, Map<RoarAlarmTypeEnum, Map<Long, String>>> crowdReceiverMap) {
        AbstractCrowdAlarmServiceImpl alarmService = crowdAlarmFactory.choose(AlarmSceneTypeEnum.CROWD_BUILD_ERROR);
        if (Objects.isNull(alarmService)) {
            return;
        }
        for (Map.Entry<String, Map<RoarAlarmTypeEnum, Map<Long, String>>> entry : crowdReceiverMap.entrySet()) {
            String empId = entry.getKey();
            if (StringUtils.isEmpty(empId) || entry.getValue().isEmpty()) {
                continue;
            }
            for (Map.Entry<RoarAlarmTypeEnum, Map<Long, String>> alarmTypeEntry : entry.getValue().entrySet()) {
                RoarAlarmTypeEnum alarmType = alarmTypeEntry.getKey();
                if (alarmType == null || alarmTypeEntry.getValue().isEmpty()) {
                    continue;
                }

                if (Objects.equals(alarmType, RoarAlarmTypeEnum.DING_TALK)) {
                    for (Map.Entry<Long, String> crowdIdEntry : alarmTypeEntry.getValue().entrySet()) {
                        Long crowdId = crowdIdEntry.getKey();
                        String errMsg = crowdIdEntry.getValue();
                        if (Objects.isNull(crowdId) || StringUtils.isEmpty(errMsg)) {
                            continue;
                        }

                        CrowdAlarmInfoBO crowdAlarmInfoBO = new CrowdAlarmInfoBO();
                        crowdAlarmInfoBO.setSceneType(AlarmSceneTypeEnum.CROWD_BUILD_ERROR);
                        crowdAlarmInfoBO.setAlarmType(alarmType);
                        crowdAlarmInfoBO.setCrowdIds(Collections.singletonList(crowdId));
                        crowdAlarmInfoBO.setReceiver(empId);
                        crowdAlarmInfoBO.setErrMsg(Collections.singletonMap(crowdId, errMsg));
                        alarmService.sendAlarm(crowdAlarmInfoBO);
                    }
                } else {
                    Set<Long> crowdIds = alarmTypeEntry.getValue().keySet();
                    if (CollectionUtils.isEmpty(crowdIds)) {
                        continue;
                    }

                    CrowdAlarmInfoBO crowdAlarmInfoBO = new CrowdAlarmInfoBO();
                    crowdAlarmInfoBO.setSceneType(AlarmSceneTypeEnum.CROWD_BUILD_ERROR);
                    crowdAlarmInfoBO.setAlarmType(alarmType);
                    crowdAlarmInfoBO.setCrowdIds(new ArrayList<>(crowdIds));
                    crowdAlarmInfoBO.setReceiver(empId);
                    crowdAlarmInfoBO.setErrMsg(alarmTypeEntry.getValue());
                    alarmService.sendAlarm(crowdAlarmInfoBO);
                }
            }
        }
    }

    @AteyeInvoker(description = "testGetLabelOrOperateErrorCrowds", paraDesc = "crowdId")
    public void testGetLabelOrOperateErrorCrowds(Long crowdId) {
        CrowdMetaInfoQuery query = new CrowdMetaInfoQuery();
        if (SwitchConfig.COMMON_DEBUG_SWITCH) {
            query.setCreator(SwitchConfig.ALARM_DEBUG_CREATOR_NAME);
        }
        query.setId(crowdId);
        query.setNeedUpdate((byte)1);
        query.setDeleted((byte)0);
        query.setExpiredDateStart(new Date());
        query.setCrowdTypeList(Arrays.asList(CrowdTypeEnum.LABEL_CROWD, CrowdTypeEnum.OPERATE_CROWD, CrowdTypeEnum.OPERATE_EXPRESSION_CROWD));
        List<CrowdMetaInfoDO> needCheckCrowdDOList = crowdService.listQuery(query, 1, 10000);
        Map<CrowdMetaInfoDO, String> labelOrOperateCrowdErrorMap = getLabelOrOperateCrowdErrorMap(needCheckCrowdDOList);
        Ateye.out.println(JSON.toJSONString(labelOrOperateCrowdErrorMap));
    }
}
