package com.fliggy.picasso.schedulerx.job;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.dao.CrowdChildParentRelationsDO;
import com.fliggy.picasso.service.crowd.CrowdChildParentRelationsService;
import com.fliggy.pokemon.client.api.odps.OdpsTunnelDownloadService;
import com.fliggy.pokemon.client.callback.DataFlowCallback;
import com.fliggy.pokemon.client.odps.domain.OdpsTableInfo;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DTS_LOG;
import static com.fliggy.picasso.common.Constant.ODPS_PROJECT_TRIP_PROFILE;

@Component
public class CrowdChildParentRelRefluxProcessor extends JavaProcessor {

    private static final Logger log = LoggerFactory.getLogger(DTS_LOG);

    private static final String TRIP_CROWD_CHILD_PARENT_REL_TABLE = "trip_crowd_child_and_parent_collect_d";
    private static final int CROWD_CHILD_PARENT_REL_REFLEX_PAGE_SIZE = 200;

    @Resource
    private OdpsTunnelDownloadService odpsTunnelDownloadService;
    @Resource
    private CrowdChildParentRelationsService crowdChildParentRelationsService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            String param = StringUtils.isNotEmpty(context.getInstanceParameters()) ? context.getInstanceParameters() : context.getJobParameters();
            executeReflux(param);
            return new ProcessResult(true);
        } catch (Exception e) {
            String errMsg = "人群血缘关系回流异常：";
            log.error(errMsg, e);
            return new ProcessResult(false, errMsg + e.getMessage());
        }
    }

    private void executeReflux(String param) {
        OdpsTableInfo odpsTableInfo = new OdpsTableInfo();
        odpsTableInfo.setProject(ODPS_PROJECT_TRIP_PROFILE);
        odpsTableInfo.setTable(TRIP_CROWD_CHILD_PARENT_REL_TABLE);
        String ds = DateTime.now().minusDays(1).toString(OdpsConstants.ODPS_PHYSICAL_PROFILE_PARTITION_FORMAT);
        odpsTableInfo.setPartition("ds=" + ds);

        odpsTunnelDownloadService.streamDownload(odpsTableInfo, new DataFlowCallback() {
            @Override
            public void success(int code, String message, Object data) {

            }

            @Override
            public void fail(int code, String message, Object data) {
                log.error("odps tunnel download fail, code:{}, message:{}, data:{}", code, message, data);
            }

            @Override
            public void stream(List<Map<String, Object>> list) {
                for (Map<String, Object> map: list) {
                    if (Objects.isNull(map.get("crowd_id"))) {
                        continue;
                    }
                    String crowdIdStr = map.get("crowd_id").toString();
                    if (StringUtils.isNotBlank(param) && !Objects.equals(crowdIdStr, param)) {
                        continue;
                    }
                    Long crowdId = Long.valueOf(crowdIdStr);
                    String childCrowds = map.get("child_crowds").toString();
                    String parentCrowds = map.get("parent_crowds").toString();
                    if (Objects.isNull(childCrowds) && Objects.isNull(parentCrowds)) {
                        continue;
                    }

                    CrowdChildParentRelationsDO originRelDO = crowdChildParentRelationsService.queryDOByCrowdId(crowdId);
                    if (Objects.isNull(originRelDO)) {
                        CrowdChildParentRelationsDO relCreateDO = new CrowdChildParentRelationsDO();
                        relCreateDO.setCrowdId(crowdId);
                        relCreateDO.setChildCrowds(childCrowds);
                        relCreateDO.setParentCrowds(parentCrowds);
                        int result = crowdChildParentRelationsService.createDO(relCreateDO);
                        if (result <= 0) {
                            log.error("CrowdChildParentRelRefluxProcessor.createDO fail, crowdId:{}", crowdId);
                        }
                    } else {
                        if (Objects.equals(originRelDO.getChildCrowds(), childCrowds) && Objects.equals(originRelDO.getParentCrowds(), parentCrowds)) {
                            continue;
                        }
                        CrowdChildParentRelationsDO relUpdateDO = new CrowdChildParentRelationsDO();
                        relUpdateDO.setId(originRelDO.getId());
                        relUpdateDO.setGmtModified(new Date());
                        if (!Objects.equals(originRelDO.getChildCrowds(), childCrowds)) {
                            relUpdateDO.setChildCrowds(childCrowds);
                        }
                        if (!Objects.equals(originRelDO.getParentCrowds(), parentCrowds)) {
                            relUpdateDO.setParentCrowds(parentCrowds);
                        }
                        int result = crowdChildParentRelationsService.updateDO(relUpdateDO);
                        if (result <= 0) {
                            log.error("CrowdChildParentRelRefluxProcessor.updateDO fail, crowdId:{}", crowdId);
                        }
                    }
                }
            }

            @Override
            public void total(long total) {

            }
        }, CROWD_CHILD_PARENT_REL_REFLEX_PAGE_SIZE);
    }
}
