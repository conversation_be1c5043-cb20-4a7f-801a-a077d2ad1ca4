package com.fliggy.picasso.schedulerx.job.profile;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.common.domain.profile.ProfileDagConfigVO;
import com.fliggy.picasso.common.enums.group.ProfileEnum;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.profile.ProfileDagGenerator;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoService;
import com.fliggy.picasso.service.profile.ProfileDagNodeConfigService;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class PhysicalProfileDagGenerateProcessor extends MapJobProcessor {

    @Resource
    private PhysicalProfileInfoService physicalProfileInfoService;
    @Resource
    private ProfileDagGenerator profileDagGenerator;
    @Resource
    private ProfileDagNodeConfigService profileDagNodeConfigService;

    @Switch(description = "不支持的画像类型")
    public static String notSupportedProfileTypes = "scrm_up";

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String dispatchName = "profile_dag_generate_dispatch_level1";
        String taskName = context.getTaskName();
        try {
            if (isRootTask(context)) {
                List<PhysicalProfileInfoDTO> physicalProfileInfoDTOList;
                String paramStr = StringUtils.isBlank(context.getInstanceParameters())? context.getJobParameters() : context.getInstanceParameters();
                if (StringUtils.isNotBlank(paramStr)) {
                    physicalProfileInfoDTOList = physicalProfileInfoService.queryByCodes(Arrays.asList(paramStr.split(",")));
                }else {
                    physicalProfileInfoDTOList = physicalProfileInfoService.queryAll();
                }

                List<String> profileCodes = filterProfile(physicalProfileInfoDTOList);
                if (null == profileCodes || profileCodes.isEmpty()) {
                    return new ProcessResult(true);
                }
                return map(profileCodes, dispatchName);
            } else if (taskName.equals(dispatchName)) {
                String profileCode = (String) context.getTask();
                if (null == profileCode || null == ProfileEnum.fromCode(profileCode)) {
                    return new ProcessResult(true);
                }

                return new ProcessResult(profileDagGenerator.generate(ProfileEnum.fromCode(profileCode)));
            } else {
                return new ProcessResult(false, "profile dag generate error: invalid task name: " + taskName);
            }
        } catch (Exception e) {
            return new ProcessResult(false);
        }
    }

    /**
     * 过滤：
     * odps更新时间不是今天，即今天没有跑的画像
     *
     * @param physicalProfileInfoDTOList
     * @return
     */
    private List<String> filterProfile(List<PhysicalProfileInfoDTO> physicalProfileInfoDTOList) {
        // 过滤不支持的画像类型
        List<PhysicalProfileInfoDTO> validProfileInfos = physicalProfileInfoDTOList.stream()
                .filter(physicalProfileInfoDTO -> !notSupportedProfileTypes.contains(physicalProfileInfoDTO.getPhysicalProfileCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNullOrEmpty(validProfileInfos)) {
            return Lists.newArrayList();
        }

        // 获取所有画像最新运行记录
        List<ProfileDagConfigVO> dagNodeConfigVOList = profileDagNodeConfigService.queryAllLatestConfigs();
        if (CollectionUtils.isNullOrEmpty(dagNodeConfigVOList)) {
            return validProfileInfos.stream().map(PhysicalProfileInfoDTO::getPhysicalProfileCode).collect(Collectors.toList());
        }

        // 获取今天已经跑过的画像
        List<Long> todayAlreadyRunProfileIds = dagNodeConfigVOList.stream()
                .filter(config -> Objects.nonNull(config.getGmtCreate()) && DateUtils.isToday(config.getGmtCreate()))
                .map(ProfileDagConfigVO::getProfileId).collect(Collectors.toList());
        if (CollectionUtils.isNullOrEmpty(todayAlreadyRunProfileIds)) {
            return validProfileInfos.stream().map(PhysicalProfileInfoDTO::getPhysicalProfileCode).collect(Collectors.toList());
        }

        // 剔除已经跑过的画像
        return validProfileInfos.stream()
                .filter(profileInfo -> !todayAlreadyRunProfileIds.contains(profileInfo.getId()))
                .map(PhysicalProfileInfoDTO::getPhysicalProfileCode)
                .collect(Collectors.toList());
    }

    @AteyeInvoker(description = "测试获取当天需要跑的画像")
    public void testTodayNeedRunProfiles() {
        List<PhysicalProfileInfoDTO> physicalProfileInfoDTOList = physicalProfileInfoService.queryAll();
        List<String> validProfiles = filterProfile(physicalProfileInfoDTOList);
        Ateye.out.println("today need run profiles: " + JSON.toJSONString(validProfiles));
    }

    @AteyeInvoker(description = "profile rerun", paraDesc = "profileCode")
    public boolean profileRerun(String profileCode) {
        ProfileEnum profileEnum = ProfileEnum.fromCode(profileCode);
        if (null == profileEnum) {
            Ateye.out.println("profile rerun error: invalid profile code: " + profileCode);
        }

        return profileDagGenerator.generate(profileEnum);
    }
}
