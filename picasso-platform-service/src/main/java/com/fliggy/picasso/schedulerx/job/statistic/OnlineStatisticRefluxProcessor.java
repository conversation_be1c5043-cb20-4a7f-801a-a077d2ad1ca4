package com.fliggy.picasso.schedulerx.job.statistic;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.common.enums.statistic.StatisticEntityTypeEnum;
import com.fliggy.picasso.common.enums.statistic.StatisticTimeDimEnum;
import com.fliggy.picasso.common.statistic.OnlineStatisticInfoVO;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.service.statistic.OnlineStatisticInfoService;
import com.fliggy.pokemon.client.api.odps.OdpsTunnelDownloadService;
import com.fliggy.pokemon.client.callback.DataFlowCallback;
import com.fliggy.pokemon.client.odps.domain.OdpsTableInfo;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DTS_LOG;
import static com.fliggy.picasso.common.Constant.ODPS_PROJECT_TRIP_PROFILE;

@Component
public class OnlineStatisticRefluxProcessor extends JavaProcessor {

    private static final Logger log = LoggerFactory.getLogger(DTS_LOG);

    private static final String TRIP_ONLINE_STATISTIC_INFO_TABLE = "trip_crowd_online_statistic_info";
    private static final int ONLINE_STATISTIC_INFO_REFLEX_PAGE_SIZE = 200;

    @Resource
    private OdpsTunnelDownloadService odpsTunnelDownloadService;
    @Resource
    private OnlineStatisticInfoService onlineStatisticInfoService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            executeReflux();
            return new ProcessResult(true);
        } catch (Exception e) {
            String errMsg = "线上统计信息回流异常：";
            log.error("errMsg", e);
            return new ProcessResult(false, errMsg + e.getMessage());
        }
    }

    /**
     * 执行回流
     */
    private void executeReflux() {
        OdpsTableInfo odpsTableInfo = new OdpsTableInfo();
        odpsTableInfo.setProject(ODPS_PROJECT_TRIP_PROFILE);
        odpsTableInfo.setTable(TRIP_ONLINE_STATISTIC_INFO_TABLE);
        String ds = DateTime.now().minusDays(1).toString(OdpsConstants.ODPS_PHYSICAL_PROFILE_PARTITION_FORMAT);
        odpsTableInfo.setPartition("ds=" + ds);

        odpsTunnelDownloadService.streamDownload(odpsTableInfo, new DataFlowCallback() {
            @Override
            public void success(int code, String message, Object data) {

            }

            @Override
            public void fail(int code, String message, Object data) {
                log.error("odps tunnel download fail, code:{}, message:{}, data:{}", code, message, data);
            }

            @Override
            public void stream(List<Map<String, Object>> list) {
                for (Map<String, Object> map: list) {
                    if (Objects.isNull(map.get("crowd_id"))) {
                        continue;
                    }

                    // 获取历史数据
                    String crowdIdStr = map.get("crowd_id").toString();
                    long crowdId = Long.parseLong(crowdIdStr);
                    List<OnlineStatisticInfoVO> statisticInfoVOList = onlineStatisticInfoService.queryByEntityIdAndType(
                            crowdId, StatisticEntityTypeEnum.TAOBAO_USER);

                    // 新增或更新数据
                    if (CollectionUtils.isNullOrEmpty(statisticInfoVOList)) {
                        List<OnlineStatisticInfoVO> insertVOList = generateInsertVO(crowdId, map, ds);
                        onlineStatisticInfoService.batchInsert(insertVOList);
                    } else {
                        List<OnlineStatisticInfoVO> updateVOList = generateUpdateVO(map, statisticInfoVOList, ds);
                        onlineStatisticInfoService.batchUpdate(updateVOList);
                        if (statisticInfoVOList.size() == StatisticTimeDimEnum.values().length) {
                            continue;
                        }

                        // 补充新维度数据
                        List<OnlineStatisticInfoVO> insertVOList = new ArrayList<>();
                        Set<StatisticTimeDimEnum> dimSet = statisticInfoVOList.stream().map(OnlineStatisticInfoVO::getStatisticDim).collect(Collectors.toSet());
                        for (StatisticTimeDimEnum dim : StatisticTimeDimEnum.values()) {
                            if (dimSet.contains(dim)) {
                                continue;
                            }
                            OnlineStatisticInfoVO insertVO = new OnlineStatisticInfoVO();
                            insertVO.setEntityId(crowdId);
                            insertVO.setEntityType(StatisticEntityTypeEnum.TAOBAO_USER);
                            insertVO.setStatisticDim(dim);
                            Object statisticVal = map.get(getDimColumnName(dim));
                            insertVO.setStatisticVal(Objects.isNull(statisticVal) ? 0L : Long.parseLong(statisticVal.toString()));
                            insertVO.setStatisticDate(ds);
                            Object isValid = map.get("is_valid");
                            insertVO.setIsValid(Objects.nonNull(isValid) && Boolean.parseBoolean(isValid.toString()));
                            Object isHeavy = map.get("is_heavy");
                            insertVO.setIsHeavy(Objects.nonNull(isHeavy) && Boolean.parseBoolean(isHeavy.toString()));
                            insertVOList.add(insertVO);
                        }
                        onlineStatisticInfoService.batchInsert(insertVOList);
                    }
                }
            }

            @Override
            public void total(long total) {

            }
        }, ONLINE_STATISTIC_INFO_REFLEX_PAGE_SIZE);
    }

    private List<OnlineStatisticInfoVO> generateUpdateVO(Map<String, Object> map, List<OnlineStatisticInfoVO> oldVOList, String ds) {
        List<OnlineStatisticInfoVO> result = new ArrayList<>();
        for (OnlineStatisticInfoVO oldVO : oldVOList) {
            OnlineStatisticInfoVO updateVO = new OnlineStatisticInfoVO();
            updateVO.setId(oldVO.getId());
            Object statisticVal = map.get(getDimColumnName(oldVO.getStatisticDim()));
            updateVO.setStatisticVal(Objects.isNull(statisticVal) ? 0L : Long.parseLong(statisticVal.toString()));
            updateVO.setStatisticDate(ds);
            Object isValid = map.get("is_valid");
            updateVO.setIsValid(Objects.nonNull(isValid) && Boolean.parseBoolean(isValid.toString()));
            Object isHeavy = map.get("is_heavy");
            updateVO.setIsHeavy(Objects.nonNull(isHeavy) && Boolean.parseBoolean(isHeavy.toString()));
            result.add(updateVO);
        }
        return result;
    }

    private List<OnlineStatisticInfoVO> generateInsertVO(long crowdId, Map<String, Object> map, String ds) {
        List<OnlineStatisticInfoVO> result = new ArrayList<>();
        for (StatisticTimeDimEnum dim : StatisticTimeDimEnum.values()) {
            OnlineStatisticInfoVO insertVO = new OnlineStatisticInfoVO();
            insertVO.setEntityId(crowdId);
            insertVO.setEntityType(StatisticEntityTypeEnum.TAOBAO_USER);
            insertVO.setStatisticDim(dim);
            Object statisticVal = map.get(getDimColumnName(dim));
            insertVO.setStatisticVal(Objects.isNull(statisticVal) ? 0L : Long.parseLong(statisticVal.toString()));
            insertVO.setStatisticDate(ds);
            Object isValid = map.get("is_valid");
            insertVO.setIsValid(Objects.nonNull(isValid) && Boolean.parseBoolean(isValid.toString()));
            Object isHeavy = map.get("is_heavy");
            insertVO.setIsHeavy(Objects.nonNull(isHeavy) && Boolean.parseBoolean(isHeavy.toString()));
            result.add(insertVO);
        }
        return result;
    }

    private String getDimColumnName(StatisticTimeDimEnum dim) {
        switch (dim) {
            case DAY:
                return "yesterday_cnt";
            case WEEK:
                return "avg_cnt_7";
            case MONTH:
                return "avg_cnt_30";
            default:
                return "";
        }
    }
}
