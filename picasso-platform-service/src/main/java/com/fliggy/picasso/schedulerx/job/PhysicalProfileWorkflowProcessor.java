package com.fliggy.picasso.schedulerx.job;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.action.ExecuteActionParam;
import com.fliggy.picasso.action.PhysicalProfileActionParam;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.schedulerx.SchedulerxConstants;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoService;
import com.fliggy.picasso.workflow.PhysicalProfileWorkflow;
import com.fliggy.picasso.workflow.WorkflowConstants;
import com.fliggy.picasso.workflow.WorkflowTasks;
import com.fliggy.picasso.tair.TairRDBLock;
import com.fliggy.picasso.tair.TairRDBLockManager;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jeasy.flows.work.DefaultWorkReport;
import org.jeasy.flows.work.WorkContext;
import org.jeasy.flows.work.WorkReport;
import org.jeasy.flows.work.WorkStatus;
import org.jeasy.flows.workflow.ParallelFlowReport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;
import static com.fliggy.picasso.common.constants.TairConstants.EXPIRE_TIME_IN_SEC;
import static com.fliggy.picasso.common.constants.TairConstants.LOCK_PREFIX;

/**
 * <AUTHOR>
 * @date 2021/1/6 上午11:02
 */
@Component
public class PhysicalProfileWorkflowProcessor extends JavaProcessor {

    private static final Logger LOG = LoggerFactory.getLogger(DISPATCH_LOG);

    @Autowired
    private PhysicalProfileWorkflow physicalProfileWorkflow;

    @Autowired
    private PhysicalProfileInfoService physicalProfileInfoService;

    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        this.executorService = new ThreadPoolExecutor(0, Integer.MAX_VALUE,
                60L, TimeUnit.SECONDS,
                new SynchronousQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("PhysicalProfile-Scheduler-pool-").build()
        );
    }

    @Override
    public ProcessResult process(JobContext jobContext) {
        String physicalProfileCodes = StringUtils.isEmpty(jobContext.getInstanceParameters()) ? jobContext.getJobParameters() : jobContext.getInstanceParameters();

        Map<PhysicalProfileInfoDTO, List<LabelInfoDTO>> param;
        if (StringUtils.isEmpty(physicalProfileCodes)) {
            param = physicalProfileInfoService.queryRefluxProfileAndLabels();
        } else {
            List<String> profileCodes = Lists.newArrayList(
                    StringUtils.split(physicalProfileCodes, SchedulerxConstants.PARAM_SPLIT));
            param = physicalProfileInfoService.queryRefluxProfileAndLabels(profileCodes);
        }

        if (param != null && !param.isEmpty()) {
            List<PhysicalProfileWorkflowJob> jobs = Lists.newArrayList();
            param.forEach((profile, labels) -> {
                PhysicalProfileWorkflowJob job = new PhysicalProfileWorkflowJob(profile, labels);
                jobs.add(job);
            });

            // submit work units and wait for results
            List<Future<WorkReport>> futures;
            try {
                futures = this.executorService.invokeAll(jobs);
            } catch (InterruptedException e) {
                throw new RuntimeException("The parallel flow was interrupted while executing work units", e);
            }

            Map<String, Future<WorkReport>> futureMap = new HashMap<>();
            for (int index = 0; index < jobs.size(); index++) {
                futureMap.put(jobs.get(index).getProfile().getPhysicalProfileCode(), futures.get(index));
            }

            LOG.info("【物理画像】futureMap: {}", JSON.toJSONString(futureMap));
            // gather
            Map<String, WorkReport> resultMap = new HashMap<>();
            for (Map.Entry<String, Future<WorkReport>> entry : futureMap.entrySet()) {
                try {
                    if (Objects.nonNull(entry.getValue().get())) {
                        WorkReport workReport = entry.getValue().get();
                        resultMap.put(entry.getKey(), workReport);
                    }
                } catch (Exception e) {
                    String msg = String.format("【物理画像】Unable to execute work '%s'", entry.getKey());
                    LOG.error(msg, e);
                    DefaultWorkReport errWorkReport = new DefaultWorkReport(WorkStatus.FAILED, null, e);
                    resultMap.put(entry.getKey(), errWorkReport);
                }
            }

            boolean succeeded = isSucceeded(new ArrayList<>(resultMap.values()));
            return new ProcessResult(succeeded, toPrintResult(resultMap, succeeded));
        }

        return new ProcessResult(false, "未执行，检查是否配置有效的物理画像！");
    }

    /**
     * 转换为Schedulerx的可打印结果字符串
     *
     * @param resultMap 多个物理画像的工作流执行报告
     * @param succeeded 多个物理画像只要有一个执行失败则false，全部成功则true
     * @return Schedulerx的可打印结果字符串
     */
    private String toPrintResult(Map<String, WorkReport> resultMap, boolean succeeded) {

        StringBuilder builder = new StringBuilder();

        resultMap.forEach((profile, workReport) -> {
            // 若有一个画像执行失败，则省略执行成功的部分，只打印执行失败的物理画像情况，留出空间给错误信息
            if (!succeeded && workReport.getStatus() == WorkStatus.COMPLETED) {
                return;
            }

            WorkflowTasks tasks = null;
            if (Objects.nonNull(workReport.getWorkContext())) {
                WorkContext context = workReport.getWorkContext();
                profile = (String) context.getOrDefault(WorkflowConstants.CONTEXT_KEY_PROFILE, "unknown");
                tasks = (WorkflowTasks) context.getOrDefault(
                        WorkflowConstants.CONTEXT_KEY_TASKS,
                        new WorkflowTasks()
                );
            }

            builder.append("物理画像:[").append(profile).append("]执行状态:[").append(workReport.getStatus()).append("]");
            if (workReport.getStatus() != WorkStatus.COMPLETED && Objects.nonNull(workReport.getError())) {
                builder.append("错误信息:[").append(workReport.getError().getMessage()).append("]");
            }
            builder.append("子任务:[");

            if (Objects.nonNull(tasks)) {
                tasks.getTasks().forEach(
                        task -> {
                            builder.append(task.getName()).append(":").append(task.isSuccess()).append(";");
                            if (!task.isSuccess()) {
                                builder.append("错误信息:[").append(task.getMsg()).append("]");
                            }
                        });
            }
            builder.append("]");
        });

        // 兜底：返回字符仍然大于1000bytes，直接截断
        String result = builder.toString();
        LOG.info("【物理画像】result: {}", result);
        return result.getBytes().length>1000 ? new String(Arrays.copyOf(result.getBytes(), 1000)) : result;
    }

    /**
     * 若有一个执行失败则标记为失败
     *
     * @param results 多个物理画像的工作流执行报告
     * @return 是否全部成功
     */
    private boolean isSucceeded(List<WorkReport> results) {
        return WorkStatus.COMPLETED == new ParallelFlowReport(results).getStatus();
    }

    @Autowired
    private TairRDBLockManager tairRDBLockManager;

    @Data
    private class PhysicalProfileWorkflowJob implements Callable<WorkReport> {

        private PhysicalProfileInfoDTO profile;
        private List<LabelInfoDTO> labels;

        public PhysicalProfileWorkflowJob(PhysicalProfileInfoDTO profile,
                                          List<LabelInfoDTO> labels) {
            this.profile = profile;
            this.labels = labels;
        }

        @Override
        public WorkReport call() {
            // 加锁
            TairRDBLock lock = tairRDBLockManager.getLock(LOCK_PREFIX + profile.getPhysicalProfileCode());

            if (lock.tryGetDistributedLock(EXPIRE_TIME_IN_SEC)) {
                try {
                    PhysicalProfileActionParam workflowParam = new PhysicalProfileActionParam(profile, labels);
                    return physicalProfileWorkflow.run(ExecuteActionParam.of(workflowParam));
                } catch (Exception e) {
                    String msg = "【物理画像】Execute PhysicalProfile Workflow Error!";
                    LOG.error(msg, e);
                    throw new RuntimeException(msg, e);
                } finally {
                    lock.releaseDistributedLock();
                }
            } else {
                String msg = String.format("【物理画像】profile %s is already execute! try again later!",
                        profile.getPhysicalProfileCode());
                throw new RuntimeException(msg);
            }
        }
    }

    @AteyeInvoker(description = "手动获取画像信息", paraDesc = "physicalProfileCodes")
    public void queryProfileInfos(String physicalProfileCodes) {
        List<String> profileCodes = Lists.newArrayList(StringUtils.split(physicalProfileCodes, SchedulerxConstants.PARAM_SPLIT));
        Map<PhysicalProfileInfoDTO, List<LabelInfoDTO>> param = physicalProfileInfoService.queryRefluxProfileAndLabels(profileCodes);
        Ateye.out.println(JSON.toJSONString(param));
    }
}
