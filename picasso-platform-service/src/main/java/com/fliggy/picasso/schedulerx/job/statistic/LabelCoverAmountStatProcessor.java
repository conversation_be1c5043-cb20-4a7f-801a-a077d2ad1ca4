package com.fliggy.picasso.schedulerx.job.statistic;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.odps.OdpsException;
import com.aliyun.odps.data.Record;
import com.aliyuncs.dataworks.model.v20171212.ListMetaTablePartitionResponse;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.domain.label.LabelOdpsSourceConfig;
import com.fliggy.picasso.common.odps.OdpsGuid;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.entity.bo.LabelStatisticsBO;
import com.fliggy.picasso.offline.OdpsMetaService;
import com.fliggy.picasso.offline.OdpsService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.label.LabelStatisticsService;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;
import static com.fliggy.picasso.common.constants.OdpsConstants.ODPS_EXECUTE_PROJECT;

@Component
public class LabelCoverAmountStatProcessor extends MapJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);

    @Autowired
    private LabelInfoService labelInfoService;
    @Autowired
    private OdpsService odpsService;
    @Autowired
    private OdpsMetaService odpsMetaService;
    @Autowired
    private LabelStatisticsService labelStatisticsService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String dispatchName = "label_cover_amount_dispatch_level1";
        String taskName = context.getTaskName();
        try {
            if (isRootTask(context)) {
                List<LabelInfoDTO> labelList;
                String paramStr = StringUtils.isBlank(context.getInstanceParameters())? context.getJobParameters() : context.getInstanceParameters();
                if (StringUtils.isNotBlank(paramStr)) {
                    labelList = labelInfoService.findByCodes(Arrays.asList(paramStr.split(",")));
                }else {
                    labelList = labelInfoService.listOfflineStandardLabels();
                }

                if (CollectionUtils.isEmpty(labelList)) {
                    return new ProcessResult(true);
                }
                return map(labelList, dispatchName);
            } else if (taskName.equals(dispatchName)) {
                LabelInfoDTO labelDTO = (LabelInfoDTO) context.getTask();
                if (Objects.isNull(labelDTO) || Objects.isNull(labelDTO.getOdpsSourceConfig())) {
                    return new ProcessResult(true);
                }
                LabelOdpsSourceConfig odpsSourceConfig = labelDTO.getOdpsSourceConfig();
                if (Objects.isNull(odpsSourceConfig.getProject()) || Objects.isNull(odpsSourceConfig.getTable())) {
                    return new ProcessResult(true);
                }

                // 查询最新分区产出时间
                String labelOutputTime = getLabelOutputTime(odpsSourceConfig.getProject(), odpsSourceConfig.getTable());
                if (StringUtils.isEmpty(labelOutputTime)) {
                    return new ProcessResult(true);
                }

                // 查询底表覆盖量
                Long coverAmount = getLabelCoverAmount(labelDTO.getCode(), odpsSourceConfig);
                return new ProcessResult(upsertRecord(labelDTO.getCode(), labelOutputTime, coverAmount));
            } else {
                return new ProcessResult(false, "label use amount error: invalid task name: " + taskName);
            }
        } catch (Exception e) {
            log.error("label cover amount error: " + e.getMessage());
            return new ProcessResult(false, e.getMessage());
        }
    }

    /**
     * 获取底表覆盖量
     */
    private Long getLabelCoverAmount(String labelCode, LabelOdpsSourceConfig odpsSourceConfig) throws OdpsException, IOException {
        if (Objects.isNull(odpsSourceConfig) || StringUtils.isBlank(odpsSourceConfig.getProject())
                || StringUtils.isBlank(odpsSourceConfig.getTable()) || StringUtils.isBlank(odpsSourceConfig.getPrimaryKey())
                || StringUtils.isBlank(odpsSourceConfig.getField()) || StringUtils.isBlank(odpsSourceConfig.getPartitionField())) {
            return 0L;
        }
        String sql = generateSql(odpsSourceConfig);
        String name = "cover_" + labelCode;
        if (name.length() > 45) {
            name = name.substring(0, 45);
        }
        String taskName = odpsService.getTaskName(name);
        List<Record> records = odpsService.syncExecuteWithResult(ODPS_EXECUTE_PROJECT, sql, taskName, null, 10L);
        Long result = odpsService.getCountResult(records);
        if (Objects.isNull(result)) {
            return 0L;
        }
        return result;
    }

    private String generateSql(LabelOdpsSourceConfig odpsSourceConfig) {
        String ds = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
        String format = "select count(distinct %s) from %s.%s where %s=%s and %s is not null;";
        return String.format(format, odpsSourceConfig.getPrimaryKey(), odpsSourceConfig.getProject(), odpsSourceConfig.getTable(),
            odpsSourceConfig.getPartitionField(), ds, odpsSourceConfig.getField());
    }

    /**
     * 获取标签今天产出时间（ds=昨天分区）
     * 若今天未产出，返回空
     */
    private String getLabelOutputTime(String projectName, String tableName) {
        OdpsGuid odpsGuid = new OdpsGuid(projectName, tableName);
        ListMetaTablePartitionResponse partitionInfo = odpsMetaService.getPartitionInfo(odpsGuid);
        if (Objects.isNull(partitionInfo) || CollectionUtils.isEmpty(partitionInfo.getPartitionList())) {
            return StringUtils.EMPTY;
        }

        ListMetaTablePartitionResponse.Partition lastPartition = partitionInfo.getPartitionList().get(0);
        if (Objects.isNull(lastPartition) || StringUtils.isEmpty(lastPartition.getModifyTime()) || StringUtils.isEmpty(lastPartition.getPartitionName())) {
            return StringUtils.EMPTY;
        }

        String ds = lastPartition.getPartitionName().substring(3);
        String yesterday = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
        if (!ds.equals(yesterday)) {
            return StringUtils.EMPTY;
        }

        Date modify = DateUtils.getDateByStrToFormat(DateUtils.YYYY_MM_DD_HH_MM_SS, lastPartition.getModifyTime());
        return DateUtils.getDateByFormat(modify, DateUtils.HMS_FORMAT);
    }

    /**
     * 插入或更新标签统计数据记录
     */
    private Boolean upsertRecord(String labelCode, String labelOutputTime, Long coverAmount) {
        String outputDate = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
        LabelStatisticsBO labelStatisticsBO = labelStatisticsService.queryByLabelCodeAndDate(labelCode, outputDate);
        if (Objects.isNull(labelStatisticsBO)) {
            labelStatisticsBO = new LabelStatisticsBO();
            labelStatisticsBO.setLabelCode(labelCode);
            labelStatisticsBO.setOutputDate(outputDate);
            labelStatisticsBO.setOutputTime(labelOutputTime);
            labelStatisticsBO.setCoverAmount(coverAmount);
            return labelStatisticsService.insert(labelStatisticsBO);
        } else {
            labelStatisticsBO.setOutputTime(labelOutputTime);
            labelStatisticsBO.setCoverAmount(coverAmount);
            return labelStatisticsService.update(labelStatisticsBO) > 0;
        }
    }

    @AteyeInvoker(description = "testListOfflineStandardLabels")
    public void testListOfflineStandardLabels() {
        List<LabelInfoDTO> labels = labelInfoService.listOfflineStandardLabels();
        List<String> labelCodes = labels.stream().map(LabelInfoDTO::getCode).collect(Collectors.toList());
        Ateye.out.println(JSON.toJSONString(labelCodes));
    }
}
