package com.fliggy.picasso.schedulerx.job.profile;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.common.domain.profile.ProfileDagConfigVO;
import com.fliggy.picasso.common.enums.group.ProfileEnum;
import com.fliggy.picasso.common.enums.profile.ProfileDagNodeEnum;
import com.fliggy.picasso.common.enums.profile.ProfileDagStatusEnum;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.profile.ProfileDagRunner;
import com.fliggy.picasso.schedulerx.task.ProfileDagTask;
import com.fliggy.picasso.service.profile.ProfileDagNodeConfigService;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;

/**
 * <AUTHOR>
 */
@Component
public class PhysicalProfileDagRunProcessor extends MapJobProcessor {

    public static final Logger logger = LoggerFactory.getLogger(DISPATCH_LOG);

    @Resource
    private ProfileDagNodeConfigService profileDagNodeConfigService;
    @Resource
    private ProfileDagRunner profileDagRunner;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String dispatchName = "profile_dag_run_dispatch_level1";
        String taskName = context.getTaskName();
        try {
            if (isRootTask(context)) {
                List<ProfileDagConfigVO> dagNodeConfigVOList;
                String paramStr = StringUtils.isBlank(context.getInstanceParameters())? context.getJobParameters() : context.getInstanceParameters();
                if (StringUtils.isNotBlank(paramStr)) {
                    ProfileDagConfigVO profileDagConfigVO = profileDagNodeConfigService.selectById(Long.valueOf(paramStr));
                    dagNodeConfigVOList = Collections.singletonList(profileDagConfigVO);
                } else {
                    dagNodeConfigVOList = profileDagNodeConfigService.queryAllLatestConfigs();
                }

                if (CollectionUtils.isNullOrEmpty(dagNodeConfigVOList)) {
                    return new ProcessResult(true);
                }

                List<ProfileDagConfigVO> validDagNodeConfigVOList = filterDag(dagNodeConfigVOList);
                if (CollectionUtils.isNullOrEmpty(validDagNodeConfigVOList)) {
                    return new ProcessResult(true);
                }
                return map(validDagNodeConfigVOList, dispatchName);
            } else if (taskName.equals(dispatchName)) {
                ProfileDagConfigVO task = (ProfileDagConfigVO) context.getTask();
                profileDagRunner.run(task);

                ProfileDagTask profileDagTask = new ProfileDagTask(ProfileEnum.fromId(task.getProfileId()), ProfileDagStatusEnum.SUCCESS, "");
                return new ProcessResult(true, JSON.toJSONString(profileDagTask));
            } else {
                return new ProcessResult(false, "profile dag run error: invalid task name: " + taskName);
            }
        } catch (Exception e) {
            logger.error("profile dag run error: ", e);
            return new ProcessResult(false, "profile dag run error:" + e.getMessage());
        }
    }

    /**
     * 过滤：
     * 任务没有终止
     *
     * @param dagNodeConfigVOList
     * @return
     */
    private List<ProfileDagConfigVO> filterDag(List<ProfileDagConfigVO> dagNodeConfigVOList) {
        return dagNodeConfigVOList.stream()
                .filter(config -> !ProfileDagStatusEnum.isTerminated(config.getDagStatus()))
                .collect(java.util.stream.Collectors.toList());
    }

    @AteyeInvoker(description = "dag状态重置", paraDesc = "id&dagNodeCode")
    public void dagRefresh(Long id, String dagNodeCode) {
        ProfileDagNodeEnum dagNodeEnum = ProfileDagNodeEnum.getEnumByCode(dagNodeCode);
        if (null == dagNodeEnum) {
            Ateye.out.println("invalid dag node code");
            return;
        }

        ProfileDagConfigVO profileDagConfigVO = profileDagNodeConfigService.selectById(id);
        if (null == profileDagConfigVO) {
            Ateye.out.println("invalid profile id");
            return;
        }

        profileDagConfigVO.getDagNodes().stream()
                .filter(node -> node.getNodeName().equals(dagNodeEnum))
                .findFirst().ifPresent(node -> {
                    node.setNodeStatus(ProfileDagStatusEnum.INIT);
                });

        ProfileDagConfigVO updateVO = new ProfileDagConfigVO();
        updateVO.setId(id);
        updateVO.setDagStatus(ProfileDagStatusEnum.RUNNING);
        updateVO.setDagNodes(profileDagConfigVO.getDagNodes());
        profileDagNodeConfigService.update(updateVO);
    }

}
