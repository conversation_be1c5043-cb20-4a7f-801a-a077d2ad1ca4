package com.fliggy.picasso.schedulerx.job.statistic;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.entity.bo.LabelStatisticsBO;
import com.fliggy.picasso.mapper.tripcrowd.CrowdMapper;
import com.fliggy.picasso.service.crowd.CrowdChildParentRelationsService;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.label.LabelStatisticsService;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;

@Component
public class LabelUseAmountStatProcessor extends JavaProcessor {

    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);

    @Autowired
    private CrowdService crowdService;
    @Autowired
    private CrowdMapper crowdMapper;
    @Autowired
    private LabelInfoService labelInfoService;
    @Autowired
    private LabelStatisticsService labelStatisticsService;
    @Autowired
    private CrowdChildParentRelationsService crowdChildParentRelationsService;

    @Switch(description = "标签使用量统计任务最大深度")
    public int labelUseAmountStatMaxDepth = 20;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            return new ProcessResult(processAllLabelsStatistic());
        } catch (Exception e) {
            log.error("label use amount error: " + e.getMessage(), e);
            return new ProcessResult(false);
        }
    }

    /**
     * 一次性统计所有标签使用量
     */
    private boolean processAllLabelsStatistic() {
        try {
            // 统计所有标签的使用量
            Map<String, Set<Long>> labelToCrowdIdsMap = statisticsAllLabelUsage();
            
            // 获取所有有效标签列表
            List<LabelInfoDTO> labelInfoDTOList = labelInfoService.listValidLabels();
            Set<String> validLabelCodes = labelInfoDTOList.stream()
                    .map(LabelInfoDTO::getCode)
                    .collect(Collectors.toSet());
            
            // 处理每个标签的统计结果
            for (String labelCode : validLabelCodes) {
                // 历史原因，这个标签code非常不标准，需要特殊处理
                String actualLabelCode = StringUtils.equals(labelCode, "label") ? "是否是黄牛用户" : labelCode;
                
                Set<Long> directCrowdIds = labelToCrowdIdsMap.getOrDefault(actualLabelCode, new HashSet<>());
                Long labelUseAmount = calculateLabelUseAmountWithParents(directCrowdIds);
                
                // 更新数据库记录
                upsertRecord(labelCode, labelUseAmount);
                
                log.info("Label {} usage statistics completed, direct crowds: {}, total usage: {}", 
                        labelCode, directCrowdIds.size(), labelUseAmount);
            }
            
            log.info("All label usage statistics completed, total labels: {}", validLabelCodes.size());
            
            return true;
        } catch (Exception e) {
            log.error("Process all labels statistic failed", e);
            return false;
        }
    }

    /**
     * 一次性遍历所有标签人群，统计每个标签的使用情况
     */
    private Map<String, Set<Long>> statisticsAllLabelUsage() {
        Map<String, Set<Long>> labelToCrowdIdsMap = new HashMap<>();
        
        Long lastId = 0L;
        int pageSize = 20; // 按照你的建议，每次查询20条
        
        while (true) {
            // 分页查询标签人群ID
            List<Long> pageList = crowdMapper.pageQueryValidLabelCrowdIds(lastId, pageSize);
            if (CollectionUtils.isEmpty(pageList)) {
                break;
            }
            
            // 批量查询人群详情
            List<CrowdMetaInfoDO> crowdMetaInfoDOList = crowdMapper.queryByIds(pageList);
            
            // 在内存中分析每个人群使用的标签
            for (CrowdMetaInfoDO crowdMetaInfoDO : crowdMetaInfoDOList) {
                if (Objects.isNull(crowdMetaInfoDO) || Objects.isNull(crowdMetaInfoDO.getConditions())
                        || CollectionUtils.isEmpty(crowdMetaInfoDO.getConditions().getGroup())) {
                    continue;
                }
                
                // 提取该人群使用的所有标签
                Set<String> usedLabels = extractLabelsFromCrowd(crowdMetaInfoDO);
                
                // 记录标签与人群的映射关系
                for (String labelCode : usedLabels) {
                    labelToCrowdIdsMap.computeIfAbsent(labelCode, k -> new HashSet<>())
                            .add(crowdMetaInfoDO.getId());
                }
            }
            
            // 准备下一页查询
            if (pageList.size() < pageSize) {
                break;
            }
            lastId = pageList.get(pageList.size() - 1);
        }
        
        log.info("Statistics all label usage completed, found {} unique labels", labelToCrowdIdsMap.size());
        return labelToCrowdIdsMap;
    }

    /**
     * 从人群条件中提取所有使用的标签
     */
    private Set<String> extractLabelsFromCrowd(CrowdMetaInfoDO crowdMetaInfoDO) {
        Set<String> labelCodes = new HashSet<>();
        
        for (LabelCrowdConditions.LabelGroup labelGroup : crowdMetaInfoDO.getConditions().getGroup()) {
            if (Objects.isNull(labelGroup) || CollectionUtils.isEmpty(labelGroup.getLabel())) {
                continue;
            }
            
            for (LabelCrowdConditions.LabelValue labelValue : labelGroup.getLabel()) {
                if (StringUtils.isNotBlank(labelValue.getName())) {
                    labelCodes.add(labelValue.getName());
                }
            }
        }
        
        return labelCodes;
    }

    /**
     * 计算标签使用量（包含间接使用的父人群）
     */
    private Long calculateLabelUseAmountWithParents(Set<Long> directCrowdIds) {
        if (CollectionUtils.isEmpty(directCrowdIds)) {
            return 0L;
        }
        
        int currentDepth = 0;
        Set<Long> crowdIdList = new HashSet<>(directCrowdIds);
        Set<Long> finalCrowdIdSet = new HashSet<>(directCrowdIds);
        
        // 根据人群id，查询间接使用的人群id
        while (currentDepth < labelUseAmountStatMaxDepth && !crowdIdList.isEmpty()) {
            Set<Long> tmpCrowdIdSet = new HashSet<>();
            crowdIdList.forEach(crowdId -> {
                List<Long> combinedCrowdIds = crowdChildParentRelationsService.queryParentCrowdIds(crowdId);
                if (CollectionUtils.isNotEmpty(combinedCrowdIds)) {
                    tmpCrowdIdSet.addAll(combinedCrowdIds);
                }
            });

            currentDepth++;
            crowdIdList.clear();
            if (CollectionUtils.isNotEmpty(tmpCrowdIdSet)) {
                crowdIdList.addAll(tmpCrowdIdSet);
                finalCrowdIdSet.addAll(tmpCrowdIdSet);
            }
        }
        
        return (long) finalCrowdIdSet.size();
    }

    /**
     * 插入或更新记录
     * @param labelCode
     * @param labelUseAmount
     * @return
     */
    private Boolean upsertRecord(String labelCode, Long labelUseAmount) {
        String outputDate = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
        LabelStatisticsBO labelStatisticsBO = labelStatisticsService.queryByLabelCodeAndDate(labelCode, outputDate);
        if (Objects.isNull(labelStatisticsBO)) {
            labelStatisticsBO = new LabelStatisticsBO();
            labelStatisticsBO.setLabelCode(labelCode);
            labelStatisticsBO.setOutputDate(outputDate);
            labelStatisticsBO.setUseAmount(labelUseAmount);
            return labelStatisticsService.insert(labelStatisticsBO);
        } else {
            labelStatisticsBO.setUseAmount(labelUseAmount);
            return labelStatisticsService.update(labelStatisticsBO) > 0;
        }
    }
}
