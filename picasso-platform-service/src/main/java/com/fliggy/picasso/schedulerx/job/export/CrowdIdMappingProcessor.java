package com.fliggy.picasso.schedulerx.job.export;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.common.enums.crowd.CrowdIdMappingTaskStatusEnum;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.entity.vo.CrowdIdMappingTaskVO;
import com.fliggy.picasso.service.crowd.CrowdIdMappingTaskService;
import com.fliggy.picasso.service.lindorm.OneIdMappingService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;
import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.ODPS_ID_MAPPING_TABLE_NAME;

/**
 * <AUTHOR>
 */
@Component
public class CrowdIdMappingProcessor extends MapJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);

    public static final String DISPATCH_TASK_NAME = "dispatch";

    @Resource
    private CrowdIdMappingTaskService crowdIdMappingTaskService;

    @Resource(name = "scrmOneIdMappingServiceImpl")
    private OneIdMappingService oneIdMappingService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String taskName = context.getTaskName();
        Object task = context.getTask();
        try {
            if (isRootTask(context)) {
                List<CrowdIdMappingTaskVO> crowdIdMappingTaskVOList;
                String param = context.getInstanceParameters()==null ? context.getJobParameters() : context.getInstanceParameters();
                if (StringUtils.isNotEmpty(param)) {
                    List<String> ids = new ArrayList<>(Arrays.asList(param.split(",")));
                    crowdIdMappingTaskVOList = crowdIdMappingTaskService.queryByIds(ids.stream().map(Long::parseLong).collect(Collectors.toList()));
                }else {
                    crowdIdMappingTaskVOList = crowdIdMappingTaskService.queryAllNeedRunTasks();
                }

                if (CollectionUtils.isNullOrEmpty(crowdIdMappingTaskVOList)) {
                    return new ProcessResult(true);
                }
                return map(crowdIdMappingTaskVOList, DISPATCH_TASK_NAME);
            }else if (DISPATCH_TASK_NAME.equals(taskName)) {
                CrowdIdMappingTaskVO crowdIdMappingTaskVO = (CrowdIdMappingTaskVO) task;

                boolean result;
                if (allowTaskRun(crowdIdMappingTaskVO)) {
                    result = runTask(crowdIdMappingTaskVO);
                }else {
                    result = updateTaskStatus(crowdIdMappingTaskVO);
                }
                return new ProcessResult(result);
            }else {
                return new ProcessResult(false, "not support taskName: " + taskName);
            }
        }catch (Exception e) {
            log.error("[CrowdIdMappingProcessor] id mapping processor failed. ", e);
            return new ProcessResult(false, "error: " + e.getMessage());
        }
    }

    private boolean allowTaskRun(CrowdIdMappingTaskVO crowdIdMappingTaskVO) {
        return CrowdIdMappingTaskStatusEnum.INIT == crowdIdMappingTaskVO.getStatus();
    }

    /**
     * 执行 id mapping 任务
     * @param crowdIdMappingTaskVO
     * @return
     */
    private boolean runTask(CrowdIdMappingTaskVO crowdIdMappingTaskVO) {
        try {
            Pair<String, String> pair = oneIdMappingService.batchIdMapping(String.valueOf(crowdIdMappingTaskVO.getCrowdId()));
            crowdIdMappingTaskVO.setExtInfo(generateExtInfo(pair, crowdIdMappingTaskVO));

            crowdIdMappingTaskVO.setStatus(CrowdIdMappingTaskStatusEnum.RUNNING);
            crowdIdMappingTaskService.update(crowdIdMappingTaskVO);
        } catch (Exception e) {
            log.error("[CrowdIdMappingProcessor] run task failed. ", e);
            return false;
        }
        return true;
    }

    private CrowdIdMappingTaskVO.CrowdIdMappingTaskExtInfo generateExtInfo(Pair<String, String> pair, CrowdIdMappingTaskVO crowdIdMappingTaskVO) {
        CrowdIdMappingTaskVO.CrowdIdMappingTaskExtInfo extInfo = crowdIdMappingTaskVO.getExtInfo()==null ?
                new CrowdIdMappingTaskVO.CrowdIdMappingTaskExtInfo() : crowdIdMappingTaskVO.getExtInfo();
        extInfo.setInstanceId(pair.getLeft());
        extInfo.setLogView(pair.getRight());
        extInfo.setIdMappingTableName(crowdIdMappingTaskService.getOdpsTableName(crowdIdMappingTaskVO));
        return extInfo;
    }

    /**
     * 坚持任务状态，并更新db
     * @param crowdIdMappingTaskVO
     * @return
     */
    private boolean updateTaskStatus(CrowdIdMappingTaskVO crowdIdMappingTaskVO) {
        if (null == crowdIdMappingTaskVO || null == crowdIdMappingTaskVO.getExtInfo()) {
            log.error("[CrowdIdMappingProcessor] update task status failed. taskBO or extInfo is null.");
            return false;
        }

        try {
            CrowdIdMappingTaskVO.CrowdIdMappingTaskExtInfo extInfo = crowdIdMappingTaskVO.getExtInfo();
            CrowdIdMappingTaskStatusEnum taskStatus = oneIdMappingService.queryIdMappingTaskStatus(extInfo.getInstanceId());
            return crowdIdMappingTaskService.updateStatus(crowdIdMappingTaskVO.getId(), taskStatus);
        } catch (Exception e) {
            log.error("[CrowdIdMappingProcessor] update task status failed. ", e);
            return false;
        }
    }
}
