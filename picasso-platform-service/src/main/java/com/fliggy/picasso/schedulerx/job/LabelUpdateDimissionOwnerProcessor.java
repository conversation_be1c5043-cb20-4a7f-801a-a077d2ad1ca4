package com.fliggy.picasso.schedulerx.job;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.employee.Amdp3271EmployeeServiceImpl;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;

@Component
public class LabelUpdateDimissionOwnerProcessor extends JavaProcessor {

    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);

    @Autowired
    private LabelInfoService labelInfoService;
    @Resource
    private Amdp3271EmployeeServiceImpl amdp3271EmployeeService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            List<LabelInfoDTO> labelInfoDTOList;
            String paramStr = StringUtils.isBlank(context.getInstanceParameters())? context.getJobParameters() : context.getInstanceParameters();
            if (StringUtils.isNotBlank(paramStr)) {
                labelInfoDTOList = labelInfoService.findByCodes(new ArrayList<>(Arrays.asList(paramStr.split(","))));
            }else {
                labelInfoDTOList = labelInfoService.listValidLabels();
            }
            if (CollectionUtils.isEmpty(labelInfoDTOList)) {
                return new ProcessResult(true);
            }

            Map<String, Employee> chargeEmployeeMap = new HashMap<>();
            Set<String> empIdList = labelInfoDTOList.stream()
                    .filter(label -> Objects.nonNull(label) && Objects.nonNull(label.getDataOwner()) && Objects.nonNull(label.getDataOwner().getEmpId()))
                    .map(label -> label.getDataOwner().getEmpId()).collect(Collectors.toSet());
            List<List<String>> partitions = partition(empIdList, 20);
            for (List<String> partition : partitions) {
                Map<String, Employee> partialResult = amdp3271EmployeeService.getDimissionChargeEmployee(partition);
                if (!partialResult.isEmpty()) {
                    chargeEmployeeMap.putAll(partialResult);
                }
            }
            if (chargeEmployeeMap.isEmpty()) {
                return new ProcessResult(true);
            }

            updateLabelOwner(labelInfoDTOList, chargeEmployeeMap);
            return new ProcessResult(true);
        } catch (Exception e) {
            return new ProcessResult(false);
        }
    }

    private List<List<String>> partition(Set<String> empIdList, int size) {
        List<String> list = new ArrayList<>(empIdList);
        List<List<String>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            partitions.add(list.subList(i, Math.min(i + size, list.size())));
        }
        return partitions;
    }

    private void updateLabelOwner(List<LabelInfoDTO> labelInfoDTOList, Map<String, Employee> chargeEmployeeMap) {
        for (LabelInfoDTO label : labelInfoDTOList) {
            if (Objects.isNull(label) || Objects.isNull(label.getDataOwner()) || Objects.isNull(label.getDataOwner().getEmpId())) {
                continue;
            }
            String empId = label.getDataOwner().getEmpId();
            if (StringUtils.isBlank(empId)) {
                continue;
            }
            empId = padEmpIdWithZeroes(empId);
            if (StringUtils.isNotBlank(empId) && chargeEmployeeMap.containsKey(empId)) {
                Employee employee = chargeEmployeeMap.get(empId);
                updateLabel(label.getId(), employee);
            }
        }
    }

    public String padEmpIdWithZeroes(String empId) {
        if (empId.length() >= 6) {
            return empId;
        }
        return String.format("%06d", Integer.parseInt(empId));
    }

    private void updateLabel(Long id, Employee employee) {
        LabelInfoDTO updateDTO = new LabelInfoDTO();
        updateDTO.setId(id);
        updateDTO.setDataOwner(employee);
        labelInfoService.updateSelectiveById(id, updateDTO);
        log.info("update label owner. id:{}, owner:{}", id, JSON.toJSONString(employee));
    }

    @AteyeInvoker(description = "批量更新标签负责人", paraDesc = "标签id列表&工号&昵称")
    public void batchUpdateLabelOwner(String ids, String empId, String nickName) {
        Employee owner = new Employee(empId, nickName);
        List<Long> idList = new ArrayList<>(Arrays.asList(ids.split(",")))
            .stream().map(Long::parseLong).collect(Collectors.toList());
        for (Long id : idList) {
            updateLabel(id, owner);
            Ateye.out.println("update label owner. id:" + id + ", owner:" + JSON.toJSONString(owner));
        }
    }
}
