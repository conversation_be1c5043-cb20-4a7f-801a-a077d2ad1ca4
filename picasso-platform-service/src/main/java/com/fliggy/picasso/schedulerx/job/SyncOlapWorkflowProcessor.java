package com.fliggy.picasso.schedulerx.job;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.action.ExecuteActionParam;
import com.fliggy.picasso.action.PhysicalProfileActionParam;
import com.fliggy.picasso.common.Constant;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.schedulerx.SchedulerxConstants;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoService;
import com.fliggy.picasso.tair.TairRDBLock;
import com.fliggy.picasso.tair.TairRDBLockManager;
import com.fliggy.picasso.workflow.SyncOlapWorkflow;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jeasy.flows.work.WorkReport;
import org.jeasy.flows.work.WorkStatus;
import org.jeasy.flows.workflow.ParallelFlowReport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.constants.TairConstants.EXPIRE_TIME_IN_SEC;
import static com.fliggy.picasso.common.constants.TairConstants.OLAP_LOCK_PREFIX;
import static com.fliggy.picasso.group.crowd.facade.hologres.CrowdHologresConfig.OLAP_SYNC_RETRY_COUNT;

/**
 * 同步分析的数据
 * 1.将画像物理表进行采样
 * 2.将采样表同步到Holo表里面
 * <AUTHOR>
 * @date 2022/6/17
 */
@Component
public class SyncOlapWorkflowProcessor extends JavaProcessor {

    private static final Logger LOG = LoggerFactory.getLogger(Constant.TASK_LOG);

    @Autowired
    private SyncOlapWorkflow syncOlapWorkflow;

    @Autowired
    private PhysicalProfileInfoService physicalProfileInfoService;

    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        this.executorService = new ThreadPoolExecutor(0, Integer.MAX_VALUE,
            60L, TimeUnit.SECONDS,
            new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("Sync-olap-Scheduler-pool-").build()
        );
    }

    @Override
    public ProcessResult process(JobContext jobContext) {
        String physicalProfileCodes = jobContext.getJobParameters();

        Map<PhysicalProfileInfoDTO, List<LabelInfoDTO>> param;
        if (StringUtils.isEmpty(physicalProfileCodes)) {
            param = physicalProfileInfoService.queryRefluxProfileAndLabels();
        } else {
            List<String> profileCodes = Lists.newArrayList(
                StringUtils.split(physicalProfileCodes, SchedulerxConstants.PARAM_SPLIT));
            param = physicalProfileInfoService.queryRefluxProfileAndLabels(profileCodes);
        }

        if (param != null && param.size() != 0) {
            List<PhysicalProfileWorkflowJob> jobs = Lists.newArrayList();
            param.forEach((profile, labels) -> {
                PhysicalProfileWorkflowJob job = new PhysicalProfileWorkflowJob(profile, labels);
                jobs.add(job);
            });

            // submit work units and wait for results
            List<Future<WorkReport>> futures;
            try {
                futures = this.executorService.invokeAll(jobs);
            } catch (InterruptedException e) {
                throw new RuntimeException("The parallel flow was interrupted while executing work units", e);
            }

            Map<PhysicalProfileWorkflowJob, Future<WorkReport>> futureMap = new HashMap<>();
            for (int index = 0; index < jobs.size(); index++) {
                futureMap.put(jobs.get(index), futures.get(index));
            }

            // gather
            List<WorkReport> results = new ArrayList<>();
            for (Map.Entry<PhysicalProfileWorkflowJob, Future<WorkReport>> entry : futureMap.entrySet()) {
                try {
                    results.add(entry.getValue().get());
                } catch (Exception e) {
                    String msg = String.format("Unable to execute work '%s'",
                        entry.getKey().getProfile().getPhysicalProfileCode());
                    LOG.error(msg, e);
                }
            }
            LOG.info("PhysicalProfileWorkflowProcessor result: {}", JSON.toJSONString(results));
            return new ProcessResult(isSucceeded(results), toPrintResult(results));
        }
        return new ProcessResult(false, "未执行，检查是否配置有效的物理画像！");
    }

    /**
     * 转换为Schedulerx的可打印结果字符串
     * @param results 多个物理画像的工作流执行报告
     * @return Schedulerx的可打印结果字符串
     */
    private String toPrintResult(List<WorkReport> results) {
        StringBuilder builder = new StringBuilder();
        results.forEach(
            result -> {
                builder.append(syncOlapWorkflow.buildWorkResult(result));
            }
        );
        return builder.toString();
    }

    /**
     * 若有一个执行失败则标记为失败
     * @param results 多个物理画像的工作流执行报告
     * @return 是否全部成功
     */
    private boolean isSucceeded(List<WorkReport> results) {
        return WorkStatus.COMPLETED == new ParallelFlowReport(results).getStatus();
    }

    @Autowired
    private TairRDBLockManager tairRDBLockManager;

    @Data
    private class PhysicalProfileWorkflowJob implements Callable<WorkReport> {

        private PhysicalProfileInfoDTO profile;
        private List<LabelInfoDTO> labels;

        public PhysicalProfileWorkflowJob(PhysicalProfileInfoDTO profile,
                                          List<LabelInfoDTO> labels) {
            this.profile = profile;
            this.labels = labels;
        }

        @Override
        public WorkReport call() {
            // 加锁
            TairRDBLock lock = tairRDBLockManager.getLock(OLAP_LOCK_PREFIX + profile.getPhysicalProfileCode());

            if (lock.tryGetDistributedLock(EXPIRE_TIME_IN_SEC)) {
                try {
                    PhysicalProfileActionParam workflowParam = new PhysicalProfileActionParam(profile, labels);
                    int retryCount = 0;
                    // 支持多次重试
                    WorkReport result = null;
                    while (retryCount < OLAP_SYNC_RETRY_COUNT) {
                        LOG.info("[SYNC SyncOlapWorkflowProcessor] call tryCount:{}, workflowParam:{}",
                            retryCount, workflowParam);
                        result = syncOlapWorkflow.run(ExecuteActionParam.of(workflowParam));
                        if (result.getStatus() == WorkStatus.COMPLETED) {
                            return result;
                        }
                        retryCount += 1;
                    }
                    // 未成功返回最后一次重试结果
                    return result;
                } catch (Exception e) {
                    String msg = "Execute PhysicalProfile Workflow Error!";
                    LOG.error(msg, e);
                    throw new RuntimeException(msg, e);
                } finally {
                    lock.releaseDistributedLock();
                }
            } else {
                String msg = String.format("profile %s is already execute! try again later!",
                    profile.getPhysicalProfileCode());
                throw new RuntimeException(msg);
            }
        }
    }
}
