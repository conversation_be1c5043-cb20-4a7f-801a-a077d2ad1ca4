package com.fliggy.picasso.schedulerx.job.statistic;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapReduceJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.alarm.dingtalk.DingDingNotifySender;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.constants.DingTalkConstants;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.entity.bo.LabelEnumDataStatBO;
import com.fliggy.picasso.service.label.LabelEnumDataStatService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.taobao.ateye.annotation.Switch;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;

/**
 * 标签取值分布波动监控告警任务
 * 监控标签取值分布相比昨天波动情况，波动超过50%则发送告警
 */
@Component
public class LabelEnumDistributionAlarmProcessor extends MapReduceJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);

    @Switch(name = "标签取值分布波动阈值")
    public static double FLUCTUATE_THRESHOLD = 0.5;

    @Switch(name = "标签取值数量阈值")
    public static long ENUM_COUNT_THRESHOLD = 100;

    @Switch(name = "标签值数量最小值")
    public static long ENUM_VALUE_MIN_COUNT = 10;

    @Resource
    private LabelInfoService labelInfoService;
    @Resource
    private LabelEnumDataStatService labelEnumDataStatService;
    @Resource
    private DingDingNotifySender dingDingNotifySender;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String dispatchName = "label_enum_distribution_monitor_dispatch";
        String taskName = context.getTaskName();
        
        try {
            if (isRootTask(context)) {
                // 获取所有离线标准标签
                List<LabelInfoDTO> labelList;
                String paramStr = StringUtils.isBlank(context.getInstanceParameters()) ? context.getJobParameters() : context.getInstanceParameters();
                if (StringUtils.isNotBlank(paramStr)) {
                    labelList = labelInfoService.findByCodes(Arrays.asList(paramStr.split(",")));
                } else {
                    labelList = labelInfoService.listOfflineStandardLabels();
                }

                if (CollectionUtils.isEmpty(labelList)) {
                    return new ProcessResult(true);
                }
                return map(labelList, dispatchName);
            } else if (taskName.equals(dispatchName)) {
                LabelInfoDTO labelDTO = (LabelInfoDTO) context.getTask();
                if (Objects.isNull(labelDTO)) {
                    return new ProcessResult(true);
                }
                
                // 检查标签取值分布波动
                List<EnumDistributionFluctuateInfo> fluctuateInfos = checkLabelEnumDistributionFluctuation(labelDTO);
                return new ProcessResult(true, JSON.toJSONString(fluctuateInfos));
            } else {
                return new ProcessResult(true);
            }
        } catch (Exception e) {
            log.error("标签取值分布波动检查失败", e);
            return new ProcessResult(false, e.getMessage());
        }
    }
    
    @Override
    public ProcessResult reduce(JobContext context) {
        Map<Long, String> taskResults = context.getTaskResults();
        if (taskResults == null || taskResults.isEmpty()) {
            return new ProcessResult(true);
        }
        
        List<EnumDistributionFluctuateInfo> allFluctuateInfos = new ArrayList<>();
        for (String result : taskResults.values()) {
            if (StringUtils.isEmpty(result)) {
                continue;
            }
            List<EnumDistributionFluctuateInfo> fluctuateInfos = JSON.parseArray(result, EnumDistributionFluctuateInfo.class);
            if (CollectionUtils.isEmpty(fluctuateInfos)) {
                continue;
            }
            allFluctuateInfos.addAll(fluctuateInfos);
        }
        
        if (CollectionUtils.isNotEmpty(allFluctuateInfos)) {
            String yesterday = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
            String dayBeforeYesterday = DateTime.now().minusDays(2).toString(DateUtils.YMD_FORMAT02);
            sendAlarm(allFluctuateInfos, yesterday, dayBeforeYesterday);
        }
        return new ProcessResult(true);
    }

    /**
     * 检查标签取值分布波动
     */
    private List<EnumDistributionFluctuateInfo> checkLabelEnumDistributionFluctuation(LabelInfoDTO labelDTO) {
        List<EnumDistributionFluctuateInfo> result = new ArrayList<>();
        String yesterday = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
        String dayBeforeYesterday = DateTime.now().minusDays(2).toString(DateUtils.YMD_FORMAT02);
        
        // 超过阈值则不监控
        Long yesterdayCount = labelEnumDataStatService.countByLabelCodeAndBizdate(labelDTO.getCode(), yesterday);
        if (yesterdayCount == null || yesterdayCount > ENUM_COUNT_THRESHOLD) {
            return result;
        }
        
        // 查询取值分布
        List<LabelEnumDataStatBO> yesterdayStats = labelEnumDataStatService.queryByLabelCodeAndBizdate(labelDTO.getCode(), yesterday);
        List<LabelEnumDataStatBO> dayBeforeYesterdayStats = labelEnumDataStatService.queryByLabelCodeAndBizdate(labelDTO.getCode(), dayBeforeYesterday);
        if (CollectionUtils.isEmpty(yesterdayStats) || CollectionUtils.isEmpty(dayBeforeYesterdayStats)) {
            return result;
        }
        Map<String, Long> dayBeforeYesterdayStatsMap = dayBeforeYesterdayStats.stream()
                .collect(Collectors.toMap(LabelEnumDataStatBO::getEnumCode, LabelEnumDataStatBO::getCnt, (v1, v2) -> v1));

        // 计算波动率
        for (LabelEnumDataStatBO yesterdayStat : yesterdayStats) {
            String enumCode = yesterdayStat.getEnumCode();
            Long yesterdayCnt = yesterdayStat.getCnt();
            Long dayBeforeYesterdayCnt = dayBeforeYesterdayStatsMap.getOrDefault(enumCode, 0L);
            if (dayBeforeYesterdayCnt <= 0L) {
                continue;
            }
            // 过滤取值数量太少的
            if (yesterdayCnt < ENUM_VALUE_MIN_COUNT && dayBeforeYesterdayCnt < ENUM_VALUE_MIN_COUNT) {
                continue;
            }

            double fluctuateRate = Math.abs((double) (yesterdayCnt - dayBeforeYesterdayCnt)) / dayBeforeYesterdayCnt;
            if (fluctuateRate > FLUCTUATE_THRESHOLD) {
                EnumDistributionFluctuateInfo fluctuateInfo = new EnumDistributionFluctuateInfo();
                fluctuateInfo.setLabelCode(labelDTO.getCode());
                fluctuateInfo.setLabelName(labelDTO.getName());
                fluctuateInfo.setEnumCode(enumCode);
                fluctuateInfo.setYesterdayCnt(yesterdayCnt);
                fluctuateInfo.setDayBeforeYesterdayCnt(dayBeforeYesterdayCnt);
                fluctuateInfo.setFluctuateRate(fluctuateRate);
                result.add(fluctuateInfo);
            }
        }
        return result;
    }

    /**
     * 发送钉钉告警
     */
    private void sendAlarm(List<EnumDistributionFluctuateInfo> fluctuateInfos, String yesterday, String dayBeforeYesterday) {
        StringBuilder content = new StringBuilder();
        content.append("### 【诸葛离线标签监控-取值分布波动告警】\n\n");
        content.append("以下标签取值分布波动超过").append(FLUCTUATE_THRESHOLD * 100).append("%，请关注：\n\n");
        
        // 按标签分组
        Map<String, List<EnumDistributionFluctuateInfo>> labelGroupedInfos = fluctuateInfos.stream()
                .collect(Collectors.groupingBy(EnumDistributionFluctuateInfo::getLabelCode));
        
        for (Map.Entry<String, List<EnumDistributionFluctuateInfo>> entry : labelGroupedInfos.entrySet()) {
            String labelCode = entry.getKey();
            List<EnumDistributionFluctuateInfo> labelFluctuateInfos = entry.getValue();
            if (CollectionUtils.isEmpty(labelFluctuateInfos)) {
                continue;
            }

            String labelName = labelFluctuateInfos.get(0).getLabelName();
            content.append("### 标签：").append(labelName).append("(").append(labelCode).append(")\n\n");

            content.append("| 枚举值 | ").append(yesterday).append("数量 | ")
                .append(dayBeforeYesterday).append("数量 | 波动率 |\n");
            content.append("| --- | --- | --- | --- |\n");

            for (EnumDistributionFluctuateInfo info : labelFluctuateInfos) {
                BigDecimal rate = BigDecimal.valueOf(info.getFluctuateRate() * 100).setScale(2, RoundingMode.HALF_UP);
                content.append("| ").append(info.getEnumCode()).append(" | ")
                    .append(info.getYesterdayCnt()).append(" | ")
                    .append(info.getDayBeforeYesterdayCnt()).append(" | ")
                    .append(rate).append("% |\n");
            }

            content.append("\n");
        }
        
        content.append("\n---\n\n");
        content.append("[诸葛平台](https://zhuge.alibaba-inc.com)");
        
        // 发送钉钉告警
        dingDingNotifySender.sendDingTalkGroupMsg(
                "诸葛离线标签取值分布波动告警",
                content.toString(),
                DingTalkConstants.OFFLINE_LABEL_ALARM_ACCESS_TOKEN);
        
        log.info("标签取值分布波动告警，content: {}", content.toString());
    }
    
         /**
      * 标签枚举取值分布波动信息
      */
     @Data
     public static class EnumDistributionFluctuateInfo {
         /**
          * 标签编码
          */
         private String labelCode;
         
         /**
          * 标签名称
          */
         private String labelName;

         /**
          * 枚举值编码
          */
         private String enumCode;
         
         /**
          * 昨天统计值
          */
         private Long yesterdayCnt;
         
         /**
          * 前天统计值
          */
         private Long dayBeforeYesterdayCnt;
         
         /**
          * 波动率
          */
         private Double fluctuateRate;
     }
} 