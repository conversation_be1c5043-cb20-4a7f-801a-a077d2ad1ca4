package com.fliggy.picasso.schedulerx.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.common.domain.CrowdExportTaskDO;
import com.fliggy.picasso.common.domain.crowd.export.CrowdExportExtInfo;
import com.fliggy.picasso.common.domain.crowd.export.CrowdExportSyncInfo;
import com.fliggy.picasso.common.enums.CrowdCenterSyncStatus;
import com.fliggy.picasso.service.crowd.CrowdCenterService;
import com.fliggy.picasso.service.crowd.export.CrowdExportService;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;
import static com.fliggy.picasso.common.Constant.TASK_LOG;

@Component
public class CrowdCenterStatusProcessor extends MapJobProcessor {
    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);

    private static final String level1Dispatch = "Level1Dispatch";

    @Resource
    private CrowdExportService crowdExportService;
    @Resource
    private CrowdCenterService crowdCenterService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String taskName = context.getTaskName();
        Object task = context.getTask();
        if (isRootTask(context)) {
            List<CrowdExportTaskDO> crowdExportTaskDOS = null;
            String params = StringUtils.isEmpty(context.getInstanceParameters()) ? context.getJobParameters() : context.getInstanceParameters();
            if (StringUtils.isNotEmpty(params)) {
                List<Long> exportTaskIds = new ArrayList<>(Arrays.asList(params.split(","))).stream().map(Long::parseLong).collect(Collectors.toList());
                crowdExportTaskDOS = crowdExportService.queryByIds(exportTaskIds);
            }else {
                crowdExportTaskDOS = crowdExportService.queryLatelySyncCrowdCenterTask();
            }

            if (CollectionUtils.isEmpty(crowdExportTaskDOS)) {
                return new ProcessResult(true);
            }

            return map(crowdExportTaskDOS, level1Dispatch);
        }else if (level1Dispatch.equals(taskName)) {
            CrowdExportTaskDO crowdExportTaskDO = (CrowdExportTaskDO) task;
            CrowdExportExtInfo exportExtInfo = JSON.parseObject(crowdExportTaskDO.getExtInfo(), CrowdExportExtInfo.class);
            CrowdExportSyncInfo syncInfo = exportExtInfo.getSyncInfo();
            String status = crowdCenterService.queryOdpsCrowdStatus(syncInfo.getCrowdId());
            updateCrowdExportTaskSyncStatus(crowdExportTaskDO.getId(), exportExtInfo, CrowdCenterSyncStatus.convertCrowdCenterSyncStatus(status));
            return new ProcessResult(true);
        }else {
            log.error(String.format("【人群中心】不支持的任务类型: %s", taskName));
            return new ProcessResult(false);
        }
    }

    private void updateCrowdExportTaskSyncStatus(Long taskId, CrowdExportExtInfo exportExtInfo, CrowdCenterSyncStatus status) {
        CrowdExportTaskDO updateTask = new CrowdExportTaskDO();
        updateTask.setId(taskId);
        exportExtInfo.getSyncInfo().setStatus(status.name());
        updateTask.setExtInfo(JSON.toJSONString(exportExtInfo));
        crowdExportService.update(updateTask);
    }

    @AteyeInvoker(description = "")
    public void needUpdateStatusTask() {
        List<CrowdExportTaskDO> crowdExportTaskDOS = crowdExportService.queryLatelySyncCrowdCenterTask();
        Ateye.out.println(crowdExportTaskDOS.toString());
    }
}
