package com.fliggy.picasso.schedulerx.crowd;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

public class UpdateModel implements Serializable {

    /**
     * 更新类型：every_day 或者 dependence
     */
    private String updateType;
    /**
     * 依赖选项：如获客宝
     */
    private String dependenceBizOption;

    public UpdateModel() {

    }

    public UpdateModel(String updateType, String dependenceBizOption) {
        this.updateType = updateType;
        this.dependenceBizOption = dependenceBizOption;
    }

    public String getUpdateType() {
        return updateType;
    }

    public void setUpdateType(String updateType) {
        this.updateType = updateType;
    }

    public String getDependenceBizOption() {
        return dependenceBizOption;
    }

    public void setDependenceBizOption(String dependenceBizOption) {
        this.dependenceBizOption = dependenceBizOption;
    }

    public JSONObject toJSONObject() {
        JSONObject o = new JSONObject();
        o.fluentPut("updateType", updateType)
                .fluentPut("dependenceBizOption", dependenceBizOption);
        return o;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
