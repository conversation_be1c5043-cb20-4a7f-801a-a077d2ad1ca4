package com.fliggy.picasso.schedulerx.job.alarm;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoQuery;
import com.fliggy.picasso.common.domain.crowd.build.CrowdBuildDagConfigDO;
import com.fliggy.picasso.common.enums.alarm.AlarmSceneTypeEnum;
import com.fliggy.picasso.common.enums.alarm.RoarAlarmTypeEnum;
import com.fliggy.picasso.common.enums.group.GroupBuildDagStatusEnum;
import com.fliggy.picasso.config.SwitchConfig;
import com.fliggy.picasso.entity.alarm.CrowdMetaInfoWithAlarmInfos;
import com.fliggy.picasso.entity.bo.CrowdAlarmInfoBO;
import com.fliggy.picasso.entity.bo.CrowdMonitorConfigBO;
import com.fliggy.picasso.group.crowd.dag.CrowdBuildDagConfigService;
import com.fliggy.picasso.service.alarm.CrowdAlarmFactory;
import com.fliggy.picasso.service.alarm.CrowdMonitorConfigService;
import com.fliggy.picasso.service.alarm.impl.AbstractCrowdAlarmServiceImpl;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class CrowdBuildSuccessAlarmProcessor extends JavaProcessor {

    @Resource
    private CrowdBuildDagConfigService crowdBuildDagConfigService;
    @Resource
    private CrowdService crowdService;
    @Resource
    private CrowdMonitorConfigService crowdMonitorConfigService;
    @Resource
    private CrowdAlarmFactory crowdAlarmFactory;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        // 当日需要日更新人群
        CrowdMetaInfoQuery query = new CrowdMetaInfoQuery();
        if (SwitchConfig.COMMON_DEBUG_SWITCH) {
            query.setCreator(SwitchConfig.ALARM_DEBUG_CREATOR_NAME);
        }
        query.setNeedUpdate((byte)1);
        query.setDeleted((byte)0);
        query.setExpiredDateStart(new Date());
        List<CrowdMetaInfoDO> needUpdateCrowdDOList = crowdService.listQuery(query, 1, 10000);
        if (CollectionUtils.isEmpty(needUpdateCrowdDOList)) {
            return new ProcessResult(true);
        }
        List<CrowdMetaInfoWithAlarmInfos> validNeedUpdateCrowdDOList = filterNonAlarmConfigCrowd(needUpdateCrowdDOList);
        if (CollectionUtils.isEmpty(validNeedUpdateCrowdDOList)) {
            return new ProcessResult(true);
        }
        Map<String, Map<RoarAlarmTypeEnum, List<Long>>> needUpdateCrowdReceiverMap = getCrowdReceiverMap(validNeedUpdateCrowdDOList);
        if (needUpdateCrowdReceiverMap.isEmpty()) {
            return new ProcessResult(true);
        }

        // 当日日更新成功人群
        List<Long> dagIds = crowdBuildDagConfigService.queryAllLatestConfigIdsToday();
        if (CollectionUtils.isEmpty(dagIds)) {
            return new ProcessResult(true);
        }
        List<CrowdBuildDagConfigDO> dagConfigDOList = crowdBuildDagConfigService.queryByIds(dagIds);
        if (CollectionUtils.isEmpty(dagConfigDOList)) {
            return new ProcessResult(true);
        }
        List<Long> updateSuccessCrowdIds = dagConfigDOList.stream()
            .filter(config -> config.getDagStatus() == GroupBuildDagStatusEnum.SUCCESS)
            .map(CrowdBuildDagConfigDO::getCrowdId).collect(Collectors.toList());
        List<CrowdMetaInfoWithAlarmInfos> updateSuccessCrowds = validNeedUpdateCrowdDOList.stream()
            .filter(crowd -> Objects.nonNull(crowd.getCrowdMetaInfoDO()) &&
                updateSuccessCrowdIds.contains(crowd.getCrowdMetaInfoDO().getId())).collect(Collectors.toList());
        Map<String, Map<RoarAlarmTypeEnum, List<Long>>> updateSuccessCrowdReceiverMap = getCrowdReceiverMap(updateSuccessCrowds);

        // 发送邮件通知
        sendCrowdBuildSuccessAlarm(needUpdateCrowdReceiverMap, updateSuccessCrowdReceiverMap);
        return new ProcessResult(true);
    }

    /**
     * 过滤掉没有邮件配置的人群
     */
    private List<CrowdMetaInfoWithAlarmInfos> filterNonAlarmConfigCrowd(List<CrowdMetaInfoDO> crowdDOList) {
        if (CollectionUtils.isEmpty(crowdDOList)) {
            return Lists.newArrayList();
        }

        List<Long> crowdIds = crowdDOList.stream().map(CrowdMetaInfoDO::getId).collect(Collectors.toList());
        List<CrowdMonitorConfigBO> configBOList = crowdMonitorConfigService.queryByCrowdIdsAndSceneTypesWithDefault(crowdIds, AlarmSceneTypeEnum.CROWD_BUILD_SUCCESS);
        if (CollectionUtils.isEmpty(configBOList)) {
            return Lists.newArrayList();
        }
        Map<Long, CrowdMonitorConfigBO> configBOMap = configBOList.stream().filter(config -> Objects.nonNull(config) && config.getIsValid())
            .collect(Collectors.toMap(CrowdMonitorConfigBO::getCrowdId, config -> config));
        if (configBOMap.isEmpty()) {
            return Lists.newArrayList();
        }

        List<CrowdMetaInfoWithAlarmInfos> result = new ArrayList<>();
        for (CrowdMetaInfoDO crowdMetaInfoDO : crowdDOList) {
            CrowdMonitorConfigBO config = configBOMap.get(crowdMetaInfoDO.getId());
            if (config == null || CollectionUtils.isEmpty(config.getAlarmTypes()) || CollectionUtils.isEmpty(config.getReceiverTypes())) {
                continue;
            }
            result.add(new CrowdMetaInfoWithAlarmInfos(crowdMetaInfoDO, config.getAlarmTypes()));
        }
        return result;
    }

    private Map<String, Map<RoarAlarmTypeEnum, List<Long>>> getCrowdReceiverMap(List<CrowdMetaInfoWithAlarmInfos> crowdWithAlarmList) {
        Map<String, Map<RoarAlarmTypeEnum, List<Long>>> result = new HashMap<>();
        for (CrowdMetaInfoWithAlarmInfos crowdWithAlarm : crowdWithAlarmList) {
            CrowdMetaInfoDO crowdMetaInfoDO = crowdWithAlarm.getCrowdMetaInfoDO();
            List<RoarAlarmTypeEnum> alarmTypes = crowdWithAlarm.getAlarmTypes();
            if (Objects.isNull(crowdMetaInfoDO) || CollectionUtils.isEmpty(alarmTypes)) {
                continue;
            }

            List<Employee> alarmReceivers = crowdMonitorConfigService.getAlarmReceivers(crowdMetaInfoDO, AlarmSceneTypeEnum.CROWD_BUILD_SUCCESS);
            for (Employee receiver : alarmReceivers) {
                if (receiver == null || receiver.getEmpId() == null) {
                    continue;
                }

                for (RoarAlarmTypeEnum alarmType : alarmTypes) {
                    result.computeIfAbsent(receiver.getEmpId(), k -> new HashMap<>())
                        .computeIfAbsent(alarmType, k -> new ArrayList<>())
                        .add(crowdMetaInfoDO.getId());
                }
            }
        }
        return result;
    }

    private void sendCrowdBuildSuccessAlarm(Map<String, Map<RoarAlarmTypeEnum, List<Long>>> needUpdateCrowdReceiverMap,
        Map<String, Map<RoarAlarmTypeEnum, List<Long>>> updateSuccessCrowdReceiverMap) {
        if (needUpdateCrowdReceiverMap.isEmpty()) {
            return;
        }

        AbstractCrowdAlarmServiceImpl alarmService = crowdAlarmFactory.choose(AlarmSceneTypeEnum.CROWD_BUILD_SUCCESS);
        if (Objects.isNull(alarmService)) {
            return;
        }
        for (Map.Entry<String, Map<RoarAlarmTypeEnum, List<Long>>> entry : needUpdateCrowdReceiverMap.entrySet()) {
            String empId = entry.getKey();
            if (StringUtils.isEmpty(empId) || entry.getValue().isEmpty()) {
                continue;
            }

            for (Map.Entry<RoarAlarmTypeEnum, List<Long>> alarmTypeEntry : entry.getValue().entrySet()) {
                RoarAlarmTypeEnum alarmType = alarmTypeEntry.getKey();
                if (alarmType == null || alarmTypeEntry.getValue().isEmpty()) {
                    continue;
                }

                CrowdAlarmInfoBO crowdAlarmInfoBO = new CrowdAlarmInfoBO();
                crowdAlarmInfoBO.setSceneType(AlarmSceneTypeEnum.CROWD_BUILD_SUCCESS);
                crowdAlarmInfoBO.setAlarmType(alarmType);
                List<Long> crowdIds;
                if (MapUtils.isNotEmpty(updateSuccessCrowdReceiverMap.get(empId)) &&
                    CollectionUtils.isNotEmpty(updateSuccessCrowdReceiverMap.get(empId).get(alarmType))) {
                    crowdIds = updateSuccessCrowdReceiverMap.get(empId).get(alarmType);
                } else {
                    crowdIds = Lists.newArrayList();
                }
                crowdAlarmInfoBO.setCrowdIds(crowdIds);
                crowdAlarmInfoBO.setReceiver(empId);
                crowdAlarmInfoBO.setNeedUpdateCnt(alarmTypeEntry.getValue().size());
                crowdAlarmInfoBO.setSingleTrigger(false);
                alarmService.sendAlarm(crowdAlarmInfoBO);
            }
        }
    }

}
