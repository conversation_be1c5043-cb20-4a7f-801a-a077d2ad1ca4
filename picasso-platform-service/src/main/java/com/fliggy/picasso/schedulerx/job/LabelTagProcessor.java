package com.fliggy.picasso.schedulerx.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.domain.label.LabelTagConfig;
import com.fliggy.picasso.common.enums.score.LabelTagEnum;
import com.fliggy.picasso.common.enums.score.ScoreTypeEnum;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.service.label.LabelInfoParameter;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.score.ScoreInfoService;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.fliggy.picasso.common.Constant.DTS_LOG;
import static com.fliggy.picasso.config.SwitchConfig.LABEL_TAG_BLACK_LIST_STR;

@Component
public class LabelTagProcessor extends JavaProcessor {

    private static final Logger logger = LoggerFactory.getLogger(DTS_LOG);

    @Resource
    private ScoreInfoService scoreInfoService;
    @Resource
    private LabelInfoService labelInfoService;

    @Switch(description = "标签打标-取top50")
    public int labelTagTopLimit = 50;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        List<LabelInfoDTO> allValidLabels = null;
        String paramStr = StringUtils.isBlank(context.getInstanceParameters())? context.getJobParameters() : context.getInstanceParameters();
        if (StringUtils.isNotBlank(paramStr)) {
            List<String> labelCodes = new ArrayList<>(Arrays.asList(paramStr.split(",")));
            allValidLabels = labelInfoService.findByCodes(labelCodes);
        } else {
            allValidLabels = labelInfoService.listValidLabels();
        }
        // 开了质保标开关
        //List<String> openQualityTagLabels = allValidLabels.stream().filter(label -> {
        //    LabelTagConfig tagConfig = label.getTagConfig();
        //    return Objects.nonNull(tagConfig) && Objects.nonNull(tagConfig.getOpenQualityTag()) && tagConfig.getOpenQualityTag();
        //}).map(LabelInfoDTO::getCode).collect(Collectors.toList());
        // 开了热度标开关
        List<String> openHotTagLabels = allValidLabels.stream().filter(label -> {
            LabelTagConfig tagConfig = label.getTagConfig();
            return Objects.nonNull(tagConfig) && Objects.nonNull(tagConfig.getOpenHotTag()) && tagConfig.getOpenHotTag();
        }).map(LabelInfoDTO::getCode).collect(Collectors.toList());

        // 黑名单
        List<String> labelTagBlackList = new ArrayList<>();
        if (StringUtils.isNotBlank(LABEL_TAG_BLACK_LIST_STR)) {
            labelTagBlackList = Arrays.asList(LABEL_TAG_BLACK_LIST_STR.split(","));
        }
        try {
            clearLabelTags();
            List<String> finalLabelTagBlackList = labelTagBlackList;
            // 质保标暂时先不打
            //List<String> highQualityLabels = scoreInfoService.queryTopLabelsByScoreTypeAndLabelCodes(openQualityTagLabels, ScoreTypeEnum.QUALITY_SCORE, labelTagTopLimit);
            //List<String> finalHighQualityLabels = highQualityLabels.stream().filter(label -> !finalLabelTagBlackList.contains(label)).collect(Collectors.toList());

            List<String> highHotLabels = scoreInfoService.queryTopLabelsByScoreTypeAndLabelCodes(openHotTagLabels, ScoreTypeEnum.HOT_SCORE, labelTagTopLimit);
            List<String> finalHighHotLabels = highHotLabels.stream().filter(label -> !finalLabelTagBlackList.contains(label)).collect(Collectors.toList());

            // 好评标暂时先不打
//            List<String> goodLabels = scoreInfoService.queryTopLabelsByScoreType(ScoreTypeEnum.FEEDBACK_SCORE, labelTagTopLimit);
//            List<String> finalGoodLabels = goodLabels.stream().filter(label -> !finalLabelTagBlackList.contains(label)).collect(Collectors.toList());
            Map<String, Set<String>> labelTagsMap = parseLabelTags(new ArrayList<>(), finalHighHotLabels, new ArrayList<>());
            if (MapUtils.isEmpty(labelTagsMap)) {
                return new ProcessResult(true);
            }

            handleLabelTags(labelTagsMap);
            return new ProcessResult(true);
        } catch (Exception e) {
            logger.error("标签打标异常", e);
            return new ProcessResult(false, e.getMessage());
        }
    }

    /**
     * 清理标签tag
     */
    private void clearLabelTags() {
        Set<LabelInfoDTO> needClearLabels = new HashSet<>();
        List<LabelTagEnum> needUpdateLabelTags = LabelTagEnum.needUpdateLabelTagList();
        needUpdateLabelTags.forEach(tag -> {
            needClearLabels.addAll(queryLabelByLabelTag(tag));
        });

        needClearLabels.forEach(label -> {
            if (Objects.isNull(label) || CollectionUtils.isNullOrEmpty(label.getTagList())) {
                return;
            }

            List<String> tags = label.getTagList();
            needUpdateLabelTags.forEach(tag -> tags.remove(tag.getCode()));
            label.setTagList(tags);
            labelInfoService.updateSelectiveById(label.getId(), label);
        });
    }

    private List<LabelInfoDTO> queryLabelByLabelTag(LabelTagEnum labelTag) {
        LabelInfoParameter labelInfoParameter = new LabelInfoParameter();
        labelInfoParameter.setLabelTag(labelTag.getCode());
        return labelInfoService.query(labelInfoParameter);
    }

    /**
     * 解析标签tags
     * @param highQualityLabels
     * @param highHotLabels
     * @param goodLabels
     * @return
     */
    private Map<String, Set<String>> parseLabelTags(List<String> highQualityLabels, List<String> highHotLabels, List<String> goodLabels) {
        Map<String, Set<String>> result = new HashMap<>();
        if (CollectionUtils.isNullOrEmpty(highQualityLabels) && CollectionUtils.isNullOrEmpty(highHotLabels) && CollectionUtils.isNullOrEmpty(goodLabels)) {
            return result;
        }

        highQualityLabels.forEach(label -> result.computeIfAbsent(label, k -> new HashSet<>()).add(LabelTagEnum.HIGH_QUALITY.getCode()));
        highHotLabels.forEach(label -> result.computeIfAbsent(label, k -> new HashSet<>()).add(LabelTagEnum.HIGH_HOT.getCode()));
        goodLabels.forEach(label -> result.computeIfAbsent(label, k -> new HashSet<>()).add(LabelTagEnum.GOOD.getCode()));
        return result;
    }

    /**
     * 处理标签tags打标
     * @param labelTagsMap
     */
    private void handleLabelTags(Map<String, Set<String>> labelTagsMap) {
        if (MapUtils.isEmpty(labelTagsMap)) {
            return;
        }
        Set<String> labelCodes = labelTagsMap.keySet();
        List<LabelInfoDTO> labelInfos = labelInfoService.findByCodes(new ArrayList<>(labelCodes));

        Map<String, LabelInfoDTO> labelCode2LabelInfoMap = new HashMap<>();
        labelInfos.forEach(labelInfo -> labelCode2LabelInfoMap.put(labelInfo.getCode(), labelInfo));

        // 更新标签tags
        labelTagsMap.forEach((labelCode, tags) -> {
            LabelInfoDTO labelInfoDTO = labelCode2LabelInfoMap.get(labelCode);
            if (labelInfoDTO == null) {
                return;
            }
            List<String> labelTags = labelInfoDTO.getTagList();
            if (CollectionUtils.isNullOrEmpty(labelTags)) {
                labelTags = new ArrayList<>();
            }
            labelTags.addAll(tags);
            labelInfoDTO.setTagList(labelTags);
            labelInfoService.updateSelectiveById(labelInfoDTO.getId(), labelInfoDTO);
        });
    }

    @AteyeInvoker(description = "测试打标", paraDesc = "labelCode&tag")
    public void testLabelTag(String labelCode, String tag) {
        LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(labelCode);
        if (labelInfoDTO == null) {
            return;
        }
        List<String> labelTags = labelInfoDTO.getTagList();
        if (CollectionUtils.isNullOrEmpty(labelTags)) {
            labelTags = new ArrayList<>();
        }
        labelTags.add(tag);
        labelInfoDTO.setTagList(labelTags);
        labelInfoService.updateSelectiveById(labelInfoDTO.getId(), labelInfoDTO);
    }

    @AteyeInvoker(description = "测试去标", paraDesc = "labelCode&tag")
    public void testRemoveTag(String labelCode, String tag) {
        LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(labelCode);
        if (labelInfoDTO == null) {
            return;
        }
        List<String> labelTags = labelInfoDTO.getTagList();
        if (CollectionUtils.isNullOrEmpty(labelTags) || !labelTags.contains(tag)) {
            return;
        }
        labelTags.remove(tag);
        labelInfoDTO.setTagList(labelTags);
        int update = labelInfoService.updateSelectiveById(labelInfoDTO.getId(), labelInfoDTO);
        Ateye.out.println(labelCode + " update:" + update);
    }

    @AteyeInvoker(description = "批量测试去标", paraDesc = "labelCodes&tag")
    public void testBatchRemoveTag(String labelCodes, String tag) {
        List<String> labelCodeList = Arrays.asList(labelCodes.split(","));
        labelCodeList.forEach(labelCode -> testRemoveTag(labelCode, tag));
    }
}
