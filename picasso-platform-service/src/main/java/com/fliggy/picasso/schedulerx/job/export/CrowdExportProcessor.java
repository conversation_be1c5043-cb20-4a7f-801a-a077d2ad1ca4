package com.fliggy.picasso.schedulerx.job.export;

import java.util.List;

import com.alibaba.common.lang.ExceptionUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.common.domain.CrowdExportTaskDO;
import com.fliggy.picasso.common.domain.crowd.CrowdExportProgress;
import com.fliggy.picasso.common.enums.sls.SlsPropertyTypeEnum;
import com.fliggy.picasso.common.enums.sls.SlsSubTaskTypeEnum;
import com.fliggy.picasso.common.enums.sls.SlsTaskTypeEnum;
import com.fliggy.picasso.common.log.JsonLogBuilder;
import com.fliggy.picasso.common.utils.NumberUtils;
import com.fliggy.picasso.service.crowd.export.CrowdExportService;
import com.fliggy.picasso.service.crowd.export.CrowdExportTask;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;
import static com.fliggy.picasso.common.Constant.TASK_LOG;

/**
 * <AUTHOR>
 * @date 2023/02/15
 */
@Component
public class CrowdExportProcessor extends MapJobProcessor {
    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);
    private static final Logger CROWD_TASK_LOGGER = LoggerFactory.getLogger(TASK_LOG);

    private final CrowdExportTask crowdExportTask;
    private final CrowdExportService crowdExportService;

    @Switch(description = "人群导出map任务大小")
    public static Integer CROWD_EXPORT_MAP_TASK_SIZE = 100;

    @Autowired
    public CrowdExportProcessor(CrowdExportTask crowdExportTask, CrowdExportService crowdExportService) {
        this.crowdExportTask = crowdExportTask;
        this.crowdExportService = crowdExportService;
    }

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String taskName = context.getTaskName();
        Object task = context.getTask();
        CrowdExportTaskDO crowdExportTaskDO = null;
        try {
            String level1Dispatch = "Level1Dispatch";
            if (isRootTask(context)) {
                String jobParameters = context.getJobParameters();
                List<CrowdExportTaskDO> exportTaskList = null;
                if (StringUtils.isNotBlank(jobParameters)) {
                    exportTaskList = crowdExportTask.queryByIds(NumberUtils.split(jobParameters));
                } else {
                    exportTaskList = crowdExportTask.queryAllExportInfo();
                }

                if (CollectionUtils.isEmpty(exportTaskList)) {
                    return new ProcessResult(true);
                }

                log.info(String.format("【人群导出】分布式调度任务数：%s", exportTaskList.size()));
                int taskSize = Math.min(CROWD_EXPORT_MAP_TASK_SIZE, exportTaskList.size());
                List<CrowdExportTaskDO> subTasks = exportTaskList.subList(0, taskSize);
                return map(subTasks, "Level1Dispatch");
            } else if (level1Dispatch.equals(taskName)) {
                crowdExportTaskDO = (CrowdExportTaskDO) task;
                CrowdExportProgress exportProgress = crowdExportTaskDO.getExportProgress();
                if (null == exportProgress || crowdExportTask.allowExecuteExportTask(crowdExportTaskDO)) {
                    Boolean result = crowdExportTask.execute(crowdExportTaskDO);
                    return new ProcessResult(result);
                }

                if (crowdExportTask.allowStatusExportTask(exportProgress)) {
                    Boolean result = crowdExportTask.updateTaskStatus(crowdExportTaskDO);
                    return new ProcessResult(result);
                }

                return new ProcessResult(true);
            } else {
                log.error(String.format("【人群导出】不支持的任务类型: %s", taskName));
                return new ProcessResult(false);
            }
        } catch (Exception ex) {
            if (crowdExportTaskDO != null) {
                String errMsg = ex.getMessage();
                CROWD_TASK_LOGGER.info(
                        JsonLogBuilder.getInstance()
                                .setTaskName(SlsTaskTypeEnum.CROWD_EXPORT.getName())
                                .setSubTaskName(SlsSubTaskTypeEnum.END_CROWD_EXPORT.getName())
                                .addProperty(SlsPropertyTypeEnum.STATUS.getName(), "failed")
                                .addProperty(SlsPropertyTypeEnum.IDS.getName(), String.valueOf(crowdExportTaskDO.getId()))
                                .addProperty(SlsPropertyTypeEnum.EXT_INFO.getName(), errMsg)
                                .build()
                );
                // 默认取前128个字符
                if (errMsg.length() > 128) {
                    errMsg = errMsg.substring(0, 128);
                }
                crowdExportTaskDO.setExportErrMsg(errMsg);
                crowdExportService.update(crowdExportTaskDO);
            }
            return new ProcessResult(false);
        }
    }

    @AteyeInvoker(description = "手动打印所有待执行导出任务")
    public void printAllExportInfoAteye() {
        try {
            List<CrowdExportTaskDO> crowdExportTaskDOList = crowdExportTask.queryAllExportInfo();
            Ateye.out.println(crowdExportTaskDOList);
        } catch (Exception e) {
            Ateye.out.println(ExceptionUtil.getStackTrace(e));
        }
    }
}
