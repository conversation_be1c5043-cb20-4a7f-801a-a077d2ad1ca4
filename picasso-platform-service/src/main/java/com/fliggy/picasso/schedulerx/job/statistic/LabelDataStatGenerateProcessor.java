package com.fliggy.picasso.schedulerx.job.statistic;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.odps.Instance;
import com.aliyun.odps.OdpsException;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.common.domain.crowd.record.AsyncTaskExtInfo;
import com.fliggy.picasso.common.domain.crowd.record.AsyncTaskRecordVO;
import com.fliggy.picasso.common.domain.label.LabelDataConfig;
import com.fliggy.picasso.common.domain.label.LabelOdpsSourceConfig;
import com.fliggy.picasso.common.enums.AsyncTaskRecordTypeEnum;
import com.fliggy.picasso.common.enums.AsyncTaskStatusEnum;
import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.entity.odps.OdpsColumn;
import com.fliggy.picasso.offline.OdpsService;
import com.fliggy.picasso.service.enumvalue.EnumDimDataDTO;
import com.fliggy.picasso.service.enumvalue.EnumDimDataService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.record.AsyncTaskRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;

@Component
public class LabelDataStatGenerateProcessor extends MapJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);

    @Autowired
    private LabelInfoService labelInfoService;
    @Autowired
    private OdpsService odpsService;
    @Autowired
    private EnumDimDataService enumDimDataService;
    @Autowired
    private AsyncTaskRecordService asyncTaskRecordService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String dispatchName = "label_data_stat_gen_dispatch_level1";
        String taskName = context.getTaskName();
        try {
            if (isRootTask(context)) {
                List<LabelInfoDTO> labelList;
                String paramStr = StringUtils.isBlank(context.getInstanceParameters())? context.getJobParameters() : context.getInstanceParameters();
                if (StringUtils.isNotBlank(paramStr)) {
                    labelList = labelInfoService.findByCodes(Arrays.asList(paramStr.split(",")));
                }else {
                    labelList = labelInfoService.listOfflineStandardLabels();
                }

                labelList = labelList.stream().filter(label -> LabelBizDataTypeEnum.isEnum(label.getDataType())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(labelList)) {
                    return new ProcessResult(true);
                }
                return map(labelList, dispatchName);
            } else if (taskName.equals(dispatchName)) {
                LabelInfoDTO labelDTO = (LabelInfoDTO) context.getTask();
                if (Objects.isNull(labelDTO) || Objects.isNull(labelDTO.getOdpsSourceConfig())) {
                    return new ProcessResult(true);
                }
                LabelOdpsSourceConfig odpsSourceConfig = labelDTO.getOdpsSourceConfig();
                if (Objects.isNull(odpsSourceConfig.getProject()) || Objects.isNull(odpsSourceConfig.getTable())) {
                    return new ProcessResult(true);
                }

                //创建异步任务
                String odpsTaskName = getTaskName(labelDTO.getCode());
                Instance instance = createTask(odpsTaskName, odpsSourceConfig, labelDTO.getDimEnumMetaId(), labelDTO.getDataConfig());
                if (Objects.isNull(instance)) {
                    return new ProcessResult(true);
                }
                Boolean result = upsertRecord(labelDTO.getCode(), odpsTaskName, instance);
                if (!result) {
                    log.error("upsert record error: " + labelDTO.getCode());
                }
                return new ProcessResult(result);
            } else {
                return new ProcessResult(false, "label data stat task error: invalid task name: " + taskName);
            }
        } catch (Exception e) {
            log.error("label data stat task generate error: " + e.getMessage());
            return new ProcessResult(false, e.getMessage());
        }
    }

    private String getTaskName(String labelCode) {
        String name = "data_" + labelCode;
        if (name.length() > 45) {
            name = name.substring(0, 45);
        }
        return odpsService.getTaskName(name);
    }

    private Instance createTask(String taskName, LabelOdpsSourceConfig odpsSourceConfig, Long dimEnumId, LabelDataConfig dataConfig) {
        String sql = generateSql(odpsSourceConfig, dimEnumId, dataConfig);
        if (StringUtils.isBlank(sql)) {
            return null;
        }

        try {
            return odpsService.startOdpsTask(OdpsConstants.ODPS_EXECUTE_PROJECT, sql, taskName, null);
        } catch (OdpsException e) {
            log.error("create task error, sql:{}, taskName:{}, error:{}", sql, taskName, e.getMessage());
        }
        return null;
    }

    private String generateSql(LabelOdpsSourceConfig odpsSourceConfig, Long dimEnumId, LabelDataConfig dataConfig) {
        if (Objects.isNull(odpsSourceConfig)) {
            return null;
        }

        String format = "select %s as field,count(*) as cnt from %s.%s " +
                "where %s='%s' and %s is not null and %s in (%s) " +
                "group by %s limit 1000;";

        String field = odpsSourceConfig.getField();
        String partition = odpsSourceConfig.getPartitionField();
        String project = odpsSourceConfig.getProject();
        String table = odpsSourceConfig.getTable();
        if (StringUtils.isBlank(field) || StringUtils.isBlank(partition) || StringUtils.isBlank(project) || StringUtils.isBlank(table)) {
            return null;
        }

        String ds = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
        String fieldConditions = generateFieldConditions(odpsSourceConfig, dimEnumId, dataConfig);
        if (StringUtils.isBlank(fieldConditions)) {
            return null;
        }

        return String.format(format, field, project, table, partition, ds, field, field, fieldConditions, field);
    }

    private String generateFieldConditions(LabelOdpsSourceConfig odpsSourceConfig, Long dimEnumId, LabelDataConfig dataConfig) {
        String type = odpsSourceConfig.getType();
        if (StringUtils.isBlank(type)) {
            return null;
        }

        List<String> enumCodes = getEnumCodes(dimEnumId, dataConfig);
        if (CollectionUtils.isEmpty(enumCodes)) {
            return null;
        }
        if (Objects.equals(type.toUpperCase(), OdpsColumn.OdpsColumnType.BIGINT.getTypeStr())) {
            return StringUtils.join(enumCodes, ",");
        } else {
            List<String> enumCodeStrs = enumCodes.stream().map(code -> "'" + code + "'").collect(Collectors.toList());
            return StringUtils.join(enumCodeStrs, ",");
        }
    }

    private List<String> getEnumCodes(Long dimEnumId, LabelDataConfig dataConfig) {
        if (Objects.isNull(dimEnumId) || dimEnumId == -1) {
            if (Objects.isNull(dataConfig) || MapUtils.isEmpty(dataConfig.getDataValue())) {
                return null;
            }
            return new ArrayList<>(dataConfig.getDataValue().keySet());
        } else {
            List<EnumDimDataDTO> enumDimDataDTOList = enumDimDataService.listByDimMetaId(dimEnumId);
            if (CollectionUtils.isEmpty(enumDimDataDTOList)) {
                return null;
            }
            return enumDimDataDTOList.stream().map(EnumDimDataDTO::getEnumCode)
                    .filter(code -> StringUtils.isNotBlank(code) && !code.contains("'")).collect(Collectors.toList());
        }
    }

    private Boolean upsertRecord(String labelCode, String taskName, Instance instance) throws OdpsException {
        if (StringUtils.isBlank(labelCode) || Objects.isNull(instance) || StringUtils.isBlank(instance.getId())) {
            return false;
        }

        AsyncTaskRecordVO asyncTaskRecordVO = asyncTaskRecordService.queryByEntity(AsyncTaskRecordTypeEnum.LABEL_ENUM_STAT, labelCode);
        if (Objects.isNull(asyncTaskRecordVO)) {
            asyncTaskRecordVO = new AsyncTaskRecordVO();
            asyncTaskRecordVO.setEntity(labelCode);
            asyncTaskRecordVO.setEntityType(AsyncTaskRecordTypeEnum.LABEL_ENUM_STAT);
            asyncTaskRecordVO.setTaskId(instance.getId());
            asyncTaskRecordVO.setTaskStatus(AsyncTaskStatusEnum.RUNNING);
            AsyncTaskExtInfo extInfo = new AsyncTaskExtInfo();
            extInfo.setTaskName(taskName);
            asyncTaskRecordVO.setExtInfo(extInfo);
            return asyncTaskRecordService.insert(asyncTaskRecordVO) > 0;
        } else {
            asyncTaskRecordVO.setGmtModified(new Date());
            asyncTaskRecordVO.setTaskId(instance.getId());
            asyncTaskRecordVO.setTaskStatus(AsyncTaskStatusEnum.RUNNING);
            AsyncTaskExtInfo extInfo = Objects.isNull(asyncTaskRecordVO.getExtInfo()) ? new AsyncTaskExtInfo() : asyncTaskRecordVO.getExtInfo();
            extInfo.setTaskName(taskName);
            asyncTaskRecordVO.setExtInfo(extInfo);
            return asyncTaskRecordService.update(asyncTaskRecordVO) > 0;
        }
    }
}
