package com.fliggy.picasso.schedulerx.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.analysis.AnalysisV2HandleResult;
import com.fliggy.picasso.analysis.AnalysisV2StatusEnum;
import com.fliggy.picasso.dao.AnalysisV2DO;
import com.fliggy.picasso.service.analysis.AnalysisV2Service;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 洞察分析2.0 任务调度 - 单机执行
 */
@Component
public class AnalysisV2Processor extends JavaProcessor {

    private static final Logger LOG = LoggerFactory.getLogger(AnalysisV2Processor.class);

    @Resource
    private AnalysisV2Service analysisV2Service;


    @Override
    public ProcessResult process(JobContext jobContext) {
        try {
            // 查询需要调度的任务
            List<AnalysisV2DO> analysisV2DOList = analysisV2Service.selectRecordByStatusAndFailTimes(AnalysisV2StatusEnum.RUNNING.getStatus(), 3);
            if (CollectionUtils.isEmpty(analysisV2DOList)){
                return new ProcessResult(true, "analysisV2DOList is null");
            }
            analysisV2DOList.forEach(p->{
                AnalysisV2HandleResult analysisV2HandleResult = analysisV2Service.handleAnalysisV2(p);
                LOG.info("AnalysisV2Processor work success. analysisId:{}, result:{}", p.getId(), JSON.toJSONString(analysisV2HandleResult));
            });
            return new ProcessResult(true);
        }catch (Exception e){
            return new ProcessResult(false, e.getMessage());
        }
    }

    @AteyeInvoker(description = "手动运行", paraDesc = "id(分析id)")
    public void manualRunAnalysis(Long id) {
        AnalysisV2DO analysisV2DO = analysisV2Service.findById(id);
        AnalysisV2HandleResult result = analysisV2Service.handleAnalysisV2(analysisV2DO);
        if (Objects.isNull(result)) {
            Ateye.out.println("任务运行失败!");
        } else {
            Ateye.out.println("任务运行成功!");
            Ateye.out.println(JSON.toJSONString(result));
        }
    }


    /**
     * 将任务状态改为初始化并运行
     */
    @AteyeInvoker(description = "将任务状态改为初始化并运行", paraDesc = "id(分析id)")
    public void resetStatusAndRetry(Long id){
        AnalysisV2DO analysisV2DO = analysisV2Service.findById(id);
        analysisV2DO.setStatus(AnalysisV2StatusEnum.INIT.getStatus());
        analysisV2Service.updateSelective(analysisV2DO, AnalysisV2StatusEnum.RUNNING.getStatus());
        Ateye.out.println("任务状态改为初始化成功!");
        AnalysisV2HandleResult analysisV2HandleResult = analysisV2Service.handleAnalysisV2(analysisV2DO);
        Ateye.out.println("任务运行成功!");
        Ateye.out.println(JSON.toJSONString(analysisV2HandleResult));
    }
}
