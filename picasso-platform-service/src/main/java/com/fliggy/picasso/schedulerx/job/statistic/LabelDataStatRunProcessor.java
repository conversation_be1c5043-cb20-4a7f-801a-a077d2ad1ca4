package com.fliggy.picasso.schedulerx.job.statistic;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.odps.Instance;
import com.aliyun.odps.data.Record;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.domain.crowd.record.AsyncTaskRecordVO;
import com.fliggy.picasso.common.enums.AsyncTaskRecordTypeEnum;
import com.fliggy.picasso.common.enums.AsyncTaskStatusEnum;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.entity.bo.LabelEnumDataStatBO;
import com.fliggy.picasso.offline.OdpsService;
import com.fliggy.picasso.service.enumvalue.EnumDimDataService;
import com.fliggy.picasso.service.label.LabelEnumDataStatService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.record.AsyncTaskRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;

@Component
public class LabelDataStatRunProcessor extends MapJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);

    @Autowired
    private AsyncTaskRecordService asyncTaskRecordService;
    @Autowired
    private OdpsService odpsService;
    @Autowired
    private EnumDimDataService enumDimDataService;
    @Autowired
    private LabelInfoService labelInfoService;
    @Autowired
    private LabelEnumDataStatService labelEnumDataStatService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String dispatchName = "label_data_stat_run_dispatch_level1";
        String taskName = context.getTaskName();
        try {
            if (isRootTask(context)) {
                List<AsyncTaskRecordVO> taskList;
                String paramStr = StringUtils.isBlank(context.getInstanceParameters())? context.getJobParameters() : context.getInstanceParameters();
                if (StringUtils.isNotBlank(paramStr)) {
                    List<Long> ids = new ArrayList<>(Arrays.asList(paramStr.split(","))).stream().map(Long::parseLong).collect(Collectors.toList());
                    taskList = asyncTaskRecordService.queryByIds(ids);
                }else {
                    taskList = asyncTaskRecordService.queryByTypeAndStatus(AsyncTaskRecordTypeEnum.LABEL_ENUM_STAT, AsyncTaskStatusEnum.RUNNING);
                }

                if (CollectionUtils.isEmpty(taskList)) {
                    return new ProcessResult(true);
                }
                return map(taskList, dispatchName);
            } else if (taskName.equals(dispatchName)) {
                AsyncTaskRecordVO taskVO = (AsyncTaskRecordVO) context.getTask();
                if (Objects.isNull(taskVO) || Objects.isNull(taskVO.getTaskId())) {
                    return new ProcessResult(true);
                }
                String taskId = taskVO.getTaskId();

                // 异步任务状态
                Instance.TaskStatus.Status status = odpsService.checkInstanceStatus(taskId);
                return new ProcessResult(handleTaskStatus(status, taskVO));
            } else {
                return new ProcessResult(false, "label data stat task error: invalid task name: " + taskName);
            }
        } catch (Exception e) {
            log.error("label data stat task run error: " + e.getMessage());
            return new ProcessResult(false, e.getMessage());
        }
    }

    /**
     * 处理任务状态
     * @param status
     * @param taskVO
     * @return
     */
    private boolean handleTaskStatus(Instance.TaskStatus.Status status, AsyncTaskRecordVO taskVO) {
        if (Objects.isNull(status) || Objects.isNull(taskVO)) {
            return false;
        }

        switch (status) {
            case WAITING:
            case RUNNING:
            case SUSPENDED:
                return true;
            case SUCCESS:
                return handleSuccess(taskVO);
            case FAILED:
            case CANCELLED:
                return handleFailed(taskVO);
        }
        return false;
    }

    /**
     * 处理成功状态
     * @param taskVO
     * @return
     */
    private boolean handleSuccess(AsyncTaskRecordVO taskVO) {
        if (Objects.isNull(taskVO) || Objects.isNull(taskVO.getTaskId()) || Objects.isNull(taskVO.getExtInfo())) {
            return false;
        }

        taskVO.setGmtModified(new Date());
        taskVO.setTaskStatus(AsyncTaskStatusEnum.SUCCESS);
        int update = asyncTaskRecordService.update(taskVO);
        if (update <= 0) {
            return false;
        }

        LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(taskVO.getEntity());
        if (Objects.isNull(labelInfoDTO)) {
            return false;
        }

        String taskName = taskVO.getExtInfo().getTaskName();
        List<Record> recordList = odpsService.getAsyncExecuteResult(taskVO.getTaskId(), taskName);
        return upsertLabelDataStat(labelInfoDTO, recordList);
    }

    /**
     * 批量更新枚举值统计值
     * @param labelInfoDTO
     * @param recordList
     * @return
     */
    private boolean upsertLabelDataStat(LabelInfoDTO labelInfoDTO, List<Record> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return true;
        }

        String labelCode = labelInfoDTO.getCode();
        Long dimEnumMetaId = Objects.isNull(labelInfoDTO.getDimEnumMetaId()) ? 0 : labelInfoDTO.getDimEnumMetaId();
        String bizdate = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
        for (Record record : recordList) {
            String enumCode = record.getString("field");
            String cnt = record.getString("cnt");

            LabelEnumDataStatBO statBO = labelEnumDataStatService.queryByLabelEnumDataAndBizdate(labelCode, dimEnumMetaId, enumCode, bizdate);
            if (Objects.isNull(statBO)) {
                statBO = new LabelEnumDataStatBO();
                statBO.setLabelCode(labelCode);
                statBO.setDimEnumId(dimEnumMetaId);
                statBO.setEnumCode(enumCode);
                statBO.setCnt(Long.valueOf(cnt));
                statBO.setBizdate(bizdate);
                labelEnumDataStatService.insert(statBO);
            } else {
                statBO.setGmtModified(new Date());
                statBO.setCnt(Long.valueOf(cnt));
                labelEnumDataStatService.update(statBO);
            }
        }
        return true;
    }

    /**
     * 处理失败状态
     * @param taskVO
     * @return
     */
    private boolean handleFailed(AsyncTaskRecordVO taskVO) {
        if (Objects.isNull(taskVO)) {
            return false;
        }

        taskVO.setGmtModified(new Date());
        taskVO.setTaskStatus(AsyncTaskStatusEnum.FAILED);
        return asyncTaskRecordService.update(taskVO) > 0;
    }
}
