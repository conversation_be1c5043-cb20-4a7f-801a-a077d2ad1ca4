package com.fliggy.picasso.schedulerx.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.common.domain.score.ScoreInfoVO;
import com.fliggy.picasso.common.enums.score.ScoreEntityTypeEnum;
import com.fliggy.picasso.common.enums.score.ScoreTypeEnum;
import com.fliggy.picasso.service.score.ScoreInfoService;
import com.fliggy.pokemon.client.api.odps.OdpsTunnelDownloadService;
import com.fliggy.pokemon.client.callback.DataFlowCallback;
import com.fliggy.pokemon.client.odps.domain.OdpsTableInfo;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.fliggy.picasso.common.Constant.DTS_LOG;
import static com.fliggy.picasso.common.Constant.ODPS_PROJECT_TRIP_PROFILE;

@Component
public class LabelScoreInfoRefluxProcessor extends JavaProcessor {

    private static final Logger log = LoggerFactory.getLogger(DTS_LOG);

    private static final String TRIP_LABEL_QUALITY_SCORE_TABLE = "trip_label_quality_score";
    private static final int LABEL_SCORE_INFO_REFLEX_PAGE_SIZE = 200;

    @Resource
    private OdpsTunnelDownloadService odpsTunnelDownloadService;
    @Resource
    private ScoreInfoService scoreInfoService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            executeReflux();
            return new ProcessResult(true);
        } catch (Exception e) {
            String errMsg = "标签评分回流异常：";
            log.error("errMsg", e);
            return new ProcessResult(false, errMsg + e.getMessage());
        }
    }

    private void executeReflux() {
        OdpsTableInfo odpsTableInfo = new OdpsTableInfo();
        odpsTableInfo.setProject(ODPS_PROJECT_TRIP_PROFILE);
        odpsTableInfo.setTable(TRIP_LABEL_QUALITY_SCORE_TABLE);
        String ds = DateTime.now().minusDays(1).toString(OdpsConstants.ODPS_PHYSICAL_PROFILE_PARTITION_FORMAT);
        odpsTableInfo.setPartition("ds=" + ds);

        odpsTunnelDownloadService.streamDownload(odpsTableInfo, new DataFlowCallback() {
            @Override
            public void success(int code, String message, Object data) {

            }

            @Override
            public void fail(int code, String message, Object data) {
                log.error("odps tunnel download fail, code:{}, message:{}, data:{}", code, message, data);
            }

            @Override
            public void stream(List<Map<String, Object>> list) {
                for (Map<String, Object> map: list) {
                    String labelCode = map.get("code").toString();
                    Map<ScoreTypeEnum, BigDecimal> labelScoreMap = scoreInfoService.queryLabelScore(labelCode);

                    // 拆分需要新增或更新的标签
                    Map<ScoreTypeEnum, BigDecimal> needInsert = new HashMap<>();
                    Map<ScoreTypeEnum, BigDecimal> needUpdate = new HashMap<>();
                    splitNeedRefluxLabels(map, labelScoreMap, needInsert, needUpdate);

                    // 更新标签评分
                    upsertLabelScore(labelCode, needInsert, needUpdate);
                }
            }

            @Override
            public void total(long total) {

            }
        }, LABEL_SCORE_INFO_REFLEX_PAGE_SIZE);
    }

    /**
     * 更新标签评分
     * @param labelCode
     * @param needInsert
     * @param needUpdate
     */
    private void upsertLabelScore(String labelCode, Map<ScoreTypeEnum, BigDecimal> needInsert, Map<ScoreTypeEnum, BigDecimal> needUpdate) {
        needInsert.forEach((scoreType, score) -> {
            scoreInfoService.create(generateScoreInfoVO(labelCode, scoreType, score));
        });

        needUpdate.forEach((scoreType, score) -> {
            scoreInfoService.updateLabelScoreByScoreType(labelCode, scoreType, score);
        });
    }

    private ScoreInfoVO generateScoreInfoVO(String labelCode, ScoreTypeEnum scoreType, BigDecimal score) {
        ScoreInfoVO vo = new ScoreInfoVO();
        vo.setEntity(labelCode);
        vo.setEntityType(ScoreEntityTypeEnum.LABEL);
        vo.setScoreType(scoreType);
        vo.setScore(score);
        return vo;
    }

    /**
     * 拆分需要新增或更新的标签
     * @param map
     * @param labelScoreMap
     * @param needInsert
     * @param needUpdate
     */
    private void splitNeedRefluxLabels(Map<String, Object> map, Map<ScoreTypeEnum, BigDecimal> labelScoreMap,
                                       Map<ScoreTypeEnum, BigDecimal> needInsert, Map<ScoreTypeEnum, BigDecimal> needUpdate) {
        map.forEach((key, value) -> {
            ScoreTypeEnum scoreTypeEnum = getScoreTypeEnum(key);
            if (scoreTypeEnum == null) {
                return;
            }

            BigDecimal labelScore = labelScoreMap.get(scoreTypeEnum);
            BigDecimal needRefluxScore = new BigDecimal(value.toString());
            if (Objects.isNull(labelScore)) {
                // db中没有，需要新增
                needInsert.put(scoreTypeEnum, needRefluxScore);
            } else if (!Objects.equals(labelScore, needRefluxScore)){
                // db中有，但评分不同，需要更新
                needUpdate.put(scoreTypeEnum, needRefluxScore);
            }
        });
    }

    /**
     * 获取评分类型枚举
     * @param scoreType
     * @return
     */
    private ScoreTypeEnum getScoreTypeEnum(String scoreType) {
        if (StringUtils.isBlank(scoreType)) {
            return null;
        }

        switch (scoreType) {
            case "score":
                return ScoreTypeEnum.QUALITY_SCORE;
            case "health_score":
                return ScoreTypeEnum.HEALTH_SCORE;
            case "cover_score":
                return ScoreTypeEnum.COVER_SCORE;
            case "hot_score":
                return ScoreTypeEnum.HOT_SCORE;
            case "feedback_score":
                return ScoreTypeEnum.FEEDBACK_SCORE;
            default:
                return null;
        }
    }
}
