package com.fliggy.picasso.schedulerx.job.alarm;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoQuery;
import com.fliggy.picasso.common.enums.alarm.AlarmSceneTypeEnum;
import com.fliggy.picasso.common.enums.alarm.RoarAlarmTypeEnum;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.config.SwitchConfig;
import com.fliggy.picasso.entity.alarm.CrowdMetaInfoWithAlarmInfos;
import com.fliggy.picasso.entity.bo.CrowdAlarmInfoBO;
import com.fliggy.picasso.entity.bo.CrowdMonitorConfigBO;
import com.fliggy.picasso.service.alarm.CrowdAlarmFactory;
import com.fliggy.picasso.service.alarm.CrowdMonitorConfigService;
import com.fliggy.picasso.service.alarm.impl.AbstractCrowdAlarmServiceImpl;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class CrowdExpireAlarmProcessor extends JavaProcessor {

    @Resource
    private CrowdService crowdService;
    @Resource
    private CrowdMonitorConfigService crowdMonitorConfigService;
    @Resource
    private CrowdAlarmFactory crowdAlarmFactory;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        // 7日内过期人群
        CrowdMetaInfoQuery query = new CrowdMetaInfoQuery();
        if (SwitchConfig.COMMON_DEBUG_SWITCH) {
            query.setCreator(SwitchConfig.ALARM_DEBUG_CREATOR_NAME);
        }
        query.setDeleted((byte)0);
        query.setExpiredDateStart(new Date());
        query.setExpiredDateEnd(DateUtils.getBeforeAfterDate(DateUtils.formatDateToYYYYMMDDHHMMSS(new Date()), 7));
        List<CrowdMetaInfoDO> aboutExpireCrowdDOList = crowdService.listQuery(query, 1, 10000);
        if (CollectionUtils.isEmpty(aboutExpireCrowdDOList)) {
            return new ProcessResult(true);
        }

        // 过滤掉没有告警配置的人群
        List<CrowdMetaInfoWithAlarmInfos> validAboutExpireCrowdDOList = filterNonAlarmConfigCrowd(aboutExpireCrowdDOList);
        if (CollectionUtils.isEmpty(validAboutExpireCrowdDOList)) {
            return new ProcessResult(true);
        }

        // 按接收人和过期时间聚合
        Map<String, Map<RoarAlarmTypeEnum, Map<Integer, List<Long>>>> aboutExpireCrowdReceiverMap = getCrowdReceiverMap(validAboutExpireCrowdDOList);
        if (aboutExpireCrowdReceiverMap.isEmpty()) {
            return new ProcessResult(true);
        }

        // 发送告警
        sendCrowdExpireAlarm(aboutExpireCrowdReceiverMap);
        return new ProcessResult(true);
    }

    private List<CrowdMetaInfoWithAlarmInfos> filterNonAlarmConfigCrowd(List<CrowdMetaInfoDO> crowdDOList) {
        List<Long> crowdIds = crowdDOList.stream().map(CrowdMetaInfoDO::getId).collect(Collectors.toList());
        List<CrowdMonitorConfigBO> configBOList = crowdMonitorConfigService.queryByCrowdIdsAndSceneTypesWithDefault(crowdIds, AlarmSceneTypeEnum.CROWD_EXPIRE);
        if (CollectionUtils.isEmpty(configBOList)) {
            return Lists.newArrayList();
        }
        Map<Long, CrowdMonitorConfigBO> configBOMap = configBOList.stream().filter(config -> Objects.nonNull(config) && config.getIsValid())
            .collect(Collectors.toMap(CrowdMonitorConfigBO::getCrowdId, config -> config));
        if (configBOMap.isEmpty()) {
            return Lists.newArrayList();
        }

        List<CrowdMetaInfoWithAlarmInfos> result = new ArrayList<>();
        for (CrowdMetaInfoDO crowdMetaInfoDO : crowdDOList) {
            CrowdMonitorConfigBO config = configBOMap.get(crowdMetaInfoDO.getId());
            if (config == null || CollectionUtils.isEmpty(config.getAlarmTypes()) || CollectionUtils.isEmpty(config.getReceiverTypes())) {
                continue;
            }
            result.add(new CrowdMetaInfoWithAlarmInfos(crowdMetaInfoDO, config.getAlarmTypes()));
        }
        return result;
    }

    private Map<String, Map<RoarAlarmTypeEnum, Map<Integer, List<Long>>>> getCrowdReceiverMap(List<CrowdMetaInfoWithAlarmInfos> crowdWithAlarmList) {
        Map<String, Map<RoarAlarmTypeEnum, Map<Integer, List<Long>>>> result = new HashMap<>();
        for (CrowdMetaInfoWithAlarmInfos crowdWithAlarm : crowdWithAlarmList) {
            CrowdMetaInfoDO crowdMetaInfoDO = crowdWithAlarm.getCrowdMetaInfoDO();
            List<RoarAlarmTypeEnum> alarmTypes = crowdWithAlarm.getAlarmTypes();
            if (Objects.isNull(crowdMetaInfoDO) || CollectionUtils.isEmpty(alarmTypes)) {
                continue;
            }

            List<Employee> alarmReceivers = crowdMonitorConfigService.getAlarmReceivers(crowdMetaInfoDO, AlarmSceneTypeEnum.CROWD_EXPIRE);
            for (Employee receiver : alarmReceivers) {
                if (receiver == null || receiver.getEmpId() == null) {
                    continue;
                }
                if (Objects.isNull(crowdMetaInfoDO.getId()) || Objects.isNull(crowdMetaInfoDO.getExpiredDate())) {
                    continue;
                }
                int intervalDays = DateUtils.getIntervalDays(new Date(), crowdMetaInfoDO.getExpiredDate());

                for (RoarAlarmTypeEnum alarmType : alarmTypes) {
                    result.computeIfAbsent(receiver.getEmpId(), k -> new HashMap<>())
                        .computeIfAbsent(alarmType, k -> new HashMap<>())
                        .computeIfAbsent(intervalDays, k -> new ArrayList<>())
                        .add(crowdMetaInfoDO.getId());
                }
            }
        }
        return result;
    }

    private void sendCrowdExpireAlarm(Map<String, Map<RoarAlarmTypeEnum, Map<Integer, List<Long>>>> aboutExpireCrowdReceiverMap) {
        AbstractCrowdAlarmServiceImpl alarmService = crowdAlarmFactory.choose(AlarmSceneTypeEnum.CROWD_EXPIRE);
        if (Objects.isNull(alarmService)) {
            return;
        }
        for (Map.Entry<String, Map<RoarAlarmTypeEnum, Map<Integer, List<Long>>>> entry : aboutExpireCrowdReceiverMap.entrySet()) {
            String empId = entry.getKey();
            if (StringUtils.isEmpty(empId) || entry.getValue().isEmpty()) {
                continue;
            }
            for (Map.Entry<RoarAlarmTypeEnum, Map<Integer, List<Long>>> alarmTypeEntry : entry.getValue().entrySet()) {
                RoarAlarmTypeEnum alarmType = alarmTypeEntry.getKey();
                if (alarmType == null || alarmTypeEntry.getValue().isEmpty()) {
                    continue;
                }

                if (Objects.equals(alarmType, RoarAlarmTypeEnum.DING_TALK)) {
                    for (Map.Entry<Integer, List<Long>> intervalDaysEntry : alarmTypeEntry.getValue().entrySet()) {
                        Integer intervalDays = intervalDaysEntry.getKey();
                        List<Long> crowdIds = intervalDaysEntry.getValue();
                        if (Objects.isNull(intervalDays) || CollectionUtils.isEmpty(crowdIds)) {
                            continue;
                        }
                        CrowdAlarmInfoBO crowdAlarmInfoBO = new CrowdAlarmInfoBO();
                        crowdAlarmInfoBO.setSceneType(AlarmSceneTypeEnum.CROWD_EXPIRE);
                        crowdAlarmInfoBO.setAlarmType(alarmType);
                        crowdAlarmInfoBO.setCrowdIds(crowdIds);
                        crowdAlarmInfoBO.setReceiver(empId);
                        crowdAlarmInfoBO.setExpireInDays(intervalDays);
                        alarmService.sendAlarm(crowdAlarmInfoBO);
                    }
                } else {
                    List<Long> crowdIds = alarmTypeEntry.getValue().values().stream()
                        .flatMap(List::stream).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(crowdIds)) {
                        continue;
                    }
                    // 按100分片后分批发送告警
                    List<List<Long>> crowdIdBatches = Lists.partition(crowdIds, 100);
                    for (List<Long> crowdIdBatch : crowdIdBatches) {
                        CrowdAlarmInfoBO crowdAlarmInfoBO = new CrowdAlarmInfoBO();
                        crowdAlarmInfoBO.setSceneType(AlarmSceneTypeEnum.CROWD_EXPIRE);
                        crowdAlarmInfoBO.setAlarmType(alarmType);
                        crowdAlarmInfoBO.setCrowdIds(crowdIdBatch);
                        crowdAlarmInfoBO.setReceiver(empId);
                        alarmService.sendAlarm(crowdAlarmInfoBO);
                    }
                }
            }
        }
    }
}
