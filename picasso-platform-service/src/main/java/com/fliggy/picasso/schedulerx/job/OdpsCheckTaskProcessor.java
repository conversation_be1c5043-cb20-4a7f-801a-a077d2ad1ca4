package com.fliggy.picasso.schedulerx.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.odps.Instance;
import com.aliyun.odps.OdpsException;
import com.fliggy.picasso.common.domain.crowd.record.AsyncTaskRecordVO;
import com.fliggy.picasso.common.enums.AsyncTaskRecordTypeEnum;
import com.fliggy.picasso.common.enums.AsyncTaskStatusEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.common.utils.NumberUtils;
import com.fliggy.picasso.offline.OdpsService;
import com.fliggy.picasso.service.record.AsyncTaskRecordService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.fliggy.picasso.common.Constant.DTS_LOG;
import static com.fliggy.picasso.common.constants.OdpsConstants.ODPS_EXECUTE_PROJECT;

@Component
public class OdpsCheckTaskProcessor extends MapJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DTS_LOG);

    @Resource
    private AsyncTaskRecordService asyncTaskRecordService;
    @Resource
    private OdpsService odpsService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String level1Dispatch = "Level1Dispatch";
        String taskName = context.getTaskName();
        Object task = context.getTask();
        try {
            if (isRootTask(context)) {
                List<AsyncTaskRecordVO> asyncTaskRecordVOList = null;
                String param = StringUtils.isNotEmpty(context.getInstanceParameters()) ? context.getInstanceParameters() : context.getJobParameters();
                if (StringUtils.isNotBlank(param)) {
                    asyncTaskRecordVOList = asyncTaskRecordService.queryByIds(NumberUtils.split(param));
                } else {
                    List<AsyncTaskStatusEnum> statusList = Arrays.asList(AsyncTaskStatusEnum.INIT, AsyncTaskStatusEnum.RUNNING);
                    asyncTaskRecordVOList = asyncTaskRecordService.queryByTypeAndStatusList(AsyncTaskRecordTypeEnum.GUID, statusList);
                }
                
                if (CollectionUtils.isNullOrEmpty(asyncTaskRecordVOList)) {
                    return new ProcessResult(true);
                }
                return map(asyncTaskRecordVOList, level1Dispatch);
            } else if (level1Dispatch.equals(taskName)) {
                AsyncTaskRecordVO asyncTaskRecordVO = (AsyncTaskRecordVO) task;
                if (Objects.equals(asyncTaskRecordVO.getTaskStatus(), AsyncTaskStatusEnum.INIT)) {
                    Boolean startTask = startOdpsCheckTask(asyncTaskRecordVO);
                    return new ProcessResult(startTask);
                }

                if (Objects.equals(asyncTaskRecordVO.getTaskStatus(), AsyncTaskStatusEnum.RUNNING)) {
                    Boolean statusTask = statusOdpsCheckTask(asyncTaskRecordVO);
                    return new ProcessResult(statusTask);
                }
                return new ProcessResult(true);
            } else {
                return new ProcessResult(false);
            }
        } catch (Exception e) {
            log.error("OdpsCheckTaskProcessor process error", e);
            return new ProcessResult(false, e.getMessage());
        }
    }

    /**
     * 启动odps任务
     * @param asyncTaskRecordVO
     * @return
     * @throws OdpsException
     */
    private Boolean startOdpsCheckTask(AsyncTaskRecordVO asyncTaskRecordVO) throws OdpsException {
        if (Objects.isNull(asyncTaskRecordVO) || Objects.isNull(asyncTaskRecordVO.getId())
                || Objects.isNull(asyncTaskRecordVO.getEntity())) {
            return false;
        }

        // start task
        Instance instance = null;
        try {
            String sql = generateOdpsCheckSql(asyncTaskRecordVO.getEntity());
            String taskName = odpsService.getTaskName("odpsCheckTask_");
            instance = odpsService.startOdpsTask(ODPS_EXECUTE_PROJECT, sql, taskName, null);
        } catch (Exception e) {
            log.error("startOdpsCheckTask error", e);
            AsyncTaskRecordVO updateVO = new AsyncTaskRecordVO();
            updateVO.setId(asyncTaskRecordVO.getId());
            updateVO.setTaskStatus(AsyncTaskStatusEnum.FAILED);
            updateVO.setErrMsg(e.getMessage());
            int update = asyncTaskRecordService.update(updateVO);
            return update > 0;
        }
        if (Objects.isNull(instance) || Objects.isNull(instance.getId())) {
            return false;
        }

        // update status
        AsyncTaskRecordVO updateVO = new AsyncTaskRecordVO();
        updateVO.setId(asyncTaskRecordVO.getId());
        updateVO.setTaskId(instance.getId());
        updateVO.setTaskStatus(AsyncTaskStatusEnum.RUNNING);
        updateVO.setErrMsg(AsyncTaskStatusEnum.RUNNING.getDesc());
        int update = asyncTaskRecordService.update(updateVO);
        return update > 0;
    }

    /**
     * 获取odps任务状态
     * @param asyncTaskRecordVO
     * @return
     */
    private Boolean statusOdpsCheckTask(AsyncTaskRecordVO asyncTaskRecordVO) {
        if (Objects.isNull(asyncTaskRecordVO) || Objects.isNull(asyncTaskRecordVO.getTaskId())) {
            return false;
        }

        // get task status
        Instance.TaskStatus.Status status = odpsService.checkInstanceStatus(asyncTaskRecordVO.getTaskId());
        if (Objects.isNull(status)) {
            return false;
        }

        // update status
        AsyncTaskRecordVO updateVO = new AsyncTaskRecordVO();
        updateVO.setId(asyncTaskRecordVO.getId());
        switch (status) {
            case SUCCESS:
                updateVO.setTaskStatus(AsyncTaskStatusEnum.SUCCESS);
                updateVO.setErrMsg("已授权");
                break;
            case FAILED:
                updateVO.setTaskStatus(AsyncTaskStatusEnum.FAILED);
                updateVO.setErrMsg(getErrMsg(asyncTaskRecordVO.getTaskId()));
                break;
            case CANCELLED:
                updateVO.setTaskStatus(AsyncTaskStatusEnum.FAILED);
                updateVO.setErrMsg("任务被取消，请联系诸葛答疑");
                break;
        }
        return asyncTaskRecordService.update(updateVO) > 0;
    }

    private String getErrMsg(String taskId) {
        String errMsg = odpsService.getInstanceFirstResultText(null, taskId);
        if (errMsg.toLowerCase().contains("authorization")) {
            return "未完成授权";
        }
        // 截取前250字符，避免字段过大
        return errMsg.length() > 251 ? errMsg.substring(0, 251) : errMsg;
    }

    private String generateOdpsCheckSql(String guid) {
        if (!guid.startsWith("odps.")) {
            throw new ParamErrorException("odps表名不规范");
        }

        String tableName = guid.substring(5);
        String format = "select * from %s limit 1;";
        return String.format(format, tableName);
    }
}
