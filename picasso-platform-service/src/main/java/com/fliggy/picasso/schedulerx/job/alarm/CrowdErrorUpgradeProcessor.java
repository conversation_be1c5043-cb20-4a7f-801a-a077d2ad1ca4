package com.fliggy.picasso.schedulerx.job.alarm;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.common.enums.alarm.AlarmSceneTypeEnum;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.service.alarm.CrowdAlarmRecordService;
import com.fliggy.picasso.service.crowd.CrowdCircleService;
import com.taobao.ateye.annotation.AteyeInvoker;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DTS_LOG;

/**
 * 人群连续7天异常，升级处理成不更新人群
 */
@Component
public class CrowdErrorUpgradeProcessor extends MapJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DTS_LOG);

    @Resource
    private CrowdAlarmRecordService crowdAlarmRecordService;
    @Resource
    private CrowdCircleService crowdCircleService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String level1Dispatch = "Level1Dispatch";
        String taskName = context.getTaskName();
        Object task = context.getTask();
        try {
            if (isRootTask(context)) {
                List<Long> crowdIdList = null;
                String param = StringUtils.isNotEmpty(context.getInstanceParameters()) ?
                    context.getInstanceParameters() : context.getJobParameters();
                if (StringUtils.isNotBlank(param)) {
                    crowdIdList = Arrays.stream(param.split(",")).map(Long::parseLong).collect(Collectors.toList());
                } else {
                    crowdIdList = queryErrorIn7DaysCrowds();
                }

                if (CollectionUtils.isEmpty(crowdIdList)) {
                    return new ProcessResult(true);
                }
                return map(crowdIdList, level1Dispatch);
            } else if (level1Dispatch.equals(taskName)) {
                Long crowdId = (Long) task;
                return new ProcessResult(crowdCircleService.updateUpdateType(crowdId, false));
            } else {
                return new ProcessResult(false);
            }
        } catch (Exception e) {
            log.error("CrowdErrorUpgradeProcessor process error", e);
            return new ProcessResult(false, e.getMessage());
        }
    }

    @AteyeInvoker(description = "queryErrorIn7DaysCrowds")
    public List<Long> queryErrorIn7DaysCrowds() {
        String dateStr = DateUtils.formatDateToStr(new Date(), DateUtils.YMD_FORMAT);

        Set<Long> result = null;
        for (int i = 0; i < 7; i++) {
            Date startDate = DateUtils.getBeforeAfterDate(dateStr, -i - 1);
            Date endDate = DateUtils.getBeforeAfterDate(dateStr, -i);

            // 查询第i天构建失败的crowdIds
            List<Long> crowdIds = crowdAlarmRecordService.queryAllCrowdBySceneTypeInSpecificDays(
                AlarmSceneTypeEnum.CROWD_BUILD_ERROR, startDate, endDate);

            // 如果是第一天，初始化结果集；否则取交集
            if (result == null) {
                result = new HashSet<>(crowdIds);
            } else {
                result.retainAll(crowdIds);
            }

            // 如果结果集为空，提前退出循环
            if (result.isEmpty()) {
                break;
            }
        }
        return new ArrayList<>(result);
    }

    @AteyeInvoker(description = "queryErrorIn3DaysCrowds")
    public List<Long> queryErrorIn3DaysCrowds() {
        String dateStr = DateUtils.formatDateToStr(new Date(), DateUtils.YMD_FORMAT);
        Date today = DateUtils.getBeforeAfterDate(dateStr, 0);
        Date yesterday = DateUtils.getBeforeAfterDate(dateStr, -1);
        Date beforeYesterday = DateUtils.getBeforeAfterDate(dateStr, -2);
        List<Long> todayCrowdIds = crowdAlarmRecordService.queryAllCrowdBySceneTypeInSpecificDays(
            AlarmSceneTypeEnum.CROWD_BUILD_ERROR, today, new Date());
        List<Long> yesterdayCrowdIds = crowdAlarmRecordService.queryAllCrowdBySceneTypeInSpecificDays(
            AlarmSceneTypeEnum.CROWD_BUILD_ERROR, yesterday, today);
        List<Long> beforeYesterdayCrowdIds = crowdAlarmRecordService.queryAllCrowdBySceneTypeInSpecificDays(
            AlarmSceneTypeEnum.CROWD_BUILD_ERROR, beforeYesterday, yesterday);

        List<Long> result = new ArrayList<>(todayCrowdIds);
        result.retainAll(yesterdayCrowdIds);
        result.retainAll(beforeYesterdayCrowdIds);
        return result;
    }
}
