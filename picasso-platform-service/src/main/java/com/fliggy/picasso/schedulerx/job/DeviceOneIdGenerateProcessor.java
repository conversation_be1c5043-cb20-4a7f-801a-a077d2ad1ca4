package com.fliggy.picasso.schedulerx.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.common.enums.oneid.DeviceOneIdOpEnum;
import com.fliggy.picasso.entity.vo.DeviceOneIdMappingVO;
import com.fliggy.picasso.service.lindorm.OneIdMappingService;
import com.fliggy.picasso.service.lindorm.column.OneIdTypeEnum;
import com.fliggy.picasso.service.lindorm.domain.OneIdMappingDO;
import com.fliggy.picasso.service.sequence.SequenceService;
import com.fliggy.pokemon.client.api.odps.OdpsTunnelDownloadService;
import com.fliggy.pokemon.client.callback.DataFlowCallback;
import com.fliggy.pokemon.client.odps.domain.OdpsTableInfo;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.util.Ateye;
import com.taobao.tddl.client.sequence.Sequence;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.fliggy.picasso.common.Constant.ODPS_PROJECT_TRIP_PROFILE;
import static com.fliggy.picasso.common.Constant.ONE_ID_LOG;
import static com.fliggy.picasso.service.lindorm.column.OneIdKeyTypeEnum.DEVICE_ONE_ID;

@Slf4j
@Component
public class DeviceOneIdGenerateProcessor extends JavaProcessor {

    private static final Logger ttLog = LoggerFactory.getLogger(ONE_ID_LOG);

    private static final String DEVICE_INFO_DIFF_ODPS_TABLE = "trip_device_info_with_op_diff";

    @Resource
    private Sequence deviceOneIdSequence;
    @Resource
    private OdpsTunnelDownloadService odpsTunnelDownloadService;
    @Resource
    private OneIdMappingService deviceOneIdMappingServiceImpl;
    @Resource
    private SequenceService sequenceService;

    @Switch(description = "设备信息表id列表")
    private String deviceInfoIdList = "device_one_id,umid,user_id,utdid,imei,oaid,idfa,caid,caid2";
    @Switch(description = "设备增量表download page size")
    public int deviceInfoDiffDownloadPageSize = 500;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            String param = Objects.isNull(context.getInstanceParameters()) ? context.getJobParameters() : context.getInstanceParameters();
            DeviceOneIdOpEnum opTypeParam = DeviceOneIdOpEnum.fromCode(param);
            if (Objects.nonNull(opTypeParam)) {
                return new ProcessResult(executeProcess(opTypeParam, null));
            }else {
                int successCnt = 0;
                for (DeviceOneIdOpEnum opType: DeviceOneIdOpEnum.values()) {
                    if (!executeProcess(opType, null)) {
                        log.error("DeviceOneIdGenerateProcessor failed, opType: " + opType.getCode());
                    }
                    successCnt += 1;
                }
                return new ProcessResult(successCnt == DeviceOneIdOpEnum.values().length);
            }
        }catch (Exception e) {
            log.error("DeviceOneIdGenerateProcessor failed " + e);
            return new ProcessResult(false, "DeviceOneIdGenerateProcessor failed " + e.getMessage());
        }
    }

    private boolean executeProcess(DeviceOneIdOpEnum opType, Integer bucketNum) {
        OdpsTableInfo odpsTableInfo = new OdpsTableInfo();
        odpsTableInfo.setProject(ODPS_PROJECT_TRIP_PROFILE);
        odpsTableInfo.setTable(DEVICE_INFO_DIFF_ODPS_TABLE);
        String partition = Objects.nonNull(bucketNum) ? "bucket_name=" + bucketNum : generateTablePartitions(opType);
        odpsTableInfo.setPartition(partition);

        odpsTunnelDownloadService.streamDownload(odpsTableInfo, new DataFlowCallback() {
            @Override
            public void success(int code, String message, Object data) {

            }

            @Override
            public void fail(int code, String message, Object data) {
                log.error("DeviceOneIdGenerateProcessor failed. msg:" + message);
            }

            @Override
            public void stream(List<Map<String, Object>> list) {
                for (Map<String, Object> map : list) {
                    List<DeviceOneIdMappingVO> voList = generateDeviceOneIdMappingVOList(map);
                    List<OneIdMappingDO> records = voList.stream().map(DeviceOneIdGenerateProcessor.this::convertToOneIdMappingDO).collect(Collectors.toList());

                    // lindorm upsert/delete
                    if (DeviceOneIdOpEnum.DEL == opType) {
                        deviceOneIdMappingServiceImpl.batchDelete(records);
                    } else {
                        deviceOneIdMappingServiceImpl.batchUpsert(records);
                    }
                    // tt log
                    records.forEach(record -> ttLog.info("DeviceOneIdGenerateProcessor success. opType:{}, record:{}", opType.getCode(), JSON.toJSONString(record)));
                }
            }

            @Override
            public void total(long total) {

            }
        }, deviceInfoDiffDownloadPageSize);
        return true;
    }

    private List<DeviceOneIdMappingVO> generateDeviceOneIdMappingVOList(Map<String, Object> map) {
        Long oneId;
        if (StringUtils.isNotEmpty(map.get(DEVICE_ONE_ID.getCode()).toString())) {
            oneId = Long.valueOf(map.get(DEVICE_ONE_ID.getCode()).toString());
        } else {
            // sequence generate
            oneId = deviceOneIdSequence.nextValue();
        }

        List<DeviceOneIdMappingVO> voList = new ArrayList<>();
        List<String> idNameList = new ArrayList<>(Arrays.asList(deviceInfoIdList.split(",")));
        idNameList.forEach((idName) -> {
            if (!map.containsKey(idName) || Objects.isNull(map.get(idName))) {
                return;
            }

            String keyValue = map.get(idName).toString();
            if (!Objects.equals(keyValue, "unchanged")) {
                voList.add(generateDeviceOneIdMappingVO(String.valueOf(oneId), 1, idName, keyValue));
            }
        });
        return voList;
    }

    private DeviceOneIdMappingVO generateDeviceOneIdMappingVO(String oneId, Integer index, String keyType, String keyValue) {
        return new DeviceOneIdMappingVO.Builder()
                .oneId(oneId)
                .index(index)
                .keyType(keyType)
                .keyValue(keyValue)
                .build();
    }

    private OneIdMappingDO convertToOneIdMappingDO(DeviceOneIdMappingVO deviceOneIdMappingVO) {
        return new OneIdMappingDO(OneIdTypeEnum.DEVICE,
                deviceOneIdMappingVO.getOneId(),
                deviceOneIdMappingVO.getIndex(),
                deviceOneIdMappingVO.getKeyType(),
                deviceOneIdMappingVO.getKeyValue());
    }

    private String generateTablePartitions(DeviceOneIdOpEnum opType) {
        String format = "ds='%s',op_type='%s'";
        String ds = DateTime.now().minusDays(1).toString(OdpsConstants.ODPS_PHYSICAL_PROFILE_PARTITION_FORMAT);
        return String.format(format, ds, opType.getCode());
    }

    @AteyeInvoker(description = "重置sequence值", paraDesc = "id&value")
    public boolean resetSequenceValue(Long id, Long value) {
        int update = sequenceService.resetSequenceValue(id, value);
        return update == 1;
    }

    @AteyeInvoker(description = "手动执行，更新lindorm", paraDesc = "op&bucketName")
    public boolean manualExecuteProcess(String op, Integer bucketNum) {
        DeviceOneIdOpEnum opType = DeviceOneIdOpEnum.fromCode(op);
        try {
            return executeProcess(opType, bucketNum);
        } catch (Exception e) {
            Ateye.out.println("manualExecuteProcess failed, opType: " + op + ", msg: " + e.getMessage());
            return false;
        }
    }

}
