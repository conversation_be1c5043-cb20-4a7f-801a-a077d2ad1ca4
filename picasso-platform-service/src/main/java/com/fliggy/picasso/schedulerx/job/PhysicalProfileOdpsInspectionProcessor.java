package com.fliggy.picasso.schedulerx.job;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyuncs.dataworks.model.v20171212.ListMetaTablePartitionResponse;
import com.aliyuncs.dataworks.model.v20171212.SearchMetaTablesResponse;
import com.fliggy.picasso.common.odps.OdpsGuid;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.msg.MessageProducer;
import com.fliggy.picasso.offline.OdpsMetaService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.csp.hotsensor.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;
import static com.fliggy.picasso.common.Constant.ODPS_INFO_LOG;

/**
 * 画像odps表巡检
 */
@Component
public class PhysicalProfileOdpsInspectionProcessor extends MapJobProcessor {

    private static final Logger DISPATCH_LOGGER = LoggerFactory.getLogger(DISPATCH_LOG);
    private static final Logger ODPS_INFO_LOGGER = LoggerFactory.getLogger(ODPS_INFO_LOG);
    // |标签|源表|负责人|分区总数|最大分区|最大分区记录数|
    private static final String LOG_FORMAT = "|%s|%s|%s|%s|%s|%s|";

    private final String LEVEL1_DISPATCH = "profileOdpsInspectionDispatchTask";
    private final String MSG_TAG = "profile_odps_notify";

    @Autowired
    private LabelInfoService labelInfoService;
    @Autowired
    private OdpsMetaService odpsMetaService;
    @Autowired
    private MessageProducer messageProducer;

    public ProcessResult process(JobContext context) {
        String taskName = context.getTaskName();
        Object task = context.getTask();

        try {
            if (isRootTask(context)) {
                List<LabelInfoDTO> labelInfoDTOS = labelInfoService.listOdpsSourceOnUse();
                return map(labelInfoDTOS, LEVEL1_DISPATCH);
            } else if (LEVEL1_DISPATCH.equals(taskName)) {
                LabelInfoDTO labelInfoDTO = (LabelInfoDTO) task;
                JSONObject sourceConfig = labelInfoDTO.getSourceConfig();
                if (null == sourceConfig || StringUtils.isEmpty(sourceConfig.getString("table"))) {
                    return new ProcessResult(true, String.format("[画像odps巡检]:%s标签源表为空", labelInfoDTO.getCode()));
                }

                OdpsGuid odpsGuid = new OdpsGuid(sourceConfig.getString("project"), sourceConfig.getString("table"));
                SearchMetaTablesResponse.DataItem tableInfo = odpsMetaService.getTableInfo(odpsGuid);
                ListMetaTablePartitionResponse partitionInfo = odpsMetaService.getPartitionInfo(odpsGuid);
                ListMetaTablePartitionResponse.Partition lastPartition = partitionInfo.getPartitionList().get(0);
                ODPS_INFO_LOGGER.info(String.format(LOG_FORMAT, labelInfoDTO.getCode(), odpsGuid.getFullGuid(), tableInfo.getOwnerId(),
                        partitionInfo.getTotalNum(), lastPartition.getPartitionName(), lastPartition.getRecords()));

                // 发送metaq消息
                sendMsg(labelInfoDTO.getCode(), tableInfo, partitionInfo);
            }
        } catch (Exception ex) {
            String errMsg = "[画像odps巡检]:异常";
            DISPATCH_LOGGER.error(errMsg + ex);
            return new ProcessResult(true, errMsg + ex.getMessage());
        }

        return new ProcessResult(true);
    }

    private void sendMsg(String labelCode, SearchMetaTablesResponse.DataItem tableInfo, ListMetaTablePartitionResponse partitionInfo) {
        JSONObject msg = convert2MsgJsonObj(labelCode, tableInfo, partitionInfo);
        messageProducer.send(msg.toJSONString(), MSG_TAG);
    }

    private JSONObject convert2MsgJsonObj(String labelCode, SearchMetaTablesResponse.DataItem tableInfo, ListMetaTablePartitionResponse partitionInfo) {
        String maxPtName = "";
        Long maxPtRecorts = 0L;
        List<ListMetaTablePartitionResponse.Partition> partitionList = partitionInfo.getPartitionList();
        if (partitionList.size() > 0) {
            partitionList.sort((o1, o2) -> o2.getPartitionName().compareTo(o1.getPartitionName()));
            ListMetaTablePartitionResponse.Partition lastPartition = partitionList.get(0);
            maxPtName = lastPartition.getPartitionName();
            maxPtRecorts = lastPartition.getRecords();
        }

        JSONObject msg = new JSONObject();
        msg.put("labelCode", labelCode);
        msg.put("odpsGuid", tableInfo.getTableGuid());
        msg.put("ownerId", tableInfo.getOwnerId());
        msg.put("ptCnts", partitionInfo.getTotalNum());
        msg.put("maxPtName", maxPtName);
        msg.put("maxPtRecords", maxPtRecorts);
        return msg;
    }

    @AteyeInvoker(description = "手动获取标签分区信息", paraDesc = "code")
    public void queryLabelPartitionsAteye(String code) {
        LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(code);
        JSONObject sourceConfig = labelInfoDTO.getSourceConfig();

        OdpsGuid odpsGuid = new OdpsGuid(sourceConfig.getString("project"), sourceConfig.getString("table"));
        SearchMetaTablesResponse.DataItem tableInfo = odpsMetaService.getTableInfo(odpsGuid);
        ListMetaTablePartitionResponse partitionInfo = odpsMetaService.getPartitionInfo(odpsGuid);
        JSONObject jsonObject = convert2MsgJsonObj(code, tableInfo, partitionInfo);
        Ateye.out.println(jsonObject.toJSONString());
    }

    @AteyeInvoker(description = "手动获取所有标签code")
    public void testGetAllLabelCodes() {
        List<LabelInfoDTO> labelInfoDTOS = labelInfoService.listOdpsSourceOnUse();
        Ateye.out.println("cnt: " + labelInfoDTOS.size());

        List<String> labelCodes = labelInfoDTOS.stream().map(LabelInfoDTO::getCode).collect(Collectors.toList());
        Ateye.out.println(JSON.toJSONString(labelCodes));
    }
}
