package com.fliggy.picasso.schedulerx.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.crowd.service.domain.profile.LabelQueryDTO;
import com.fliggy.crowd.service.domain.profile.ProfileQueryRequest;
import com.fliggy.crowd.service.domain.profile.ProfileValue;
import com.fliggy.crowd.service.open.api.PicassoCommonService;
import com.fliggy.crowd.service.result.TripCrowdCommonResult;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.constants.label.LabelValueSourceEnum;
import com.fliggy.picasso.common.enums.ProfileCodeEnum;
import com.fliggy.picasso.common.enums.group.ProfilePrefixEnum;
import com.fliggy.picasso.common.enums.label.LabelSourceEnum;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;
import static com.fliggy.picasso.common.Constant.LABEL_INSPECTION_LOG;

/**
 * 实时标签调用巡检
 * 1、不支持设备画像，设备画像多入参无法确定，不好查
 * 2、不支持星辰画像，星辰画像很多标签值格式不一样，例如：item_exclude_title 查的字段为title，模糊匹配后去除；noTravelDate 是复合标签；playOnly 有默认值等等
 * 太多特殊标签，需要单独一期盘一下
 */
@Component
public class RtLabelInspectionProcessor extends MapJobProcessor {

    private static final Logger DISPATCH_LOGGER = LoggerFactory.getLogger(DISPATCH_LOG);
    private static final Logger LABEL_INSPECTION_LOGGER = LoggerFactory.getLogger(LABEL_INSPECTION_LOG);

    private final String LEVEL1_DISPATCH = "rtLabelInspectionDispatchTask";
    // ｜rtLabel｜labelCode｜source｜result｜rt｜
    private final String LOG_FORMAT = "|rtLabel|%s|%s|%s|%s|";

    @Autowired
    private LabelInfoService labelInfoService;
    @Resource
    private PicassoCommonService picassoCommonService;

    @Switch(description = "rt标签巡检-诸葛id")
    public String rtLabelInspectionZhugeId = "2350588476";
    @Switch(description = "rt标签巡检-星辰id")
    public String rtLabelInspectionGalaxyId = "726126356756";
    @Switch(description = "rt标签巡检-不支持的profileCode")
    public String notSupportedProfileCode = "device,galaxy";

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String taskName = context.getTaskName();
        Object task = context.getTask();

        try {
            if (isRootTask(context)) {
                List<Byte> rtLabelSources = Arrays.asList(LabelSourceEnum.LINDORM.getCode(), LabelSourceEnum.HSF.getCode()
                        , LabelSourceEnum.API.getCode(), LabelSourceEnum.HOLO.getCode());
                List<LabelInfoDTO> labelInfoDTOS = labelInfoService.listLabelOnUseBySource(rtLabelSources);
                return map(labelInfoDTOS, LEVEL1_DISPATCH);
            } else if (LEVEL1_DISPATCH.equals(taskName)) {
                LabelInfoDTO labelInfoDTO = (LabelInfoDTO) task;
                if (Objects.isNull(labelInfoDTO)) {
                    return new ProcessResult(true);
                }

                // 过滤逻辑，有的标签不支持标签值查询
                if (needFilter(labelInfoDTO)) {
                    return new ProcessResult(true);
                }

                // 查询标签值，并记录rt
                long startTime = System.currentTimeMillis();
                boolean result = queryLabelValue(labelInfoDTO.getCode(), labelInfoDTO.getProfileCode(), labelInfoDTO.getPhysicalProfileCode());
                long processTime = System.currentTimeMillis() - startTime;
                LABEL_INSPECTION_LOGGER.info(String.format(LOG_FORMAT, labelInfoDTO.getCode(), labelInfoDTO.getSource(), result, processTime));
            }
        } catch (Exception ex) {
            String errMsg = "[实时标签巡检]:异常";
            DISPATCH_LOGGER.error(errMsg + ex);
            return new ProcessResult(true, errMsg + ex.getMessage());
        }
        return new ProcessResult(true);
    }

    private boolean needFilter(LabelInfoDTO labelInfoDTO) {
        // 过滤不支持的画像类型
        List<String> notSupportedProfileCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(notSupportedProfileCode)) {
            notSupportedProfileCodeList = new ArrayList<>(Arrays.asList(notSupportedProfileCode.split(",")));
        }
        if (Objects.nonNull(labelInfoDTO.getProfileCode()) && notSupportedProfileCodeList.contains(labelInfoDTO.getProfileCode())) {
            return true;
        }

        // 过滤需要运营配置传参的标签，目前就hsf标签需要过滤
        if (Objects.nonNull(labelInfoDTO.getSource()) && Objects.equals(labelInfoDTO.getSource(), LabelSourceEnum.HSF)
                && Objects.nonNull(labelInfoDTO.getSourceConfig())) {
            String sourceConfig = JSON.toJSONString(labelInfoDTO.getSourceConfig());
            if (sourceConfig.contains(LabelValueSourceEnum.MANUAL_CONFIG.getCode()) || sourceConfig.contains(LabelValueSourceEnum.EXT_INFO.getCode())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查询标签值，查询成功返回true，查询失败返回false
     */
    private boolean queryLabelValue(String labelCode, String profileCode, String physicalProfileCode) {
        if (StringUtils.isBlank(labelCode) || StringUtils.isBlank(profileCode) || StringUtils.isBlank(physicalProfileCode)) {
            return false;
        }

        ProfileQueryRequest request = new ProfileQueryRequest();
        request.setEntityId(getEntityId(profileCode, physicalProfileCode));
        LabelQueryDTO labelQueryDTO = new LabelQueryDTO();
        labelQueryDTO.setLabelName(labelCode);
        request.setLabelQueryDTOS(Collections.singletonList(labelQueryDTO));
        TripCrowdCommonResult<Map<String, ProfileValue>> result = picassoCommonService.queryProfiles(request);
        if (Objects.isNull(result)) {
            DISPATCH_LOGGER.error("[实时标签巡检]查询标签值失败，labelCode:{}, result is null", labelCode);
            return false;
        }

        if (!result.isSuccess()) {
            DISPATCH_LOGGER.error("[实时标签巡检]查询标签值失败，labelCode:{}, result:{}", labelCode, JSON.toJSONString(result));
            return false;
        }
        return true;
    }

    /**
     * 获取entityId
     */
    private String getEntityId(String profileCode, String physicalProfileCode) {
        if (Objects.equals(ProfileCodeEnum.USER.getCode(), profileCode) || Objects.equals(ProfileCodeEnum.SCRM_USER.getCode(), profileCode)) {
            return rtLabelInspectionZhugeId;
        }

        // 暂时不支持星辰，代码先保留，之后可能会用到
        if (Objects.equals(ProfileCodeEnum.GALAXY.getCode(), profileCode)) {
            String prefix = ProfilePrefixEnum.getPrefixByCode(physicalProfileCode);
            if (StringUtils.isBlank(prefix)) {
                throw new RuntimeException("不支持的physicalProfileCode：" + physicalProfileCode);
            }
            return prefix +  rtLabelInspectionGalaxyId;
        }
        throw new RuntimeException("不支持的profileCode：" + profileCode);
    }
}
