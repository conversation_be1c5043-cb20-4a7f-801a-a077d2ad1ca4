package com.fliggy.picasso.schedulerx.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.fliggy.picasso.action.ExecuteActionParam;
import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.common.constants.label.LabelEnumDataConstants;
import com.fliggy.picasso.common.constants.label.LabelEnumValueSourceTypeEnum;
import com.fliggy.picasso.common.domain.label.LabelOdpsSourceConfig;
import com.fliggy.picasso.common.domain.label.enumvalue.EnumDimMetaInfoParameter;
import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.common.odps.OdpsGuid;
import com.fliggy.picasso.common.odps.domain.OdpsRegisterProfileTableDTO;
import com.fliggy.picasso.entity.enumvalue.LabelEnumDataRefluxTask;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.service.enumvalue.EnumDimMetaInfoDTO;
import com.fliggy.picasso.service.enumvalue.EnumDimMetaInfoService;
import com.fliggy.picasso.service.label.LabelInfoParameter;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.odps.OdpsRegisterProfileTableService;
import com.fliggy.picasso.tair.TairRDBLock;
import com.fliggy.picasso.tair.TairRDBLockManager;
import com.fliggy.picasso.workflow.LabelEnumDataWorkflow;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeasy.flows.work.WorkReport;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.fliggy.picasso.common.constants.TairConstants.EXPIRE_TIME_IN_SEC;
import static com.fliggy.picasso.common.constants.TairConstants.LABEL_ENUM_LOCK_PREFIX;

/**
 * <AUTHOR>
 * @date 2021/2/20 下午3:58
 */
@Component
public class LabelEnumDataRefluxProcessor extends MapJobProcessor {

    private static final Logger LOG = LoggerFactory.getLogger("dispatch");

    private static final String TASK_NAME = "labelEnumDataReflux";

    @Autowired
    private LabelInfoService labelInfoService;

    @Autowired
    private EnumDimMetaInfoService enumDimMetaInfoService;

    @Autowired
    private OdpsRegisterProfileTableService odpsRegisterProfileTableService;

    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        this.executorService = new ThreadPoolExecutor(0, Integer.MAX_VALUE,
                60L, TimeUnit.SECONDS,
                new SynchronousQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("LabelEnumDataReflux-Scheduler-pool-").build()
        );
    }

    @Override
    public ProcessResult process(JobContext context) throws Exception {

        String taskName = context.getTaskName();
        Object task = context.getTask();

        if (isRootTask(context)) {
            String jobParameters = context.getJobParameters();
            List<LabelEnumDataRefluxTask> tasks;
            if (StringUtils.isNotBlank(jobParameters)) {
                Set<Long> enumMetaIds = Arrays.stream(jobParameters.split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toSet());

                tasks = queryLabelEnumDataRefluxTasks(enumMetaIds);
            } else {
                tasks = queryLabelEnumDataRefluxTasks(new HashSet<>());
            }

            LOG.info("[LabelEnumData Reflux] Tasks: {}", tasks);
            return CollectionUtils.isEmpty(tasks) ? new ProcessResult(true) : map(tasks, TASK_NAME);
        } else if (TASK_NAME.equals(taskName)) {
            // 子任务，执行分发的标签枚举值待回流任务
            LabelEnumDataRefluxTask labelEnumDataRefluxTask = (LabelEnumDataRefluxTask) task;
            LOG.info("[LabelEnumData Reflux] Execute Task: {}", labelEnumDataRefluxTask);
            try {
                Future<WorkReport> future = executorService.submit(new LabelEnumDataWorkflowJob(labelEnumDataRefluxTask));
                future.get();
            } catch (Exception e) {
                String msg = String.format("[LabelEnumData Reflux] Execute Error! EnumCode: '%s', Msg: %s",
                        labelEnumDataRefluxTask.getEnumMetaInfoId(),
                        e.getMessage());
                LOG.error(msg, e);
                return new ProcessResult(false, msg);
            }

            return new ProcessResult(true);
        } else {
            String msg = "Not Support Task! TaskName:" + taskName;
            LOG.error(msg);
            return new ProcessResult(false, msg);
        }
    }

    private List<LabelEnumDataRefluxTask> queryLabelEnumDataRefluxTasks(Set<Long> enumMetaIds) {
        List<LabelEnumDataRefluxTask> result = new ArrayList<>();
        EnumDimMetaInfoParameter query = new EnumDimMetaInfoParameter();
        query.setDeleted((byte) 0);
        query.setIds(new ArrayList<>(enumMetaIds));
        List<EnumDimMetaInfoDTO> needRefluxEnumMetaInfo = enumDimMetaInfoService.list(query);
        if (CollectionUtils.isEmpty(needRefluxEnumMetaInfo)) {
            return result;
        }

        Map<Long, LabelInfoDTO> labelEnumMaps = Maps.newHashMap();
        List<LabelInfoDTO> labels = labelInfoService.query(new LabelInfoParameter());
        for (LabelInfoDTO labelInfoDTO : labels) {
            if (labelInfoDTO.getDimEnumMetaId() != null) {
                labelEnumMaps.put(labelInfoDTO.getDimEnumMetaId(), labelInfoDTO);
            }
        }

        return buildLabelEnumDataRefluxTasks(needRefluxEnumMetaInfo, labelEnumMaps);
    }

    /**
     *
     *
     * @param enumMetaInfoList 枚举对象列表
     * @param labelMaps <enumId, LabelInfoDTO>
     * @return List<LabelEnumDataRefluxTask>
     */
    private List<LabelEnumDataRefluxTask> buildLabelEnumDataRefluxTasks(List<EnumDimMetaInfoDTO> enumMetaInfoList, Map<Long, LabelInfoDTO> labelMaps) {
        List<LabelEnumDataRefluxTask> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(enumMetaInfoList)) {
            return result;
        }

        enumMetaInfoList.forEach(enumMetaInfo -> {
                            try {
                                    if (LabelEnumValueSourceTypeEnum.SYSTEM.equals(enumMetaInfo.getType())
                                            || LabelEnumValueSourceTypeEnum.ODPS.equals(enumMetaInfo.getType())) {
                                        String yesterday = DateTime.now().minusDays(1).toString(OdpsConstants.ODPS_PHYSICAL_PROFILE_PARTITION_FORMAT);
                                        LabelInfoDTO label = labelMaps.get(enumMetaInfo.getId());
                                        if (label == null) {
                                            label = new LabelInfoDTO();
                                            label.setCode("enum_sync_without_label");
                                            label.setDataType(LabelBizDataTypeEnum.ENUM);
                                        }

                                        LabelEnumDataRefluxTask task = buildLabelEnumDataRefluxTask(
                                                label,
                                                enumMetaInfo,
                                                enumMetaInfo.getType(),
                                                yesterday
                                        );
                                        if (Objects.nonNull(task)) {
                                            result.add(task);
                                        }
                                    }
                            } catch (Exception ex) {
                                LOG.error("Build LabelEnumDataRefluxTask Error! Enum: {}", enumMetaInfo.getCode(), ex);
                            }
                        }
        );

        return result;
    }

    public static final Long LABEL_ENUM_DATA_REFLUX_LIMIT = 1000L;


    /**
     * 构建标签枚举值回流任务
     *
     * @param metaInfo 标签枚举值元数据信息
     * @param type     标签枚举值源类型
     * @return LabelEnumDataRefluxTask
     */
    private LabelEnumDataRefluxTask buildLabelEnumDataRefluxTask(LabelInfoDTO label,
                                                                 EnumDimMetaInfoDTO metaInfo,
                                                                 LabelEnumValueSourceTypeEnum type,
                                                                 String yyyyMMdd) {
        switch (type) {
            case ODPS: {
                OdpsGuid odpsGuid = null;
                String codeColumn = null;
                String descColumn = null;
                try {
                    String extInfoJson = metaInfo.getExtInfo();
                    if (StringUtils.isBlank(extInfoJson)) {
                        throw new ParamErrorException("[LabelEnumData Reflux] 枚举对象extInfo不能为空！");
                    }

                    JSONObject extInfo = (JSONObject) JSON.parse(extInfoJson);
                    String guid = extInfo.getString(LabelEnumDataConstants.EXT_INFO_GUID);
                    odpsGuid = OdpsGuid.newOdpsGuid(guid);

                    if (odpsGuid == null) {
                        throw new ParamErrorException("OdpsGuid Can't Be Null!");
                    }
                    codeColumn = extInfo.getString(LabelEnumDataConstants.EXT_INFO_ENUM_COLUMN);

                    if (codeColumn == null) {
                        throw new ParamErrorException("codeColumn Can't Be Null!");
                    }
                    descColumn = extInfo.getString(LabelEnumDataConstants.EXT_INFO_ENUM_DESC_COLUMN);

                    if (descColumn == null) {
                        throw new ParamErrorException("descColumn Can't Be Null!");
                    }
                } catch (Exception e) {
                    LOG.error("Parse EnumDimMetaInfoDTO ExtInfo Error! enumMetaId:{}, extInfoString:{}",
                            metaInfo.getId(),
                            metaInfo.getExtInfo());
                }

                // 获取枚举值维表接入表信息
                OdpsRegisterProfileTableDTO odpsRegisterTable = odpsRegisterProfileTableService.find(odpsGuid);
                if (odpsRegisterTable == null) {
                    throw new ParamErrorException("OdpsRegisterProfileTable Is Null! Please Add.");
                }

                return LabelEnumDataRefluxTask.LabelEnumDataRefluxTaskBuilder.aLabelEnumDataRefluxTask()
                        .labelCode(label.getCode())
                        .enumMetaInfoId(metaInfo.getId())
                        // ODPS维表类型目前仅支持String类型，不支持多值、KV类型
                        .labelBizDataType(LabelBizDataTypeEnum.ENUM)
                        .project(odpsRegisterTable.getProjectName())
                        .table(odpsRegisterTable.getTableName())
                        .codeColumn(codeColumn)
                        .descColumn(descColumn)
                        .partitionKey(odpsRegisterTable.getPartitionKey())
                        // 去当前系统时间的昨日日期
                        .partitionValue(yyyyMMdd)
                        .limit(LABEL_ENUM_DATA_REFLUX_LIMIT)
                        .build();
            }

            case SYSTEM: {
                if (label == null) {
                    throw new ParamErrorException(String.format("回流系统级枚举对象[%s]出错，对应标签为空！", metaInfo.getCode()));
                }
                if (Objects.isNull(label.getOdpsSourceConfig())) {
                    return null;
                }

                LabelOdpsSourceConfig odpsSourceConfig = label.getOdpsSourceConfig();
                // 获取标签接入表信息
                OdpsRegisterProfileTableDTO odpsRegisterTable = odpsRegisterProfileTableService.find(
                        odpsSourceConfig.getGuid());

                if (odpsRegisterTable == null) {
                    throw new ParamErrorException("OdpsRegisterProfileTable Is Null! Please Add.");
                }

                return LabelEnumDataRefluxTask.LabelEnumDataRefluxTaskBuilder.aLabelEnumDataRefluxTask()
                        .labelCode(label.getCode())
                        .enumMetaInfoId(metaInfo.getId())
                        .labelBizDataType(label.getDataType())
                        .project(odpsRegisterTable.getProjectName())
                        .table(odpsRegisterTable.getTableName())
                        .codeColumn(odpsSourceConfig.getField())
                        .descColumn(odpsSourceConfig.getField())
                        .partitionKey(odpsRegisterTable.getPartitionKey())
                        // 去当前系统时间的昨日日期
                        .partitionValue(yyyyMMdd)
                        .limit(LABEL_ENUM_DATA_REFLUX_LIMIT)
                        .build();
            }

            default:
                throw new ParamErrorException("Not Support LabelEnumValueSourceType To Build LabelEnumDataRefluxTask");
        }
    }

    @Autowired
    private TairRDBLockManager tairRDBLockManager;

    @Autowired
    private LabelEnumDataWorkflow labelEnumDataWorkflow;

    @Data
    private class LabelEnumDataWorkflowJob implements Callable<WorkReport> {

        private LabelEnumDataRefluxTask task;

        public LabelEnumDataWorkflowJob(LabelEnumDataRefluxTask task) {
            this.task = task;
        }

        @Override
        public WorkReport call() {
            WorkReport result = null;
            TairRDBLock lock = tairRDBLockManager.getLock(LABEL_ENUM_LOCK_PREFIX + task.getEnumMetaInfoId());

            if (lock.tryGetDistributedLock(EXPIRE_TIME_IN_SEC)) {
                try {
                    result = labelEnumDataWorkflow.run(ExecuteActionParam.of(task));
                } catch (Exception ex) {
                    LOG.error("Execute LabelEnumDataWorkflowJob Error:", ex);
                } finally {
                    lock.releaseDistributedLock();
                }
            } else {
                String msg = String.format("LabelEnumDataRefluxTask %s is already execute! try again later!",
                        task);
                throw new RuntimeException(msg);
            }

            return result;
        }
    }
}
