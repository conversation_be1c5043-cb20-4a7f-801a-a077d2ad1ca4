package com.fliggy.picasso.schedulerx.job;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.alarm.dingtalk.DingDingNotifySender;
import com.fliggy.picasso.common.constants.DingTalkConstants;
import com.fliggy.picasso.common.domain.crowd.record.AsyncTaskRecordVO;
import com.fliggy.picasso.common.enums.AsyncTaskRecordTypeEnum;
import com.fliggy.picasso.common.enums.AsyncTaskStatusEnum;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.dao.ActivityDO;
import com.fliggy.picasso.group.crowd.dag.galaxy.activity.DataSource;
import com.fliggy.picasso.group.crowd.dag.galaxy.activity.ItemDataDTO;
import com.fliggy.picasso.group.crowd.dag.galaxy.activity.domain.RuleConfig;
import com.fliggy.picasso.group.crowd.dag.galaxy.activity.domain.RuleGroupDTO;
import com.fliggy.picasso.group.crowd.dag.galaxy.constance.SymbolConstants;
import com.fliggy.picasso.group.crowd.dag.galaxy.convert.BizIdHelper;
import com.fliggy.picasso.group.crowd.dag.galaxy.enums.Ha3SearchFieldEnum;
import com.fliggy.picasso.group.crowd.dag.galaxy.enums.MergeCalTypeEnum;
import com.fliggy.picasso.group.crowd.dag.galaxy.ha3.service.GalaxyHa3SqlMergeCalService;
import com.fliggy.picasso.group.crowd.dag.galaxy.service.ActivityService;
import com.fliggy.picasso.group.crowd.dag.galaxy.service.TagMetaDTO;
import com.fliggy.picasso.group.crowd.dag.galaxy.service.TagMetaService;
import com.fliggy.picasso.group.crowd.dag.galaxy.utils.TagsUtil;
import com.fliggy.picasso.service.ic.IcItemQueryService;
import com.fliggy.picasso.service.record.AsyncTaskRecordService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DTS_LOG;

@Component
public class IcTagCheckProcessor extends JavaProcessor {

    private static final Logger log = LoggerFactory.getLogger(DTS_LOG);

    @Resource
    private AsyncTaskRecordService asyncTaskRecordService;
    @Resource
    private ActivityService activityService;
    @Resource
    private TagMetaService tagMetaService;
    @Resource
    private GalaxyHa3SqlMergeCalService galaxyHa3SqlMergeCalService;
    @Resource
    private IcItemQueryService icItemQueryService;
    @Resource
    private DingDingNotifySender dingDingNotifySender;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            List<AsyncTaskRecordVO> asyncTasks = asyncTaskRecordService.queryByTypeAndStatus(AsyncTaskRecordTypeEnum.IC_TAG_CHECK, AsyncTaskStatusEnum.INIT);
            if (CollectionUtils.isEmpty(asyncTasks)) {
                return new ProcessResult(true);
            }

            for (AsyncTaskRecordVO asyncTask : asyncTasks) {
                if (StringUtils.isBlank(asyncTask.getEntity())) {
                    continue;
                }
                long activityId = Long.parseLong(asyncTask.getEntity());
                Map<Long, Set<Integer>> unTagMap = new HashMap<>();
                checkItemIcTagInActivity(activityId, unTagMap);
                if (!unTagMap.isEmpty()) {
                    sendUnTagActivityAlarm(activityId, unTagMap);
                }
                updateAsyncTaskStatus(asyncTask.getId());
            }
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("IcTagCheckProcessor process error", e);
            return new ProcessResult(false, e.getMessage());
        }
    }

    private void checkItemIcTagInActivity(Long activityId, Map<Long, Set<Integer>> unTagMap) throws Exception {
        ActivityDO activityDO = activityService.getById(activityId);
        if (Objects.isNull(activityDO) || StringUtils.isBlank(activityDO.getTags())) {
            return;
        }
        if (Objects.isNull(activityDO.getStatus()) || !Objects.equals(activityDO.getStatus(), 1)) {
            // 池子发布未完成，不校验
            return;
        }
        if (DateUtils.getIntervalHours(activityDO.getGmtModified(), new Date()) < 1) {
            // 池子发布未超过1小时，异步ic打标可能未完成，继续等待
            return;
        }

        List<Long> tagIds = TagsUtil.convertToNumList(activityDO.getTags());
        if (CollectionUtils.isEmpty(tagIds)) {
            return;
        }
        List<TagMetaDTO> tagMetaList = tagMetaService.findByIdList(tagIds);
        if (CollectionUtils.isEmpty(tagMetaList)) {
            return;
        }
        List<Integer> icTagIds = tagMetaList.stream().map(TagMetaDTO::getTagId).map(Integer::parseInt).collect(Collectors.toList());

        Set<Long> currentItemIds = getCurrentItemIds(activityId);
        if (CollectionUtils.isEmpty(currentItemIds)) {
            return;
        }

        // 一般一个池子只会打一个ic标
        for (Integer icTagId : icTagIds) {
            List<List<Long>> partitions = Lists.partition(new ArrayList<>(currentItemIds), 500);
            for (List<Long> part : partitions) {
                batchCheckItemIcTag(part, icTagId, unTagMap);
                // ic 查询接口qps有限制，sleep 500ms
                Thread.sleep(500);
            }
        }
    }

    private void batchCheckItemIcTag(List<Long> itemIds, Integer icTagId, Map<Long, Set<Integer>> unTagMap) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return;
        }
        for (Long itemId : itemIds) {
            if (!icItemQueryService.hasIcTag(itemId, icTagId)) {
                log.error("itemId:{} has not icTagId:{}", itemId, icTagId);
                unTagMap.computeIfAbsent(itemId, k -> new HashSet<>()).add(icTagId);
            }
        }
    }

    private Set<Long> getCurrentItemIds(Long activityId) throws Exception {
        ActivityDO activityDO = new ActivityDO();
        activityDO.setId(activityId);
        activityDO.setDataSource(DataSource.TRAVEL.name());

        List<RuleConfig> ruleConfigList = new ArrayList<>();
        RuleConfig ruleConfig = new RuleConfig();
        ruleConfig.setRuleName("selectionTagHidden");
        ruleConfig.setRuleValue("tripgalaxy:" + activityId);
        ruleConfig.setRuleOp("EQUAL");
        ruleConfigList.add(ruleConfig);

        RuleGroupDTO ruleGroup = new RuleGroupDTO();
        ruleGroup.setRuleConfigList(ruleConfigList);

        List<RuleGroupDTO> ruleGroupDTOList = new ArrayList<>();
        ruleGroupDTOList.add(ruleGroup);
        activityDO.setRuleGroup(JSON.toJSONString(ruleGroupDTOList));

        List<ItemDataDTO> itemDataDTOS = galaxyHa3SqlMergeCalService.mergeCalItemIdListForDag(activityDO,
            Ha3SearchFieldEnum.BIZ_ID_AND_INDEX_FIELDS, MergeCalTypeEnum.BLANK);
        return itemDataDTOS.stream()
            .filter(Objects::nonNull)
            .map(ItemDataDTO::getBizId)
            .filter(bizId -> StringUtils.isNotEmpty(bizId) && bizId.contains(SymbolConstants.UNDER_LINE_STR))
            .map(BizIdHelper::parseBizIdLong)
            .collect(Collectors.toSet());
    }

    private void sendUnTagActivityAlarm(Long activityId, Map<Long, Set<Integer>> unTagMap) {
        StringBuilder content = new StringBuilder();
        content.append("### 【星辰IC标校验告警】\n\n");
        content.append("星辰池").append(activityId).append("以下商品打标未完成，请关注：\n\n");

        content.append("| 商品id | icTags |\n");
        content.append("| --- | --- |\n");

        for (Map.Entry<Long, Set<Integer>> entry : unTagMap.entrySet()) {
            if (Objects.isNull(entry.getKey()) || CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            content.append("| ").append(entry.getKey()).append(" | ").append(JSON.toJSONString(entry.getValue())).append(" |\n");
        }

        content.append("\n---\n\n");
        content.append("[星辰平台](https://tripgalaxy.alibaba-inc.com/#/manageCollect)");

        // 发送钉钉告警
        dingDingNotifySender.sendDingTalkGroupMsg(
            "星辰IC标校验告警",
            content.toString(),
            DingTalkConstants.GALAXY_IC_TAG_CHECK_ACCESS_TOKEN);
    }

    private void updateAsyncTaskStatus(Long id) {
        AsyncTaskRecordVO asyncTaskRecordVO = new AsyncTaskRecordVO();
        asyncTaskRecordVO.setId(id);
        asyncTaskRecordVO.setTaskStatus(AsyncTaskStatusEnum.SUCCESS);
        asyncTaskRecordService.update(asyncTaskRecordVO);
    }
}
