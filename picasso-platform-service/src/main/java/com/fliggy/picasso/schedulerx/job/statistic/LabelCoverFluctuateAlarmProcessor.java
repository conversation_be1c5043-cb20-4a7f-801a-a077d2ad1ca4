package com.fliggy.picasso.schedulerx.job.statistic;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapReduceJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.alarm.dingtalk.DingDingNotifySender;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.constants.DingTalkConstants;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.entity.bo.LabelStatisticsBO;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.label.LabelStatisticsService;
import com.taobao.ateye.annotation.Switch;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;

/**
 * 离线标签产出监控告警任务
 * 监控标签覆盖量波动情况，波动超过阈值则告警
 */
@Component
public class LabelCoverFluctuateAlarmProcessor extends MapReduceJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);

    @Switch(name = "标签覆盖量波动阈值")
    public static double FLUCTUATE_THRESHOLD = 0.5;

    @Resource
    private LabelInfoService labelInfoService;
    @Resource
    private LabelStatisticsService labelStatisticsService;
    @Resource
    private DingDingNotifySender dingDingNotifySender;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String dispatchName = "label_cover_amount_fluc_dispatch_level1";
        String taskName = context.getTaskName();
        
        try {
            if (isRootTask(context)) {
                List<LabelInfoDTO> labelList;
                String paramStr = StringUtils.isBlank(context.getInstanceParameters()) ? context.getJobParameters() : context.getInstanceParameters();
                if (StringUtils.isNotBlank(paramStr)) {
                    labelList = labelInfoService.findByCodes(Arrays.asList(paramStr.split(",")));
                } else {
                    labelList = labelInfoService.listOfflineStandardLabels();
                }

                if (CollectionUtils.isEmpty(labelList)) {
                    return new ProcessResult(true);
                }
                return map(labelList, dispatchName);
            } else if (taskName.equals(dispatchName)) {
                LabelInfoDTO labelDTO = (LabelInfoDTO) context.getTask();
                if (Objects.isNull(labelDTO)) {
                    return new ProcessResult(true);
                }
                
                // 检查标签覆盖量波动
                LabelFluctuateInfo fluctuateInfo = checkLabelFluctuation(labelDTO);
                return new ProcessResult(true, JSON.toJSONString(fluctuateInfo));
            } else {
                return new ProcessResult(true);
            }
        } catch (Exception e) {
            log.error("离线标签覆盖量波动检查失败", e);
            return new ProcessResult(false, e.getMessage());
        }
    }
    
    @Override
    public ProcessResult reduce(JobContext context) {
        Map<Long, String> taskResults = context.getTaskResults();
        if (taskResults == null || taskResults.isEmpty()) {
            return new ProcessResult(true);
        }
        
        List<LabelFluctuateInfo> fluctuatedLabels = new ArrayList<>();
        for (String result : taskResults.values()) {
            if (StringUtils.isEmpty(result)) {
                continue;
            }
            LabelFluctuateInfo labelFluctuateInfo = JSON.parseObject(result, LabelFluctuateInfo.class);
            if (Objects.isNull(labelFluctuateInfo)) {
                continue;
            }
            if (labelFluctuateInfo.isFluctuated()) {
                fluctuatedLabels.add(labelFluctuateInfo);
            }
        }
        
        if (CollectionUtils.isNotEmpty(fluctuatedLabels)) {
            String yesterday = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
            String dayBeforeYesterday = DateTime.now().minusDays(2).toString(DateUtils.YMD_FORMAT02);
            sendAlarm(fluctuatedLabels, yesterday, dayBeforeYesterday);
        }
        return new ProcessResult(true);
    }

    /**
     * 检查标签覆盖量波动
     */
    private LabelFluctuateInfo checkLabelFluctuation(LabelInfoDTO labelDTO) {
        String yesterday = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
        String dayBeforeYesterday = DateTime.now().minusDays(2).toString(DateUtils.YMD_FORMAT02);
        
        // 查询昨天和前天的标签覆盖量
        LabelStatisticsBO yesterdayStat = labelStatisticsService.queryByLabelCodeAndDate(labelDTO.getCode(), yesterday);
        LabelStatisticsBO dayBeforeYesterdayStat = labelStatisticsService.queryByLabelCodeAndDate(labelDTO.getCode(), dayBeforeYesterday);
        if (Objects.isNull(yesterdayStat) || Objects.isNull(dayBeforeYesterdayStat)) {
            return null;
        }
        
        // 计算覆盖量波动
        Long yesterdayCoverAmount = yesterdayStat.getCoverAmount();
        Long dayBeforeYesterdayCoverAmount = dayBeforeYesterdayStat.getCoverAmount();
        if (Objects.isNull(yesterdayCoverAmount) || Objects.isNull(dayBeforeYesterdayCoverAmount)
                || dayBeforeYesterdayCoverAmount == 0) {
            return null;
        }
        
        // 计算波动率 (|今天 - 昨天| / 昨天)
        double fluctuateRate = Math.abs((double) (yesterdayCoverAmount - dayBeforeYesterdayCoverAmount)) / dayBeforeYesterdayCoverAmount;
        if (fluctuateRate > FLUCTUATE_THRESHOLD) {
            LabelFluctuateInfo fluctuateInfo = new LabelFluctuateInfo();
            fluctuateInfo.setLabelCode(labelDTO.getCode());
            fluctuateInfo.setLabelName(labelDTO.getName());
            fluctuateInfo.setYesterdayCoverAmount(yesterdayCoverAmount);
            fluctuateInfo.setDayBeforeYesterdayCoverAmount(dayBeforeYesterdayCoverAmount);
            fluctuateInfo.setFluctuateRate(fluctuateRate);
            fluctuateInfo.setFluctuated(true);
            return fluctuateInfo;
        }
        return null;
    }

    /**
     * 发送钉钉告警
     */
    private void sendAlarm(List<LabelFluctuateInfo> fluctuatedLabels, String yesterday, String dayBeforeYesterday) {
        StringBuilder content = new StringBuilder();
        content.append("### 【诸葛离线标签监控-覆盖量波动告警】\n\n");
        content.append("以下标签覆盖量波动超过").append(FLUCTUATE_THRESHOLD * 100).append("%，请关注：\n\n");
        
        content.append("| 标签code | 标签名称 | ").append(yesterday).append("覆盖量 | ")
               .append(dayBeforeYesterday).append("覆盖量 | 波动率 |\n");
        content.append("| --- | --- | --- | --- | --- |\n");
        
        for (LabelFluctuateInfo labelInfo : fluctuatedLabels) {
            BigDecimal rate = BigDecimal.valueOf(labelInfo.getFluctuateRate() * 100).setScale(2, RoundingMode.HALF_UP);
            content.append("| ").append(labelInfo.getLabelCode()).append(" | ")
                   .append(labelInfo.getLabelName()).append(" | ")
                   .append(labelInfo.getYesterdayCoverAmount()).append(" | ")
                   .append(labelInfo.getDayBeforeYesterdayCoverAmount()).append(" | ")
                   .append(rate).append("% |\n");
        }
        
        content.append("\n---\n\n");
        content.append("[诸葛平台](https://zhuge.alibaba-inc.com)");
        
        // 发送钉钉告警
        dingDingNotifySender.sendDingTalkGroupMsg(
                "诸葛离线标签覆盖量波动告警",
                content.toString(),
                DingTalkConstants.OFFLINE_LABEL_ALARM_ACCESS_TOKEN);
        
        log.info("标签覆盖量波动告警，content: {}", content.toString());
    }
    
    /**
     * 标签波动信息
     */
    @Data
    public static class LabelFluctuateInfo {
        /**
         * 标签编码
         */
        private String labelCode;
        
        /**
         * 标签名称
         */
        private String labelName;
        
        /**
         * 昨天覆盖量
         */
        private Long yesterdayCoverAmount;
        
        /**
         * 前天覆盖量
         */
        private Long dayBeforeYesterdayCoverAmount;
        
        /**
         * 波动率
         */
        private Double fluctuateRate;
        
        /**
         * 是否波动超过阈值
         */
        private boolean fluctuated;
    }
} 