package com.fliggy.picasso.schedulerx.job.alarm;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.common.Constant;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.enums.alarm.AlarmSceneTypeEnum;
import com.fliggy.picasso.common.enums.alarm.DataFlucDimEnum;
import com.fliggy.picasso.common.enums.alarm.RoarAlarmTypeEnum;
import com.fliggy.picasso.config.SwitchConfig;
import com.fliggy.picasso.entity.alarm.CrowdDataFlucInfos;
import com.fliggy.picasso.entity.alarm.CrowdMetaInfoWithAlarmInfos;
import com.fliggy.picasso.entity.bo.CrowdAlarmInfoBO;
import com.fliggy.picasso.entity.bo.CrowdMonitorConfigBO;
import com.fliggy.picasso.entity.bo.MonitorExtInfo;
import com.fliggy.picasso.service.alarm.CrowdAlarmFactory;
import com.fliggy.picasso.service.alarm.CrowdMonitorConfigService;
import com.fliggy.picasso.service.alarm.impl.AbstractCrowdAlarmServiceImpl;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.fliggy.pokemon.client.api.odps.OdpsTunnelDownloadService;
import com.fliggy.pokemon.client.callback.DataFlowCallback;
import com.fliggy.pokemon.client.odps.domain.OdpsTableInfo;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.ODPS_PROJECT_TRIP_PROFILE;

@Slf4j
@Component
public class CrowdDataFlucAlarmProcessor extends JavaProcessor {

    private static final String CROWD_DATA_FLUC_ODPS_TABLE = "trip_crowd_data_fluc_statistics";

    @Resource
    private OdpsTunnelDownloadService odpsTunnelDownloadService;
    @Resource
    private CrowdService crowdService;
    @Resource
    private CrowdMonitorConfigService crowdMonitorConfigService;
    @Resource
    private CrowdAlarmFactory crowdAlarmFactory;

    @Switch(description = "数据波动表download page size")
    public int crowdDataFlucDownloadPageSize = 200;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        // 读取odps表数据
        List<CrowdDataFlucInfos> dataFlucInfosList = readOdpsTable();
        if (SwitchConfig.COMMON_DEBUG_SWITCH) {
            dataFlucInfosList = dataFlucInfosList.stream().filter(info -> StringUtils.isNotBlank(info.getCreator())
                && info.getCreator().contains(SwitchConfig.ALARM_DEBUG_CREATOR_NAME)).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(dataFlucInfosList)) {
            return new ProcessResult(true, "No Data Fluc!");
        }

        // 过滤掉没有配置告警的人群
        List<CrowdMetaInfoWithAlarmInfos> validDataFlucList = filterNonAlarmConfigCrowd(dataFlucInfosList);
        if (CollectionUtils.isEmpty(validDataFlucList)) {
            return new ProcessResult(true);
        }

        // 按接收人聚合
        Map<String, Map<RoarAlarmTypeEnum, Map<Long, Map<DataFlucDimEnum, String>>>> dataFlucReceiverMap = getCrowdReceiverMap(validDataFlucList);
        if (dataFlucReceiverMap.isEmpty()) {
            return new ProcessResult(true);
        }

        // 发送告警
        sendCrowdDataFlucAlarm(dataFlucReceiverMap);
        return new ProcessResult(true);
    }

    private List<CrowdMetaInfoWithAlarmInfos> filterNonAlarmConfigCrowd(List<CrowdDataFlucInfos> dataFlucInfosList) {
        List<Long> crowdIds = dataFlucInfosList.stream().map(CrowdDataFlucInfos::getCrowdId).collect(Collectors.toList());
        List<CrowdMetaInfoDO> crowdMetaInfoDOList = crowdService.queryByIds(crowdIds);
        if (CollectionUtils.isEmpty(crowdMetaInfoDOList)) {
            return Lists.newArrayList();
        }
        Map<Long, CrowdMetaInfoDO> crowdDOMap = crowdMetaInfoDOList.stream().collect(Collectors.toMap(CrowdMetaInfoDO::getId, crowd -> crowd));

        List<CrowdMonitorConfigBO> configBOList = crowdMonitorConfigService.queryByCrowdIdsAndSceneTypesWithDefault(crowdIds, AlarmSceneTypeEnum.CROWD_DATA_FLUCTUATE);
        if (CollectionUtils.isEmpty(configBOList)) {
            return Lists.newArrayList();
        }
        Map<Long, CrowdMonitorConfigBO> configBOMap = configBOList.stream().filter(config -> Objects.nonNull(config) && config.getIsValid())
            .collect(Collectors.toMap(CrowdMonitorConfigBO::getCrowdId, config -> config));
        if (configBOMap.isEmpty()) {
            return Lists.newArrayList();
        }

        List<CrowdMetaInfoWithAlarmInfos> result = new ArrayList<>();
        for (CrowdDataFlucInfos crowdDataFlucInfos : dataFlucInfosList) {
            CrowdMetaInfoDO crowdMetaInfoDO = crowdDOMap.get(crowdDataFlucInfos.getCrowdId());
            CrowdMonitorConfigBO config = configBOMap.get(crowdDataFlucInfos.getCrowdId());
            if (crowdMetaInfoDO == null || config == null || CollectionUtils.isEmpty(config.getAlarmTypes())
                || CollectionUtils.isEmpty(config.getReceiverTypes()) || config.getExtInfo() == null) {
                continue;
            }
            MonitorExtInfo extInfo = config.getExtInfo();
            if (extInfo.getDataFlucDimMap() == null || extInfo.getDataFlucDimMap().isEmpty()) {
                continue;
            }
            CrowdMetaInfoWithAlarmInfos crowdMetaInfoWithAlarmInfos = new CrowdMetaInfoWithAlarmInfos(crowdMetaInfoDO, config.getAlarmTypes());
            crowdMetaInfoWithAlarmInfos.setDataFlucConfigMap(extInfo.getDataFlucDimMap());
            addUpDataFlucRate(crowdMetaInfoDO.getCrowdAmount(), crowdDataFlucInfos);
            crowdMetaInfoWithAlarmInfos.setCrowdDataFlucInfos(crowdDataFlucInfos);
            result.add(crowdMetaInfoWithAlarmInfos);
        }
        return result;
    }

    private void addUpDataFlucRate(Long crowdAmount, CrowdDataFlucInfos crowdDataFlucInfos) {
        crowdDataFlucInfos.setYesterdayAmountFlucRate(calculateFlucRate(crowdDataFlucInfos.getYesterdayAmount(), crowdAmount));
        crowdDataFlucInfos.setDays7BeforeAmountFlucRate(calculateFlucRate(crowdDataFlucInfos.getDays7BeforeAmount(), crowdAmount));
        crowdDataFlucInfos.setDays7AvgAmountFlucRate(calculateFlucRate(crowdDataFlucInfos.getDays7AvgAmount(), crowdAmount));
    }

    private Double calculateFlucRate(Double a, Long b) {
        if (a == null) {
            return 0.0;
        }
        return Math.abs(b-a) * 100.0 / a;
    }

    private Double calculateFlucRate(Long a, Long b) {
        if (a == null) {
            return 0.0;
        }
        return Math.abs(b-a) * 100.0 / a;
    }

    private String calculateFlucRate(Double num) {
        if (num == null) {
            return "0.0";
        }
        return String.format("%.2f", num);
    }

    private Map<String, Map<RoarAlarmTypeEnum, Map<Long, Map<DataFlucDimEnum, String>>>> getCrowdReceiverMap(List<CrowdMetaInfoWithAlarmInfos> crowdWithAlarmList) {
        Map<String, Map<RoarAlarmTypeEnum, Map<Long, Map<DataFlucDimEnum, String>>>> result = new HashMap<>();
        for (CrowdMetaInfoWithAlarmInfos crowdWithAlarm : crowdWithAlarmList) {
            CrowdMetaInfoDO crowdMetaInfoDO = crowdWithAlarm.getCrowdMetaInfoDO();
            List<RoarAlarmTypeEnum> alarmTypes = crowdWithAlarm.getAlarmTypes();
            Map<DataFlucDimEnum, Integer> dataFlucConfigMap = crowdWithAlarm.getDataFlucConfigMap();
            CrowdDataFlucInfos crowdDataFlucInfos = crowdWithAlarm.getCrowdDataFlucInfos();
            if (Objects.isNull(crowdMetaInfoDO) || CollectionUtils.isEmpty(alarmTypes)
                || Objects.isNull(dataFlucConfigMap) || dataFlucConfigMap.isEmpty() || Objects.isNull(crowdDataFlucInfos)) {
                continue;
            }

            Map<DataFlucDimEnum, String> dataFlucMap = new HashMap<>();
            if (dataFlucConfigMap.containsKey(DataFlucDimEnum.YESTERDAY) && Objects.nonNull(crowdDataFlucInfos.getYesterdayAmountFlucRate())
                && crowdDataFlucInfos.getYesterdayAmountFlucRate() > dataFlucConfigMap.get(DataFlucDimEnum.YESTERDAY)) {
                dataFlucMap.put(DataFlucDimEnum.YESTERDAY, calculateFlucRate(crowdDataFlucInfos.getYesterdayAmountFlucRate()));
            }
            if (dataFlucConfigMap.containsKey(DataFlucDimEnum.DAYS_7_BEFORE) && Objects.nonNull(crowdDataFlucInfos.getDays7BeforeAmountFlucRate())
                && crowdDataFlucInfos.getDays7BeforeAmountFlucRate() > dataFlucConfigMap.get(DataFlucDimEnum.DAYS_7_BEFORE)) {
                dataFlucMap.put(DataFlucDimEnum.DAYS_7_BEFORE, calculateFlucRate(crowdDataFlucInfos.getDays7BeforeAmountFlucRate()));
            }
            if (dataFlucConfigMap.containsKey(DataFlucDimEnum.DAYS_7_AVG) && Objects.nonNull(crowdDataFlucInfos.getDays7AvgAmountFlucRate())
                && crowdDataFlucInfos.getDays7AvgAmountFlucRate() > dataFlucConfigMap.get(DataFlucDimEnum.DAYS_7_AVG)) {
                dataFlucMap.put(DataFlucDimEnum.DAYS_7_AVG, calculateFlucRate(crowdDataFlucInfos.getDays7AvgAmountFlucRate()));
            }
            if (dataFlucMap.isEmpty()) {
                continue;
            }

            List<Employee> alarmReceivers = crowdMonitorConfigService.getAlarmReceivers(crowdMetaInfoDO, AlarmSceneTypeEnum.CROWD_DATA_FLUCTUATE);
            for (Employee receiver : alarmReceivers) {
                if (receiver == null || receiver.getEmpId() == null) {
                    continue;
                }

                for (RoarAlarmTypeEnum alarmType : alarmTypes) {
                    result.computeIfAbsent(receiver.getEmpId(), k -> new HashMap<>())
                        .computeIfAbsent(alarmType, k -> new HashMap<>())
                        .put(crowdMetaInfoDO.getId(), dataFlucMap);
                }
            }
        }
        return result;
    }

    private List<CrowdDataFlucInfos> readOdpsTable() {
        OdpsTableInfo odpsTableInfo = new OdpsTableInfo();
        odpsTableInfo.setProject(ODPS_PROJECT_TRIP_PROFILE);
        odpsTableInfo.setTable(CROWD_DATA_FLUC_ODPS_TABLE);
        String ds = DateTime.now().minusDays(1).toString(Constant.YYYYMMDD);;
        odpsTableInfo.setPartition("ds=" + ds);

        List<CrowdDataFlucInfos> dataFlucInfosList = new ArrayList<>();
        odpsTunnelDownloadService.streamDownload(odpsTableInfo, new DataFlowCallback() {
            @Override
            public void success(int code, String message, Object data) {

            }

            @Override
            public void fail(int code, String message, Object data) {
                log.error("CrowdDataFlucAlarmProcessor readOdpsTable failed. msg:" + message);
            }

            @Override
            public void stream(List<Map<String, Object>> list) {
                for (Map<String, Object> map : list) {
                    CrowdDataFlucInfos dataFlucInfos = generateCrowdDataFlucInfos(map);
                    dataFlucInfosList.add(dataFlucInfos);
                }
            }

            @Override
            public void total(long total) {

            }
        }, crowdDataFlucDownloadPageSize);
        return dataFlucInfosList;
    }

    private CrowdDataFlucInfos generateCrowdDataFlucInfos(Map<String, Object> map) {
        CrowdDataFlucInfos dataFlucInfos = new CrowdDataFlucInfos();
        if (!map.containsKey("id")) {
            return null;
        }
        dataFlucInfos.setCrowdId((Long)map.get("id"));
        if (map.containsKey("creator")) {
            dataFlucInfos.setCreator((String)map.get("creator"));
        }
if (map.containsKey("yesterday_amount") && map.get("yesterday_amount") instanceof Long) {
    dataFlucInfos.setYesterdayAmount((Long) map.get("yesterday_amount"));
}
if (map.containsKey("days_7_before_amount") && map.get("days_7_before_amount") instanceof Long) {
    dataFlucInfos.setDays7BeforeAmount((Long) map.get("days_7_before_amount"));
}
if (map.containsKey("days_7_avg_amount") && map.get("days_7_avg_amount") instanceof Double) {
    dataFlucInfos.setDays7AvgAmount((Double) map.get("days_7_avg_amount"));
}
        return dataFlucInfos;
    }

    private void sendCrowdDataFlucAlarm(Map<String, Map<RoarAlarmTypeEnum, Map<Long, Map<DataFlucDimEnum, String>>>> dataFlucReceiverMap) {
        AbstractCrowdAlarmServiceImpl alarmService = crowdAlarmFactory.choose(AlarmSceneTypeEnum.CROWD_DATA_FLUCTUATE);
        if (Objects.isNull(alarmService)) {
            return;
        }
        for (Map.Entry<String, Map<RoarAlarmTypeEnum, Map<Long, Map<DataFlucDimEnum, String>>>> entry : dataFlucReceiverMap.entrySet()) {
            String empId = entry.getKey();
            if (StringUtils.isEmpty(empId) || entry.getValue().isEmpty()) {
                continue;
            }
            for (Map.Entry<RoarAlarmTypeEnum, Map<Long, Map<DataFlucDimEnum, String>>> alarmTypeEntry : entry.getValue().entrySet()) {
                RoarAlarmTypeEnum alarmType = alarmTypeEntry.getKey();
                if (alarmType == null || alarmTypeEntry.getValue().isEmpty()) {
                    continue;
                }

                if (Objects.equals(alarmType, RoarAlarmTypeEnum.DING_TALK)) {
                    for (Map.Entry<Long, Map<DataFlucDimEnum, String>> crowdIdEntry : alarmTypeEntry.getValue().entrySet()) {
                        Long crowdId = crowdIdEntry.getKey();
                        Map<DataFlucDimEnum, String> dataFlucMap = crowdIdEntry.getValue();
                        if (Objects.isNull(crowdId) || dataFlucMap.isEmpty()) {
                            continue;
                        }

                        CrowdAlarmInfoBO crowdAlarmInfoBO = new CrowdAlarmInfoBO();
                        crowdAlarmInfoBO.setSceneType(AlarmSceneTypeEnum.CROWD_DATA_FLUCTUATE);
                        crowdAlarmInfoBO.setAlarmType(alarmType);
                        crowdAlarmInfoBO.setCrowdIds(Collections.singletonList(crowdId));
                        crowdAlarmInfoBO.setReceiver(empId);
                        crowdAlarmInfoBO.setDataFluc(Collections.singletonMap(crowdId, dataFlucMap));
                        alarmService.sendAlarm(crowdAlarmInfoBO);
                    }
                } else {
                    Set<Long> crowdIds = alarmTypeEntry.getValue().keySet();
                    if (CollectionUtils.isEmpty(crowdIds)) {
                        continue;
                    }

                    CrowdAlarmInfoBO crowdAlarmInfoBO = new CrowdAlarmInfoBO();
                    crowdAlarmInfoBO.setSceneType(AlarmSceneTypeEnum.CROWD_DATA_FLUCTUATE);
                    crowdAlarmInfoBO.setAlarmType(alarmType);
                    crowdAlarmInfoBO.setCrowdIds(new ArrayList<>(crowdIds));
                    crowdAlarmInfoBO.setReceiver(empId);
                    crowdAlarmInfoBO.setDataFluc(alarmTypeEntry.getValue());
                    alarmService.sendAlarm(crowdAlarmInfoBO);
                }
            }
        }
    }

}
