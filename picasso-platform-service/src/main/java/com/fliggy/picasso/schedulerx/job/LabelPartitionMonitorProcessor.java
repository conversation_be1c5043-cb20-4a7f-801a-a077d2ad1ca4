package com.fliggy.picasso.schedulerx.job;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.enums.score.LabelTagEnum;
import com.fliggy.picasso.common.odps.OdpsGuid;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.offline.OdpsMetaService;
import com.fliggy.picasso.service.label.LabelInfoParameter;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.DISPATCH_LOG;

/**
 * 标签分区监控处理器
 * 用于监控离线标签底表分区情况，并标记延迟产出的标签
 */
@Component
public class LabelPartitionMonitorProcessor extends MapJobProcessor {

    private static final Logger log = LoggerFactory.getLogger(DISPATCH_LOG);
    
    private static final String DISPATCH_TASK_NAME = "label_partition_monitor_dispatch";

    @Resource
    private LabelInfoService labelInfoService;
    
    @Resource
    private OdpsMetaService odpsMetaService;
    
    @Switch(description = "最大重试次数")
    private int MAX_RETRY_COUNT = 3;
    
    @Switch(description = "重试间隔（毫秒）")
    private long RETRY_INTERVAL = 1000;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String taskName = context.getTaskName();
        Object task = context.getTask();
        try {
            if (isRootTask(context)) {
                // 先清除所有标签的【延迟产出】标签
                clearAllDelayTags();

                // 获取所有离线标准标签
                List<LabelInfoDTO> labelList;
                String paramStr = StringUtils.isBlank(context.getInstanceParameters())? context.getJobParameters() : context.getInstanceParameters();
                if (StringUtils.isNotBlank(paramStr)) {
                    labelList = labelInfoService.findByCodes(Arrays.asList(paramStr.split(",")));
                }else {
                    labelList = labelInfoService.listOfflineStandardLabels();
                }

                if (org.apache.commons.collections4.CollectionUtils.isEmpty(labelList)) {
                    return new ProcessResult(true);
                }
                return map(labelList, DISPATCH_TASK_NAME);
            } else if (DISPATCH_TASK_NAME.equals(taskName)) {
                LabelInfoDTO label = (LabelInfoDTO) task;
                JSONObject sourceConfig = label.getSourceConfig();
                if (sourceConfig == null) {
                    return new ProcessResult(true);
                }
                
                String project = sourceConfig.getString("project");
                String table = sourceConfig.getString("table");
                if (StringUtils.isEmpty(project) || StringUtils.isEmpty(table)) {
                    return new ProcessResult(true);
                }
                OdpsGuid odpsGuid = new OdpsGuid(project, table);
                
                // 获取最新分区，带重试机制
                String maxPt = getMaxPtWithRetry(odpsGuid);
                String yesterday = DateTime.now().minusDays(1).toString(DateUtils.YMD_FORMAT02);
                if (StringUtils.isEmpty(maxPt) || !maxPt.equals(yesterday)) {
                    // 如果不是昨天分区，则打上【延迟产出】标签
                    addDelayTag(label);
                }
                return new ProcessResult(true);
            }
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("处理标签分区监控任务异常", e);
            return new ProcessResult(false, e.getMessage());
        }
    }
    
    /**
     * 清除所有标签的【延迟产出】标签
     */
    private void clearAllDelayTags() {
        try {
            LabelInfoParameter labelInfoParameter = new LabelInfoParameter();
            labelInfoParameter.setLabelTag(LabelTagEnum.OUTPUT_LATE.getCode());
            List<LabelInfoDTO> labelList = labelInfoService.query(labelInfoParameter);
            if (CollectionUtils.isEmpty(labelList)) {
                return;
            }
            
            for (LabelInfoDTO labelDTO : labelList) {
                removeDelayTag(labelDTO);
            }
        } catch (Exception e) {
            log.error("清除【延迟产出】标签异常", e);
        }
    }
    
    /**
     * 获取最新分区，带重试机制
     * 
     * @param odpsGuid ODPS表GUID
     * @return 最新分区字符串
     */
    private String getMaxPtWithRetry(OdpsGuid odpsGuid) {
        String maxPt = null;
        int retryCount = 0;
        while (retryCount < MAX_RETRY_COUNT) {
            try {
                maxPt = odpsMetaService.getMaxPtString(odpsGuid);
                if (StringUtils.isNotEmpty(maxPt)) {
                    return maxPt;
                }
            } catch (Exception e) {
                log.warn("获取表[{}]最新分区异常，重试次数：{}", odpsGuid.toString(), retryCount + 1, e);
            }
            
            retryCount++;
            if (retryCount < MAX_RETRY_COUNT) {
                try {
                    TimeUnit.MILLISECONDS.sleep(RETRY_INTERVAL);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        return maxPt;
    }
    
    /**
     * 给标签添加【延迟产出】标签
     * 
     * @param label 标签信息
     */
    private void addDelayTag(LabelInfoDTO label) {
        List<String> tagList = label.getTagList();
        if (tagList == null) {
            tagList = new ArrayList<>();
            label.setTagList(tagList);
        }

        if (tagList.contains(LabelTagEnum.OUTPUT_LATE.getCode())) {
            return;
        }
        tagList.add(LabelTagEnum.OUTPUT_LATE.getCode());
        updateLabelTagList(label.getId(), tagList);
    }
    
    /**
     * 移除标签的【延迟产出】标签
     * 
     * @param label 标签信息
     */
    private void removeDelayTag(LabelInfoDTO label) {
        List<String> tagList = label.getTagList();
        if (CollectionUtils.isNotEmpty(tagList) && tagList.contains(LabelTagEnum.OUTPUT_LATE.getCode())) {
            tagList.remove(LabelTagEnum.OUTPUT_LATE.getCode());
            updateLabelTagList(label.getId(), tagList);
        }
    }
    
    /**
     * 更新标签的tagList字段
     * 
     * @param labelId 标签ID
     * @param tagList 标签列表
     */
    private void updateLabelTagList(Long labelId, List<String> tagList) {
        try {
            LabelInfoDTO updateDTO = new LabelInfoDTO();
            updateDTO.setId(labelId);
            updateDTO.setTagList(tagList);
            labelInfoService.updateSelectiveById(labelId, updateDTO);
        } catch (Exception e) {
            log.error("更新标签[{}]的tagList字段失败", labelId, e);
        }
    }
} 