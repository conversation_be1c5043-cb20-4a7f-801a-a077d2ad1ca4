package com.fliggy.picasso.config.switcher;

import java.util.List;
import java.util.Set;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;

/**
 * <AUTHOR>
 * @date 2025/3/17
 */
public class CrowdSwitchConfig {
    @AppSwitch(des = "开启新的人群分组 hash 算法", level = Level.p4)
    public static boolean openTppHashUser = false;

    @AppSwitch(des = "开启编辑后自动加速调度", level = Level.p4)
    public static boolean openEditBoostAutoSchedule = true;

    @AppSwitch(des = "开启 sql 圈人多表优先级逻辑", level = Level.p4)
    public static boolean openSqlMultiTablePriority = true;

    @AppSwitch(des = "重保人群，优先级最高", level = Level.p4)
    public static List<Long> highPriorityCrowdIds = Lists.newArrayList();

    @AppSwitch(des = "人群导出优先级", level = Level.p4)
    public static int crowdExportPriority = 8;

    @AppSwitch(des = "官方人群ID列表", level = Level.p4)
    public static Set<Long> officialCrowds = Sets.newHashSet();
}
