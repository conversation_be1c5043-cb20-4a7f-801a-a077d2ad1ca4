package com.fliggy.picasso.exception;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 洞察分析异常
 *
 * <AUTHOR>
 * Created on 2022/5/26 下午2:20
 */
public class AnalysisException extends RuntimeException {


    @Setter
    @Getter
    private Map<String, String> extMap;

    @Setter
    @Getter
    private String msg;

    public AnalysisException(Throwable cause) {
        super(cause);
    }

    public AnalysisException(String message, Throwable cause) {
        super(message, cause);
    }

    public AnalysisException(AnalysisException e, String key, String value) {

        if (StringUtils.isNotBlank(e.getMsg())) {
            this.msg = e.getMsg();
        }
        Map<String, String> extMsg = e.getExtMap();
        if (MapUtils.isEmpty(extMsg)) {
            extMsg = new HashMap<>();
        }
        extMsg.put(key, value);
        this.extMap = extMsg;
    }

    public void setExtMsg(String key, String value) {
        Map<String, String> extMap = this.getExtMap();
        if (MapUtils.isEmpty(extMap)) {
            extMap = new HashMap<>();
            this.extMap = extMap;
        }
        extMap.put(key, value);
    }

    /**
     * 异常信息构建
     *
     * @param e
     * @param msg
     * @return
     */
    public static String buildErrorMsg(Exception e, String msg) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(msg);
        if (e instanceof AnalysisException) {
            AnalysisException e1 = (AnalysisException) e;
            stringBuilder.append(e1.getMessage());
            if (MapUtils.isNotEmpty(e1.getExtMap())) {
                stringBuilder.append(JSON.toJSONString(e1.getExtMap()));
            }
            return stringBuilder.toString();
        } else {
            stringBuilder.append(e.getMessage());
        }
        return stringBuilder.toString();
    }


}
