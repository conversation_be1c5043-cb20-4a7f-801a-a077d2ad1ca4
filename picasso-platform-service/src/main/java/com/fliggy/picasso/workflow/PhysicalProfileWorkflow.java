package com.fliggy.picasso.workflow;

import com.fliggy.picasso.action.ExecuteActionParam;
import com.fliggy.picasso.action.PhysicalProfileActionParam;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO.PhysicalProfileStatus;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoService;
import com.fliggy.picasso.workflow.profilework.service.common.ProfileFlowCreateService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.jeasy.flows.work.WorkContext;
import org.jeasy.flows.work.WorkReport;
import org.jeasy.flows.work.WorkStatus;
import org.jeasy.flows.workflow.WorkFlow;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.fliggy.picasso.common.Constant.TASK_LOG;
import static org.jeasy.flows.engine.WorkFlowEngineBuilder.aNewWorkFlowEngine;

/**
 * <AUTHOR>
 * @date 2021/1/5 下午3:43
 */
@Component
public class PhysicalProfileWorkflow {

    private static final Logger LOG = LoggerFactory.getLogger(TASK_LOG);

    private ExecutorService executorService;

    @Resource
    private ProfileFlowCreateService profileFlowCreateService;

    @PostConstruct
    public void init() {
        this.executorService = new ThreadPoolExecutor(0, Integer.MAX_VALUE,
                60L, TimeUnit.SECONDS,
                new SynchronousQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("PhysicalProfile-Workflow-Pool-").build()
        );
    }

    @Autowired
    private PhysicalProfileInfoService physicalProfileInfoService;

    /**
     * 执行物理画像表工作流
     *
     * @param param
     * @return
     */
    public WorkReport run(ExecuteActionParam<PhysicalProfileActionParam> param) {

        PhysicalProfileInfoDTO profile = param.getData().getPhysicalProfileInfo();

        // 更新物理画像状态为待回流
        updatePhysicalProfileStatus(profile, PhysicalProfileStatus.REFLUXING);

        WorkReport workReport = null;
        try {

            // 生成物理画像工作流
            WorkFlow workflow = physicalProfileWorkflow(param);

            // 初始化上下文
            WorkContext context = initContext(profile);

            // 执行工作流
            workReport = aNewWorkFlowEngine()
                    .build()
                    .run(workflow, context);
            LOG.info("【物理画像】执行画像工作流：code:{}, status:{}", profile.getPhysicalProfileCode(), workReport.getStatus());

            if (WorkStatus.COMPLETED.equals(workReport.getStatus())) {
                updatePhysicalProfileStatus(profile, PhysicalProfileStatus.REFLUXED);
            } else {
                updatePhysicalProfileStatus(profile, PhysicalProfileStatus.REFLUX_EXCEPTION);
            }

        } catch (Exception e) {
            LOG.error("【物理画像】执行画像工作流失败：code:{}, error:{}", profile.getProfileCode(), e.getMessage());
            updatePhysicalProfileStatus(profile, PhysicalProfileStatus.REFLUX_EXCEPTION);
        }

        return workReport;
    }

    /**
     * 初始化上下文
     *
     * @param profile 物理画像
     * @return 上下文
     */
    private WorkContext initContext(PhysicalProfileInfoDTO profile) {
        WorkContext context = new WorkContext();
        context.put(WorkflowConstants.CONTEXT_KEY_PROFILE, profile.getPhysicalProfileCode());
        context.put(WorkflowConstants.CONTEXT_KEY_TASKS, new WorkflowTasks());
        return context;
    }

    /**
     * 更新物理画像状态
     *
     * @param profile
     * @param status
     */
    private void updatePhysicalProfileStatus(PhysicalProfileInfoDTO profile, PhysicalProfileStatus status) {
        physicalProfileInfoService.updateStatus(profile.getId(), status);
    }

    /**
     * @param param
     * @return
     */
    private WorkFlow physicalProfileWorkflow(ExecuteActionParam<PhysicalProfileActionParam> param) {
        return profileFlowCreateService.createWorkFlow(param);
    }

}
