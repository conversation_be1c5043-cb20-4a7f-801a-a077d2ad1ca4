package com.fliggy.picasso.workflow.profilework.service.scene;

import com.fliggy.picasso.action.ExecuteActionParam;
import com.fliggy.picasso.action.PhysicalProfileActionParam;
import com.fliggy.picasso.common.enums.PhysicalProfileSceneEnum;
import org.jeasy.flows.workflow.ParallelFlow;
import org.jeasy.flows.workflow.WorkFlow;

import java.util.List;

/**
 * 物理画像场景服务
 *
 * <AUTHOR>
 * Created on 2022/8/18 上午11:23
 */
public interface PhysicalProfileSceneService {

    List<WorkFlow> buildSceneFlow(ExecuteActionParam<PhysicalProfileActionParam> param);

    PhysicalProfileSceneEnum sceneType();

}
