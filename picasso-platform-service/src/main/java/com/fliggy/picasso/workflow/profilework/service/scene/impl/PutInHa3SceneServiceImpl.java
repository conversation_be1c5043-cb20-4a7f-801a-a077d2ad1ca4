package com.fliggy.picasso.workflow.profilework.service.scene.impl;

import com.fliggy.picasso.action.*;
import com.fliggy.picasso.common.enums.PhysicalProfileSceneEnum;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;
import com.fliggy.picasso.workflow.ExecuteActionWork;
import com.fliggy.picasso.workflow.profilework.service.scene.PhysicalProfileSceneService;
import com.fliggy.picasso.workflow.profilework.factory.WorkFlowExecutorFactory;
import org.jeasy.flows.work.BlankFlowWork;
import org.jeasy.flows.work.WorkReportPredicate;
import org.jeasy.flows.workflow.WorkFlow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;

import static org.jeasy.flows.workflow.ConditionalFlow.Builder.aNewConditionalFlow;
import static org.jeasy.flows.workflow.ParallelFlow.Builder.aNewParallelFlow;

/**
 * <AUTHOR>
 * Created on 2022/8/18 上午11:26
 */
@Service
public class PutInHa3SceneServiceImpl implements PhysicalProfileSceneService {

    @Resource
    private final WorkFlowExecutorFactory workFlowExecutorFactory;

    @Autowired
    protected OdpsProfileHa3FormatAction odpsProfileHa3FormatAction;

    public PutInHa3SceneServiceImpl(WorkFlowExecutorFactory workFlowExecutorFactory) {
        this.workFlowExecutorFactory = workFlowExecutorFactory;
    }

    @Override
    public List<WorkFlow> buildSceneFlow(ExecuteActionParam<PhysicalProfileActionParam> param) {
        return Collections.singletonList(buildNextWork(param));
    }


    /**
     * 构建下个工作流
     *
     * @param param
     * @return
     */
    private WorkFlow buildNextWork(ExecuteActionParam<PhysicalProfileActionParam> param) {
        // 为了防止每个物料大宽表都去执行merge， 这里用标签最多的商品来做卡点，商品大宽表完成才会触发merge
        PhysicalProfileInfoDTO physicalProfileInfo = param.getData().getPhysicalProfileInfo();
        if (physicalProfileInfo.getPhysicalProfileCode().equals("galaxy_item")) {
            return mergeFlow(param);
        } else {
            return new BlankFlowWork();
        }
    }

    /**
     * 画像JSON串同步KV存储流程
     *
     * @param param
     * @return
     */
    private WorkFlow mergeFlow(ExecuteActionParam<PhysicalProfileActionParam> param) {
        return aNewConditionalFlow()
                .named("各个物料合并为一个大宽表")
                .execute(new ExecuteActionWork(odpsProfileHa3FormatAction, param))
                .when(WorkReportPredicate.COMPLETED)
                .then(aNewParallelFlow()
                        .named("Create Ha3Table & Ha3Table in parallel")
                        .execute(
                                new BlankFlowWork()
                        )
                        .with(workFlowExecutorFactory.getExecutorService())
                        .build())
                .build();
    }


    @Override
    public PhysicalProfileSceneEnum sceneType() {
        return PhysicalProfileSceneEnum.PUT_IN_HA3;
    }

}
