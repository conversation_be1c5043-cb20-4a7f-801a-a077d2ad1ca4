package com.fliggy.picasso.workflow.profilework.service.common;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.action.ExecuteActionParam;
import com.fliggy.picasso.action.OdpsProfileCreateAction;
import com.fliggy.picasso.action.OdpsProfileLoadAction;
import com.fliggy.picasso.action.PhysicalProfileActionParam;
import com.fliggy.picasso.common.domain.profile.PhysicalProfileSceneConfig;
import com.fliggy.picasso.common.enums.PhysicalProfileSceneEnum;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;
import com.fliggy.picasso.workflow.ExecuteActionWork;
import com.fliggy.picasso.workflow.profilework.factory.PhysicalProfileSceneFactory;
import com.fliggy.picasso.workflow.profilework.factory.WorkFlowExecutorFactory;
import com.taobao.ateye.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.flows.work.BlankFlowWork;
import org.jeasy.flows.work.NoOpWork;
import org.jeasy.flows.work.WorkReportPredicate;
import org.jeasy.flows.workflow.ConditionalFlow;
import org.jeasy.flows.workflow.ParallelFlow;
import org.jeasy.flows.workflow.WorkFlow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.jeasy.flows.workflow.ConditionalFlow.Builder.aNewConditionalFlow;
import static org.jeasy.flows.workflow.ParallelFlow.Builder.aNewParallelFlow;

/**
 * 画像flow创建服务
 *
 * <AUTHOR>
 * Created on 2022/7/22 下午3:54
 */
@Service
@Slf4j
public class ProfileFlowCreateService {

    @Autowired
    protected OdpsProfileCreateAction odpsProfileCreateAction;

    @Autowired
    protected OdpsProfileLoadAction odpsProfileLoadAction;

    @Resource
    private WorkFlowExecutorFactory workFlowExecutorFactory;

    /**
     * 创建画像宽表工作流
     *
     * @param param
     * @return
     */
    public WorkFlow createWorkFlow(ExecuteActionParam<PhysicalProfileActionParam> param) {
        PhysicalProfileActionParam physicalProfileActionParam = param.getData();
        PhysicalProfileInfoDTO profile = physicalProfileActionParam.getPhysicalProfileInfo();

        // 画像表创建
        ConditionalFlow.Builder.ThenStep createTableStep = buildCreateTableFlow(param, profile);

        // 画像表数据写入
        ConditionalFlow.Builder.ThenStep writeDataStep = buildWriteDataFlow(param, profile);

        // 画像表产出后 后置处理
        ParallelFlow postProcessFlow = postProcessFlow(param);
        ConditionalFlow conditionalFlow = createTableStep.then(writeDataStep.then(postProcessFlow).build()).build();
        log.info("createWorkFlow conditionalFlow  thread:{}, flow: {}", Thread.currentThread().getName(), JSON.toJSONString(conditionalFlow));
        return conditionalFlow;
    }


    /**
     * 构建往画像宽表写入数据的工作流
     *
     * @param param
     * @param profile
     * @return
     */
    private ConditionalFlow.Builder.ThenStep buildWriteDataFlow(ExecuteActionParam<PhysicalProfileActionParam> param, PhysicalProfileInfoDTO profile) {
        return aNewConditionalFlow()
                .named(String.format("PhysicalProfileWork profile[%s]", profile.getPhysicalProfileCode()))
                // 加载ODPS物理画像表数据
                .execute(new ExecuteActionWork(odpsProfileLoadAction, param))
                .when(WorkReportPredicate.COMPLETED);
    }


    /**
     * 构建创建画像宽表的工作流
     *
     * @param param
     * @param profile
     * @return
     */
    private ConditionalFlow.Builder.ThenStep buildCreateTableFlow(ExecuteActionParam<PhysicalProfileActionParam> param, PhysicalProfileInfoDTO profile) {
        return aNewConditionalFlow()
                .named(String.format("PhysicalProfileWork profile[%s]", profile.getPhysicalProfileCode()))
                // 创建ODPS物理画像表
                .execute(new ExecuteActionWork(odpsProfileCreateAction, param))
                .when(WorkReportPredicate.COMPLETED);
    }


    /**
     * 画像表产出完成后
     * 后置处理Flow
     *
     * @param param
     * @return
     */
    private ParallelFlow postProcessFlow(ExecuteActionParam<PhysicalProfileActionParam> param) {
        // 这里抽象一下， 按场景来创建flow
        PhysicalProfileInfoDTO physicalProfileInfo = param.getData().getPhysicalProfileInfo();
        List<PhysicalProfileSceneConfig> physicalProfileSceneConfigList = physicalProfileInfo.getPhysicalProfileSceneConfigList();
        if (CollectionUtils.isEmpty(physicalProfileSceneConfigList)) {
            return buildNoWorkFlow();
        }
        List<WorkFlow> workFlows = new ArrayList<>();
        // 遍历场景
        for (PhysicalProfileSceneConfig physicalProfileSceneConfig : physicalProfileSceneConfigList) {
            List<WorkFlow> workFlowsTemp = PhysicalProfileSceneFactory
                    .getProfileFlowCreateService(PhysicalProfileSceneEnum.getBySceneName(physicalProfileSceneConfig.getPhysicalProfileSceneName()))
                    .buildSceneFlow(param);
            if (CollectionUtils.isNotEmpty(workFlowsTemp)) {
                workFlows.addAll(workFlowsTemp);
            }
        }
        if (CollectionUtils.isEmpty(workFlows)) {
            workFlows.add(new BlankFlowWork());
        }
        return aNewParallelFlow()
                .named("odps profile sync in parallel")
                .execute(
                        workFlows.toArray(new WorkFlow[0])
                )
                .with(workFlowExecutorFactory.getExecutorService())
                .build();
    }


    private ParallelFlow buildNoWorkFlow() {
        return aNewParallelFlow()
                .named("odps profile sync in parallel")
                .execute(
                        new BlankFlowWork()
                )
                .with(workFlowExecutorFactory.getExecutorService())
                .build();
    }
}
