package com.fliggy.picasso.workflow.profilework.service.common;

import com.aliyun.odps.TableSchema;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.common.domain.label.LabelOdpsSourceConfig;
import com.fliggy.picasso.common.enums.ProfileCodeEnum;
import com.fliggy.picasso.common.enums.group.ProfileEnum;
import com.fliggy.picasso.common.odps.OdpsGuid;
import com.fliggy.picasso.common.odps.OdpsTableAliasUtils;
import com.fliggy.picasso.common.odps.domain.OdpsRegisterProfileTableDTO;
import com.fliggy.picasso.entity.odps.OdpsColumn;
import com.fliggy.picasso.entity.odps.OdpsMasterConfig;
import com.fliggy.picasso.entity.odps.OdpsProfileTableMeta;
import com.fliggy.picasso.entity.template.OdpsProfileCreateParam;
import com.fliggy.picasso.entity.template.OdpsProfileJsonLoadParam;
import com.fliggy.picasso.entity.template.OdpsProfileLoadParam;
import com.fliggy.picasso.entity.template.OdpsProfileUpdateParam;
import com.fliggy.picasso.entity.template.SqlSelectColumn;
import com.fliggy.picasso.freemarker.TemplateEnum;
import com.fliggy.picasso.freemarker.TemplateReader;
import com.fliggy.picasso.offline.OdpsColumnTypeHelper;
import com.fliggy.picasso.offline.OdpsMetaService;
import com.fliggy.picasso.offline.OdpsService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.odps.OdpsRegisterProfileTableService;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;
import com.fliggy.picasso.utils.EnvUtils;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * Created on 2022/7/22 下午4:34
 */
@Slf4j
public abstract class AbstractProfileTableService implements ProfileTableService {

    @Autowired
    protected OdpsService odpsService;

    @Autowired
    protected OdpsMetaService odpsMetaService;

    @Autowired
    protected TemplateReader templateReader;

    @Autowired
    protected LabelInfoService labelInfoService;

    @Autowired
    protected OdpsRegisterProfileTableService odpsRegisterProfileTableService;

    @Switch(description = "load新方式")
    public Boolean USE_ODPS_PROFILE_DATA_LOAD_NEW = false;

    @Override
    public Boolean checkDependentTableIsExists(PhysicalProfileInfoDTO physicalProfileInfo, List<LabelInfoDTO> labels) {
        try {

            // do check


        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Override
    public String generateProfileTableCreateSql(PhysicalProfileInfoDTO profile, List<LabelInfoDTO> labels) {
        Optional<TableSchema> tableSchema = odpsService.queryTableSchema(profile.getOdpsProject(),
                EnvUtils.isPre() ? profile.getOdpsTable() + "_test" : profile.getOdpsTable());

        String sql = null;
        if (!tableSchema.isPresent()) {
            Optional<OdpsProfileCreateParam> param = buildOdpsProfileCreateParam(profile, labels);
            if (param.isPresent()) {
                sql = templateReader.read(getOdpsProfileCreateTemplate(), param.get());
            }
        } else {
            Optional<OdpsProfileUpdateParam> param = buildOdpsProfileUpdateParam(tableSchema.get(),
                    profile,
                    labels);
            if (param.isPresent()) {
                sql = templateReader.read(getOdpsProfileUpdateTemplate(), param.get());
            }
        }
        return sql;
    }


    @Override
    public String generateProfileTableWriteDataSql(PhysicalProfileInfoDTO profile,
                                                   List<LabelInfoDTO> labels,
                                                   Map<OdpsGuid, List<LabelInfoDTO>> groupLabelMap,
                                                   Map<OdpsGuid, OdpsProfileTableMeta> metaMap) {

        if (groupLabelMap == null || groupLabelMap.isEmpty()) {
            return null;
        }

        // 物理画像表参数
        OdpsProfileLoadParam param = new OdpsProfileLoadParam();
        param.setProject(profile.getOdpsProject());
        param.setTable(EnvUtils.isPre() ? profile.getOdpsTable() + "_test" : profile.getOdpsTable());
        param.setPartitionKey(profile.getOdpsTablePtKey());
        param.setPartitionValue(DateTime.now().minusDays(1).toString(OdpsConstants.ODPS_PHYSICAL_PROFILE_PARTITION_FORMAT));

        // 主表参数
        OdpsMasterConfig masterConfig = profile.getOdpsMasterConfig();
        OdpsGuid masterOdpsGuid = new OdpsGuid(masterConfig.getProject(), masterConfig.getTable());
        OdpsProfileTableMeta masterTableMeta = metaMap.get(masterOdpsGuid);
        List<LabelInfoDTO> masterTableLabels = groupLabelMap.getOrDefault(masterOdpsGuid, Lists.newArrayList());
        Optional<OdpsProfileLoadParam.OdpsProfileSelectParam> masterOpt = buildOdpsProfileSelectParam(masterOdpsGuid, masterTableMeta, masterTableLabels);

        if (!masterOpt.isPresent()) {
            String msg = "Build Master OdpsProfileSelectParam Error! Master's OdpsProfileSelectParam Is Empty!";
            log.error(msg);
            throw new RuntimeException(msg);
        }
        param.setMasterTable(masterOpt.get());

        // 生成其他关联表的SELECT参数
        groupLabelMap.remove(masterOdpsGuid);
        List<OdpsProfileLoadParam.OdpsProfileSelectParam> associatedProfileSelectParams = Lists.newArrayList();
        groupLabelMap.forEach(
                (associatedOdpsGuid, associatedTableLabels) -> {
                    try {
                        OdpsProfileTableMeta associatedTableMeta = metaMap.get(associatedOdpsGuid);
                        Optional<OdpsProfileLoadParam.OdpsProfileSelectParam> associatedSelectParamOpt = buildOdpsProfileSelectParam(
                                associatedOdpsGuid,
                                associatedTableMeta,
                                associatedTableLabels);

                        if (associatedSelectParamOpt.isPresent()) {
                            associatedProfileSelectParams.add(associatedSelectParamOpt.get());
                        } else {
                            log.error("AssociatedTableSelectParam Is Empty! SKIP! Guid: " + associatedOdpsGuid);
                        }
                    } catch (Exception e) {
                        log.error("Build AssociatedTableSelectParam Error!  SKIP! Guid:" + associatedOdpsGuid, e);
                    }
                }
        );
        param.setAssociatedTables(associatedProfileSelectParams);

        List<String> selectColumns = buildOdpsGlobleSelectParam(profile, labels);
        param.setSelectedColumns(selectColumns);
        return templateReader.read(getProfileWriteTemplate(), param);
    }

    @Override
    public String generateOdpsProfileJsonSql(PhysicalProfileInfoDTO profile, List<LabelInfoDTO> labels) {

        OdpsProfileJsonLoadParam param = new OdpsProfileJsonLoadParam();
        String jsonTableName = EnvUtils.isPre() ? OdpsConstants.ODPS_TABLE_PROFILE_JSON + "_test" : OdpsConstants.ODPS_TABLE_PROFILE_JSON;
        param.setJsonTable(OdpsConstants.ODPS_EXECUTE_PROJECT + OdpsConstants.DOT + jsonTableName);

        String physicalProfileCode = profile.getPhysicalProfileCode();
        param.setPhysicalProfileCode(physicalProfileCode);
        // 普通人群和重点人群画像，前缀应该保持一致。当是重点用户画像时，取普通用户画像code
        param.setTairKeyPrefix(Objects.equals(ProfileEnum.TAOBAO_CORE.getCode(), physicalProfileCode) ? ProfileEnum.TAOBAO_USER.getCode() : physicalProfileCode);

        String profileTableName = EnvUtils.isPre() ? profile.getOdpsTable() + "_test" : profile.getOdpsTable();
        param.setPhysicalProfileTable(profile.getOdpsProject() + OdpsConstants.DOT + profileTableName);
        param.setDs(
                DateTime.now().minusDays(1).toString(OdpsConstants.ODPS_PHYSICAL_PROFILE_PARTITION_FORMAT)
        );
        param.setPrimaryKey(profile.getPrimaryKey());

        // 拼接resolve_data_as_json_filter_null udf的参数
        List<String> labelCodes = labels
                .stream()
                .map(label -> String.format("'%s', %s", label.getCode(), label.getCode()))
                .collect(Collectors.toList());
        param.setColumns(labelCodes);

        String sql = templateReader.read(TemplateEnum.ODPS_PROFILE_JSON_LOAD, param);

        return sql;
    }

    protected abstract TemplateEnum getProfileWriteTemplate();

    protected abstract List<String> buildOdpsGlobleSelectParam(PhysicalProfileInfoDTO profile, List<LabelInfoDTO> labels);



    protected abstract TemplateEnum getOdpsProfileUpdateTemplate();

    protected abstract TemplateEnum getOdpsProfileCreateTemplate();


    /**
     * 构建物理表创建参数
     *
     * @param profile 物理画像
     * @param labels  标签
     * @return 物理表创建参数
     */
    protected abstract Optional<OdpsProfileCreateParam> buildOdpsProfileCreateParam(PhysicalProfileInfoDTO profile, List<LabelInfoDTO> labels);


    /**
     * 构建物理画像表更新SQL
     *
     * @param tableSchema ODPS画像表元信息
     * @param profile     物理画像
     * @param labels      物理画像包含标签
     * @return ODPS物理画像表更新SQL
     */
    protected abstract Optional<OdpsProfileUpdateParam> buildOdpsProfileUpdateParam(TableSchema tableSchema, PhysicalProfileInfoDTO profile, List<LabelInfoDTO> labels);


    /**
     * 实体code
     *
     * @return
     */
    public abstract ProfileCodeEnum type();



    /**
     * 根据ODPS原表GUID、表元数据信息、读取列构造Odps表Select参数
     *
     * @param odpsGuid             ODPS表GUID
     * @param odpsProfileTableMeta 表元数据信息
     * @param labelInfoDTOS        读取列
     * @return OdpsProfileSelectParam
     */
    protected Optional<OdpsProfileLoadParam.OdpsProfileSelectParam> buildOdpsProfileSelectParam(OdpsGuid odpsGuid,
                                                                                                OdpsProfileTableMeta odpsProfileTableMeta,
                                                                                                List<LabelInfoDTO> labelInfoDTOS) {

        OdpsProfileLoadParam.OdpsProfileSelectParam selectParam = new OdpsProfileLoadParam.OdpsProfileSelectParam();

        try {
            selectParam.setProject(odpsGuid.getProject());
            selectParam.setTable(odpsGuid.getTable());
            selectParam.setCondition(odpsGuid.getCondition());

            OdpsRegisterProfileTableDTO odpsRegisterTable = odpsRegisterProfileTableService.find(odpsGuid);
            if (odpsRegisterTable == null) {
                String msg = String.format("Odps Table Not Register! Please Add, OdpsGuid:%s", odpsGuid);
                throw new RuntimeException(msg);
            }
            selectParam.setPartitionKey(odpsRegisterTable.getPartitionKey());
            selectParam.setPartitionValue(odpsProfileTableMeta.getMaxPt());
            selectParam.setPrimaryKey(odpsRegisterTable.getPrimaryKey());
            selectParam.setAlias(OdpsTableAliasUtils.alias(odpsGuid.getProject(), odpsGuid.getTable()));

            List<SqlSelectColumn> selectColumns = Lists.newArrayList();
            // 添加主键列
            selectColumns.add(
                    new SqlSelectColumn(getTrimSql(odpsRegisterTable.getPrimaryKey()), odpsRegisterTable.getPrimaryKey()));

            // 添加其他列
            List<SqlSelectColumn> otherColumns = labelInfoDTOS.stream()
                    .map(labelInfoDTO -> {
                        String sql = handleTypeMapping(labelInfoDTO);
                        String aliasName = labelInfoDTO.getCode();
                        return new SqlSelectColumn(sql, aliasName);
                    })
                    .collect(Collectors.toList());
            selectColumns.addAll(otherColumns);
            selectParam.setColumns(Lists.newArrayList(selectColumns));
            return Optional.of(selectParam);
        } catch (Exception e) {
            log.error("BuildOdpsProfileSelectParam Error! Return Empty()", e);
            return Optional.empty();
        }
    }

    private String getTrimSql(String sql) {
        return USE_ODPS_PROFILE_DATA_LOAD_NEW ? String.format("trim(%s)", sql) : sql;
    }

    /**
     * 处理原表字段/类型兼容问题，标签类型协议加工（如多值/KV）: 1. OdpsSourceConfig.Type兼容问题 2. 标签类型为多值/JSON的需要做协议上的转换
     *
     * @param labelInfoDTO
     * @return
     */
    private String handleTypeMapping(LabelInfoDTO labelInfoDTO) {

        LabelOdpsSourceConfig odpsSourceConfig = labelInfoDTO.getOdpsSourceConfig();
        // 兼容不配置odps.field时默认与标签Code同名
        String oriName = odpsSourceConfig.getField() == null ? labelInfoDTO.getCode()
                : odpsSourceConfig.getField();
        String type = odpsSourceConfig.getType();
        String castField = OdpsColumnTypeHelper.castSql(oriName, type);

        // 标签类型为多值/JSON的需要做存储协议上的转换
        String convertSql = OdpsColumnTypeHelper.convertSql(
                maxSql(castField),
                OdpsColumn.OdpsColumnType.fromTypeStr(type),
                labelInfoDTO.getDataType(),
                labelInfoDTO.getSubDataType());

        return convertSql;
    }

    private String maxSql(String sql) {
        return USE_ODPS_PROFILE_DATA_LOAD_NEW ? String.format("max(%s)", sql) : sql;
    }

}
