package com.fliggy.picasso.workflow.profilework.service.scene.impl;

import com.fliggy.picasso.action.ExecuteActionParam;
import com.fliggy.picasso.action.OdpsProfileJson2TairAction;
import com.fliggy.picasso.action.OdpsProfileJsonAction;
import com.fliggy.picasso.action.PhysicalProfileActionParam;
import com.fliggy.picasso.common.enums.PhysicalProfileSceneEnum;
import com.fliggy.picasso.workflow.ExecuteActionWork;
import com.fliggy.picasso.workflow.profilework.service.scene.PhysicalProfileSceneService;
import com.fliggy.picasso.workflow.profilework.factory.WorkFlowExecutorFactory;
import org.jeasy.flows.work.WorkReportPredicate;
import org.jeasy.flows.workflow.ParallelFlow;
import org.jeasy.flows.workflow.WorkFlow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.jeasy.flows.workflow.ConditionalFlow.Builder.aNewConditionalFlow;
import static org.jeasy.flows.workflow.ParallelFlow.Builder.aNewParallelFlow;

/**
 * <AUTHOR>
 * Created on 2022/8/18 上午11:26
 */
@Service
public class PutInTairSceneServiceImpl implements PhysicalProfileSceneService {

    @Resource
    private WorkFlowExecutorFactory workFlowExecutorFactory;

    @Autowired
    protected OdpsProfileJson2TairAction odpsProfileJson2TairAction;

    @Autowired
    protected OdpsProfileJsonAction odpsProfileJsonAction;

    @Override
    public List<WorkFlow> buildSceneFlow(ExecuteActionParam<PhysicalProfileActionParam> param) {
        List<WorkFlow> parallelFlowList = new ArrayList<>();
        parallelFlowList.add(profileJsonSequentialFlow(param));
        return parallelFlowList;
    }

    @Override
    public PhysicalProfileSceneEnum sceneType() {
        return PhysicalProfileSceneEnum.PUT_IN_TAIR;
    }


    /**
     * 画像JSON串同步KV存储流程
     *
     * @param param
     * @return
     */
    private WorkFlow profileJsonSequentialFlow(ExecuteActionParam<PhysicalProfileActionParam> param) {
        return aNewConditionalFlow()
                .named("Sync odps profile to lindorm/ldb")
                .execute(new ExecuteActionWork(odpsProfileJsonAction, param))
                .when(WorkReportPredicate.COMPLETED)
                .then(aNewParallelFlow()
                        .named("Create HoloForeignTable & HoloTable in parallel")
                        .execute(
                                new ExecuteActionWork(odpsProfileJson2TairAction, param)
                        )
                        .with(workFlowExecutorFactory.getExecutorService())
                        .build())
                .build();
    }
}
