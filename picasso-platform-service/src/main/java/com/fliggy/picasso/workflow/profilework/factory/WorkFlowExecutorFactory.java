package com.fliggy.picasso.workflow.profilework.factory;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * Created on 2022/8/18 上午11:31
 */
@Component
public class WorkFlowExecutorFactory {

    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        this.executorService = new ThreadPoolExecutor(
                5
                , 20
                , 5
                , TimeUnit.SECONDS
                , new LinkedBlockingQueue<>(200)
                , new ThreadFactoryBuilder().setNameFormat("PhysicalProfile-Workflow-Pool-%d").build()
                , new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public ExecutorService getExecutorService(){
        return this.executorService;
    }
}
