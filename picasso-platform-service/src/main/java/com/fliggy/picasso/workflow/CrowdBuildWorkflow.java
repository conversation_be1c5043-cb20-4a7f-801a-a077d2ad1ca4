package com.fliggy.picasso.workflow;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/4/9
 */
@Component
public class CrowdBuildWorkflow {

    //private static final Logger log = LoggerFactory.getLogger(CrowdBuildWorkflow.class);
    //
    //private final CrowdService crowdService;
    //private final EventUtils eventUtils;
    //private final ApplicationEventPublisher eventPublisher;
    //private final CrowdBuildProcessRouter<CrowdBuildResult, AbstractCrowdBuildContext> crowdBuildProcessRouter;
    //
    //
    //@Autowired
    //public CrowdBuildWorkflow(CrowdService crowdService,
    //                          EventUtils eventUtils,
    //                          ApplicationEventPublisher eventPublisher,
    //                          CrowdBuildProcessRouter<CrowdBuildResult, AbstractCrowdBuildContext> crowdBuildProcessRouter) {
    //    this.crowdService = crowdService;
    //    this.eventUtils = eventUtils;
    //    this.eventPublisher = eventPublisher;
    //    this.crowdBuildProcessRouter = crowdBuildProcessRouter;
    //}
    //
    //private ExecutorService executorService;
    //
    //@PostConstruct
    //private void init() {
    //    this.executorService = new ThreadPoolExecutor(4, 100,
    //            60L, TimeUnit.SECONDS,
    //            new SynchronousQueue<>(),
    //            new ThreadFactoryBuilder().setNameFormat("crowd-build-Workflow-Pool-").build()
    //    );
    //}

    //public WorkReport run(ExecuteActionParam<AbstractCrowdBuildContext> executeActionParam) {
    //    long buildStart = System.currentTimeMillis();
    //    WorkFlow workFlow = crowdBuildWorkflow(executeActionParam);
    //    WorkReport workReport = aNewWorkFlowEngine().build().run(workFlow, initContext());
    //    CrowdMetaInfoDO crowdMetaInfoDO = executeActionParam.getData().getCrowdMetaInfoDO();
    //
    //    finishCrowdBuild(crowdMetaInfoDO);
    //    long buildCost = (System.currentTimeMillis() - buildStart) / 1000;
    //    log.info("|crowd_build|all|{}|{}|{}|{}|", crowdMetaInfoDO.getId(), crowdMetaInfoDO.getCrowdStatus(), crowdMetaInfoDO.getCrowdType(), buildCost);
    //    return workReport;
    //}

    //private void finishCrowdBuild(CrowdMetaInfoDO crowdMetaInfoDO) {
    //    if (crowdMetaInfoDO == null) {
    //        return;
    //    }
    //
    //    CrowdStatusEnum crowdStatus = crowdMetaInfoDO.getCrowdStatus();
    //    for (Map.Entry<String, CrowdBuildProgressDO> entry : crowdMetaInfoDO.getProgress().entrySet()) {
    //        CrowdStatusEnum sceneStatus = CrowdStatusEnum.fromName(entry.getValue().getStatus());
    //        if (sceneStatus == CrowdStatusEnum.ERROR) {
    //            crowdStatus = CrowdStatusEnum.ERROR;
    //        }
    //    }
    //
    //    if (crowdStatus != CrowdStatusEnum.ERROR) {
    //        crowdStatus = CrowdStatusEnum.SUCCESS;
    //    }
    //
    //    crowdMetaInfoDO.setCrowdStatus(crowdStatus);
    //    String eventMsg;
    //    if (crowdMetaInfoDO.getCrowdStatus() == CrowdStatusEnum.SUCCESS) {
    //        eventMsg = "人群创建成功！";
    //        crowdMetaInfoDO.setErrorCode(CrowdBuildErrorEnum.SUCCESS.getCode());
    //        crowdMetaInfoDO.setBizDate(DateTime.now().minusDays(1).toString(YYYYMMDD));
    //    } else {
    //        eventMsg = "人群创建失败！";
    //    }
    //
    //    CrowdBuildEvent event = new CrowdBuildEvent.Builder()
    //            .crowdId(crowdMetaInfoDO.getId())
    //            .crowdName(crowdMetaInfoDO.getCrowdName())
    //            .crowdType(crowdMetaInfoDO.getCrowdType())
    //            .crowdStatus(crowdMetaInfoDO.getCrowdStatus())
    //            .crowdAmount(crowdMetaInfoDO.getCrowdAmount())
    //            .eventMsg(eventMsg)
    //            .host(ContextUtils.getHostName())
    //            .receivers(eventUtils.buildReceivers(crowdMetaInfoDO))
    //            .build();
    //
    //    eventPublisher.publishEvent(event);
    //
    //    crowdMetaInfoDO.putCrowdBuildStepTs(CrowdBuildStepEnum.BUILD_FINISH_TIME.name(), System.currentTimeMillis());
    //    crowdService.updateAcrossNonFront(crowdMetaInfoDO);
    //}

    //private WorkContext initContext() {
    //    WorkContext context = new WorkContext();
    //    context.put(WorkflowConstants.CONTEXT_KEY_PROFILE, "人群ID");
    //    context.put(WorkflowConstants.CONTEXT_KEY_TASKS, new WorkflowTasks());
    //    return context;
    //}

    //private WorkFlow crowdBuildWorkflow(ExecuteActionParam<AbstractCrowdBuildContext> executeActionParam) {
    //    CrowdTaskTypeEnum crowdTaskType = executeActionParam.getData().getCrowdTaskType();
    //    CrowdBuildProcess<CrowdBuildResult, AbstractCrowdBuildContext> crowdBuildProcess = crowdBuildProcessRouter.choose(crowdTaskType);
    //    return aNewSequentialFlow().named("人群构建")
    //                .execute(new ExecuteActionWork(crowdBuildProcess.getCrowdBuildCountAction(), executeActionParam))
    //                .then(new ExecuteActionWork(crowdBuildProcess.getCrowdBuildDumpOssAction(), executeActionParam))
    //                .then(aNewParallelFlow().named("人群使用场景子任务")
    //                        .execute(generateCrowdApplySceneExecuteAction(executeActionParam))
    //                        .with(executorService)
    //                        .build())
    //                .build();
    //}

    //private ExecuteActionWork[] generateCrowdApplySceneExecuteAction(ExecuteActionParam<AbstractCrowdBuildContext> executeActionParam) {
    //    if (executeActionParam == null) {
    //        throw new ParamErrorException("生成人群使用场景执行任务异常：executeActionParam为空！");
    //    }
    //
    //    AbstractCrowdBuildContext crowdBuildContext = executeActionParam.getData();
    //    if (crowdBuildContext == null) {
    //        throw new ParamErrorException("生成人群使用场景执行任务异常：crowdBuildContext为空！");
    //    }
    //
    //    List<String> crowdApplyScenes = crowdBuildContext.getCrowdMetaInfoDO().getCrowdApplyScene();
    //    if (CollectionUtils.isNullOrEmpty(crowdApplyScenes)) {
    //        return new ExecuteActionWork[0];
    //    }
    //
    //    ExecuteActionWork[] result = new ExecuteActionWork[crowdApplyScenes.size()];
    //    CrowdTaskTypeEnum crowdTaskType = executeActionParam.getData().getCrowdTaskType();
    //    CrowdBuildProcess crowdBuildProcess = crowdBuildProcessRouter.choose(crowdTaskType);
    //    int index = 0;
    //    for (String applyScene : crowdApplyScenes) {
    //        CrowdApplySceneEnum crowdApplyScene = CrowdApplySceneEnum.fromName(applyScene);
    //        switch (crowdApplyScene) {
    //            case ANALYSIS:
    //                result[index++] = (new ExecuteActionWork(crowdBuildProcess.getCrowdBuildAnalysisSceneAction(), executeActionParam));
    //                break;
    //            case PUSH:
    //                result[index++] = (new ExecuteActionWork(crowdBuildProcess.getCrowdBuildPushSceneAction(), executeActionParam));
    //                break;
    //            case PAGE:
    //                result[index++] = (new ExecuteActionWork(crowdBuildProcess.getCrowdBuildPageSceneAction(), executeActionParam));
    //                break;
    //            case MATCH:
    //                result[index++] = (new ExecuteActionWork(crowdBuildProcess.getCrowdBuildMatchSceneAction(), executeActionParam));
    //                break;
    //            default:
    //                throw new ParamErrorException(String.format("生成人群使用场景执行任务异常：暂不支持该人群使用场景[%s]", crowdApplyScene));
    //        }
    //    }
    //
    //    return result;
    //}
}
