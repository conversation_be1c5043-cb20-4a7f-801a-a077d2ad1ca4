package com.fliggy.picasso.workflow;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.action.ExecuteAction;
import com.fliggy.picasso.action.ExecuteActionParam;
import com.fliggy.picasso.action.ExecuteActionResult;
import com.fliggy.picasso.action.ExecuteActionResult.ExecuteActionResultCode;
import com.fliggy.picasso.action.OdpsProfileHa3FormatAction;
import com.fliggy.picasso.alarm.dingcard.DingTalkEventTypeEnum;
import com.fliggy.picasso.alarm.dingcard.DingTalkNotifyManager;
import com.fliggy.picasso.alarm.dingcard.DingTalkReq;
import com.fliggy.picasso.workflow.WorkflowTasks.WorkflowTask;
import org.jeasy.flows.work.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.fliggy.picasso.action.EnumDataLoadAction.NO_PARTITION_DATA_ERROR_MSG;
import static com.fliggy.picasso.common.Constant.TASK_LOG;

/**
 * <AUTHOR>
 * @date 2021/2/22 下午7:35
 */
public class ExecuteActionWork<T, K> implements Work {

    private static final Logger log = LoggerFactory.getLogger(TASK_LOG);

    private final ExecuteAction<T, K> action;
    private final ExecuteActionParam<K> param;

    public ExecuteActionWork(ExecuteAction<T, K> action,
                             ExecuteActionParam<K> param) {
        this.action = action;
        this.param = param;
    }

    @Override
    public WorkReport execute(WorkContext workContext) {
        try {
            ExecuteActionResult<T, K> result = action.execute(param);
            WorkflowTasks workflowTasks = (WorkflowTasks) workContext.get(WorkflowConstants.CONTEXT_KEY_TASKS);
            if (ExecuteActionResultCode.SUCCESS == result.getCode()
                    || ExecuteActionResultCode.SKIP == result.getCode()) {
                workflowTasks.add(new WorkflowTask(action.getActionType().getName(), true, null));
                log.info("action execute success. action:{}", action.getActionType().getName());
                return new DefaultWorkReport(WorkStatus.COMPLETED, workContext);
            } else {
                workflowTasks.add(new WorkflowTask(action.getActionType().getName(), false, result.getMsg()));
                log.error("action execute failed. action:{}", action.getActionType().getName() + ", errMsg: " + JSON.toJSONString(result));

                if (!NO_PARTITION_DATA_ERROR_MSG.equals(result.getMsg())) {
                    // 源表为空跳过，不发通知
                    sendAlarm(String.format("Action执行错误!  \n  profile:%s  \n actionName:%s  \n  errMsg: %s"
                            , workContext.getOrDefault(WorkflowConstants.CONTEXT_KEY_PROFILE, "unknown")
                            , action.getActionType().getName(), JSON.toJSONString(result)));
                }
                return new DefaultWorkReport(WorkStatus.FAILED, workContext);
            }
        } catch (Exception e) {
            log.error("action execute failed. action:{}", action.getActionType().getName(), e);
            sendAlarm(String.format("Action执行异常!  \n  profile:%s  \n actionName:%s  \n  errMsg: %s"
                    , workContext.getOrDefault(WorkflowConstants.CONTEXT_KEY_PROFILE, "unknown")
                    , action.getActionType().getName(), e.getMessage()));
            return new DefaultWorkReport(WorkStatus.FAILED, workContext);
        }
    }

    private void sendAlarm(String alarmMsg) {
        DingTalkReq dingTalkReq = DingTalkReq.builder().empId(OdpsProfileHa3FormatAction.ZHUGE_DOMAIN_ADMIN_LIST)
                .msg(alarmMsg).build();
        DingTalkNotifyManager.sendDingMsg(dingTalkReq, DingTalkEventTypeEnum.ODPS_ALARM);
    }
}
