package com.fliggy.picasso.workflow;

import com.fliggy.picasso.action.EnumDataLoadAction;
import com.fliggy.picasso.action.EnumDataSyncAction;
import com.fliggy.picasso.action.ExecuteActionParam;
import com.fliggy.picasso.entity.enumvalue.LabelEnumDataRefluxTask;
import org.jeasy.flows.work.WorkContext;
import org.jeasy.flows.work.WorkReport;
import org.jeasy.flows.work.WorkReportPredicate;
import org.jeasy.flows.workflow.WorkFlow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static org.jeasy.flows.engine.WorkFlowEngineBuilder.aNewWorkFlowEngine;
import static org.jeasy.flows.workflow.ConditionalFlow.Builder.aNewConditionalFlow;

/**
 * <AUTHOR>
 * @date 2021/2/20 下午4:36
 */
@Component
public class LabelEnumDataWorkflow {

    /**
     * @param param
     * @return
     */
    public WorkReport run(ExecuteActionParam<LabelEnumDataRefluxTask> param) {

        WorkReport workReport = null;
        try {
            WorkFlow workFlow = newLabelEnumDataWorkflow(param);

            // 初始化上下文
            WorkContext context = initContext(param.getData());

            // 执行工作流
            workReport = aNewWorkFlowEngine()
                    .build()
                    .run(workFlow, context);

        } catch (Exception e) {

        }

        return workReport;
    }

    /**
     * 初始化上下文
     *
     * @param task
     * @return
     */
    private WorkContext initContext(LabelEnumDataRefluxTask task) {
        WorkContext context = new WorkContext();
        context.put(WorkflowConstants.CONTEXT_KEY_LABEL, task.getLabelCode());
        context.put(WorkflowConstants.CONTEXT_KEY_ENUM_META_ID, task.getEnumMetaInfoId());
        context.put(WorkflowConstants.CONTEXT_KEY_TASKS, new WorkflowTasks());
        return context;
    }

    @Autowired
    private EnumDataLoadAction enumDataLoadAction;

    @Autowired
    private EnumDataSyncAction enumDataSyncAction;

    /**
     * 画像JSON串同步KV存储流程
     *
     * @param param
     * @return
     */
    private WorkFlow newLabelEnumDataWorkflow(ExecuteActionParam<LabelEnumDataRefluxTask> param) {
        return aNewConditionalFlow()
                .named("Reflux LabelEnumData To Idb")
                .execute(new ExecuteActionWork(enumDataLoadAction, param))
                .when(WorkReportPredicate.COMPLETED)
                .then(new ExecuteActionWork(enumDataSyncAction, param))
                .build();
    }
}
