package com.fliggy.picasso.workflow.profilework.factory;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.common.enums.ProfileCodeEnum;
import com.fliggy.picasso.utils.PicassoSpringContextUtils;
import com.fliggy.picasso.workflow.profilework.service.common.ProfileTableService;
import org.springframework.beans.BeansException;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2022/7/25 下午3:54
 */

@Component
@DependsOn("picassoSpringContextUtil")
public class ProfileTableServiceFactory {


    private static Map<ProfileCodeEnum, ProfileTableService> profileTableServiceMap;

    @PostConstruct
    public void init() throws BeansException {
        Map<String, ProfileTableService> map = PicassoSpringContextUtils.getApplicationContext().getBeansOfType(ProfileTableService.class);
        profileTableServiceMap = new HashMap<>();
        map.forEach((key, value) ->{
            if (profileTableServiceMap.containsKey(value.type())){
                throw new IllegalArgumentException("ProfileTableService.type 类型有重复:" + JSON.toJSONString(value), null);
            }
            profileTableServiceMap.put(value.type(), value);
        });
    }

    public static ProfileTableService getProfileTableService(ProfileCodeEnum type) {
        if (profileTableServiceMap.containsKey(type)) {
            return profileTableServiceMap.get(type);
        }
        throw new IllegalArgumentException("ProfileTableService.type不存在该类型." + type.toString());
    }
}
