package com.fliggy.picasso.workflow;

import java.util.List;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/2/19 下午7:42
 */
public class WorkflowTasks {

    public List<WorkflowTask> list = Lists.newArrayList();

    public void add(WorkflowTask task) {
        list.add(task);
    }

    public List<WorkflowTask> getTasks() {
        return this.list;
    }

    @Data
    @AllArgsConstructor
    public static class WorkflowTask {
        private String name;
        private boolean success;
        private String msg;

    }
}
