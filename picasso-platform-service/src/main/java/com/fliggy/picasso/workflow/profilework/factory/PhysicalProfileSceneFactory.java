package com.fliggy.picasso.workflow.profilework.factory;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.common.enums.PhysicalProfileSceneEnum;
import com.fliggy.picasso.exception.AnalysisException;
import com.fliggy.picasso.utils.PicassoSpringContextUtils;
import com.fliggy.picasso.workflow.profilework.service.scene.PhysicalProfileSceneService;
import org.springframework.beans.BeansException;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2022/8/18 下午2:13
 */
@Component
@DependsOn("picassoSpringContextUtil")
public class PhysicalProfileSceneFactory {

    private static Map<PhysicalProfileSceneEnum, PhysicalProfileSceneService> physicalProfileSceneServiceMap;

    @PostConstruct
    public void init() throws BeansException {
        Map<String, PhysicalProfileSceneService> map = PicassoSpringContextUtils.getApplicationContext().getBeansOfType(PhysicalProfileSceneService.class);
        physicalProfileSceneServiceMap = new HashMap<>();
        map.forEach((key, value) -> {
            if (physicalProfileSceneServiceMap.containsKey(value.sceneType())) {
                throw new AnalysisException("PhysicalProfileSceneService 类型有重复:" + JSON.toJSONString(value), null);
            }
            physicalProfileSceneServiceMap.put(value.sceneType(), value);
        });
    }

    public static PhysicalProfileSceneService getProfileFlowCreateService(PhysicalProfileSceneEnum sceneEnum) {
        if (physicalProfileSceneServiceMap.containsKey(sceneEnum)) {
            return physicalProfileSceneServiceMap.get(sceneEnum);
        }
        throw new IllegalArgumentException("PhysicalProfileSceneService.type不存在该类型." + sceneEnum.getSceneName());
    }
}
