package com.fliggy.picasso.workflow.profilework.service.common;

import com.fliggy.picasso.common.enums.ProfileCodeEnum;
import com.fliggy.picasso.common.odps.OdpsGuid;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.entity.odps.OdpsProfileTableMeta;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;

import java.util.List;
import java.util.Map;

/**
 * 画像表服务
 * <AUTHOR>
 * Created on 2022/7/22 下午4:32
 */
public interface ProfileTableService {


    /**
     * 检查画像表数据依赖表的分区是否存在
     * @param physicalProfileInfo
     * @param labels
     * @return
     */
    Boolean checkDependentTableIsExists(PhysicalProfileInfoDTO physicalProfileInfo, List<LabelInfoDTO> labels);


    /**
     * 生成sql: 创建画像表
     * @param physicalProfileInfo
     * @param labels
     * @return
     */
    String generateProfileTableCreateSql(PhysicalProfileInfoDTO physicalProfileInfo, List<LabelInfoDTO> labels);


    /**
     * 生成sql: 画像表数据写入
     * @param profile       待生成SQL的物理画像
     * @param groupLabelMap 按所属原ODPS表GUID分组的标签集合
     * @param labels        排序的标签集合用于生成Select顺序
     * @param metaMap       标签所属原ODPS表GUID的元数据信息
     * @return 物理画像的数据加载SQL
     */
    String generateProfileTableWriteDataSql(PhysicalProfileInfoDTO profile, List<LabelInfoDTO> labels, Map<OdpsGuid, List<LabelInfoDTO>> groupLabelMap, Map<OdpsGuid, OdpsProfileTableMeta> metaMap);

    /**
     * 生成sql: 画像表数据写入
     * @param profile
     * @param labels
     * @return
     */
    String generateOdpsProfileJsonSql(PhysicalProfileInfoDTO profile, List<LabelInfoDTO> labels);

    ProfileCodeEnum type();

}
