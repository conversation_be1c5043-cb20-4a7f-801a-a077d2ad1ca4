package com.fliggy.picasso.workflow.profilework.service.common.impl;

import com.aliyun.odps.Column;
import com.aliyun.odps.TableSchema;
import com.fliggy.picasso.common.Constant;
import com.fliggy.picasso.common.enums.ProfileCodeEnum;
import com.fliggy.picasso.common.odps.OdpsTableAliasUtils;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.entity.odps.OdpsColumn;
import com.fliggy.picasso.entity.odps.OdpsMasterConfig;
import com.fliggy.picasso.entity.template.OdpsProfileCreateParam;
import com.fliggy.picasso.entity.template.OdpsProfileUpdateParam;
import com.fliggy.picasso.freemarker.TemplateEnum;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;
import com.fliggy.picasso.workflow.profilework.service.common.AbstractProfileTableService;
import com.fliggy.picasso.utils.EnvUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户画像表sql构建服务
 * <AUTHOR>
 * Created on 2022/7/22 下午4:00
 */
@Service
public class UserProfileTableSqlService extends AbstractProfileTableService {

    @Override
    public ProfileCodeEnum type() {
        return ProfileCodeEnum.USER;
    }

    @Override
    protected TemplateEnum getOdpsProfileCreateTemplate() {
        return TemplateEnum.ODPS_PROFILE_CREATE;
    }

    @Override
    protected TemplateEnum getOdpsProfileUpdateTemplate() {
        return TemplateEnum.ODPS_PROFILE_UPDATE;
    }

    @Override
    protected TemplateEnum getProfileWriteTemplate() {
        if (USE_ODPS_PROFILE_DATA_LOAD_NEW) {
            return TemplateEnum.ODPS_PROFILE_DATA_LOAD_NEW;
        }else {
            return TemplateEnum.ODPS_PROFILE_DATA_LOAD;
        }
    }

    @Override
    protected Optional<OdpsProfileUpdateParam> buildOdpsProfileUpdateParam(TableSchema tableSchema, PhysicalProfileInfoDTO profile, List<LabelInfoDTO> labels) {
        if (profile == null || CollectionUtils.isEmpty(labels)) {
            return Optional.empty();
        }

        List<Column> columns = tableSchema.getColumns();
        if (CollectionUtils.isEmpty(labels)) {
            return Optional.empty();
        }

        List<String> columnNames = columns.stream()
                .map(Column::getName)
                .collect(Collectors.toList());

        OdpsProfileUpdateParam param = new OdpsProfileUpdateParam();
        param.setProjectName(profile.getOdpsProject());
        param.setTableName(EnvUtils.isPre() ? profile.getOdpsTable() + "_test" : profile.getOdpsTable());

        // 不包括日期分区列
        List<LabelInfoDTO> needAddLabel = Lists.newArrayList();
        labels.forEach(
                labelInfoDTO -> {
                    if (!columnNames.contains(labelInfoDTO.getCode())) {
                        needAddLabel.add(labelInfoDTO);
                    }
                }
        );

        if (CollectionUtils.isEmpty(needAddLabel)) {
            return Optional.empty();
        }

        List<OdpsColumn> addColumns = needAddLabel.stream()
                .map(label -> new OdpsColumn(label.getCode(), toOdpsColumnType(label), label.getName()))
                .collect(Collectors.toList());
        param.setAddColumns(addColumns);

        return Optional.of(param);
    }

    @Override
    protected Optional<OdpsProfileCreateParam> buildOdpsProfileCreateParam(PhysicalProfileInfoDTO profile, List<LabelInfoDTO> labels) {
        if (profile == null || CollectionUtils.isEmpty(labels)) {
            return Optional.empty();
        }

        OdpsProfileCreateParam param = new OdpsProfileCreateParam();
        param.setProjectName(profile.getOdpsProject());
        param.setTableName(EnvUtils.isPre() ? profile.getOdpsTable() + "_test" : profile.getOdpsTable());
        param.setComment(profile.getOdpsTableComment());
        param.setLifecycle(profile.getOdpsTableLifecycle());

        // 添加主键列、标签列
        String primaryKey = profile.getPrimaryKey();
        List<OdpsColumn> odpsColumns = Lists.newArrayList();
        odpsColumns.add(OdpsColumn.newPrimaryColumn(primaryKey));
        labels.forEach(
                label -> odpsColumns.add(new OdpsColumn(label.getCode(), toOdpsColumnType(label), label.getName()))
        );
        param.setColumns(odpsColumns);

        // 添加分桶参数
        param.setClusteredColumn(OdpsColumn.newPrimaryColumn(primaryKey));
        //TODO 根据预估数据量调整
        param.setBucketNum(512);

        // 添加分区列
        param.setPartitionColumn(OdpsColumn.newPartitionColumn());

        return Optional.of(param);
    }

    /**
     * 根据标签的数据类型推断ODPS列类型
     *
     * @param labelInfoDTO 标签信息
     * @return ODPS列信息
     */
    private OdpsColumn.OdpsColumnType toOdpsColumnType(LabelInfoDTO labelInfoDTO) {
        return OdpsColumn.OdpsColumnType.fromLabelDataType(labelInfoDTO.getDataType());
    }




    /**
     * 构建主查询中的SELECT参数
     *
     * @param profile      物理画像
     * @param reSortLabels 重排序标签
     * @return
     */
    public List<String> buildOdpsGlobleSelectParam(PhysicalProfileInfoDTO profile, List<LabelInfoDTO> reSortLabels) {
        List<String> selectColumns = Lists.newArrayList();

        OdpsMasterConfig masterConfig = profile.getOdpsMasterConfig();
        String masterAlias = OdpsTableAliasUtils.alias(masterConfig.getProject(),
                masterConfig.getTable());
        // 添加主表主键
        String primaryColumn = masterAlias + Constant.DOT + profile.getPrimaryKey();
        selectColumns.add(primaryColumn);

        // 添加关联表字段
        // 查询ODPS表元信息
        String tableName = EnvUtils.isPre()? profile.getOdpsTable() + "_test" : profile.getOdpsTable();
        Optional<TableSchema> tableSchema = odpsService.queryTableSchema(profile.getOdpsProject(), tableName);

        if (!tableSchema.isPresent()) {
            throw new RuntimeException(
                    "Not Found OdpsTableMeta:" + profile.getOdpsProject() + "." + tableName);
        }

        List<String> columnNames = tableSchema.get().getColumns()
                .stream()
                .map(Column::getName).collect(Collectors.toList());
        // 剔除主键字段
        columnNames.remove(profile.getPrimaryKey());

        Map<String, LabelInfoDTO> resortLabelMap = Maps.newHashMap();
        reSortLabels.forEach(
                labelInfoDTO -> {
                    resortLabelMap.put(labelInfoDTO.getCode(), labelInfoDTO);
                }
        );

        columnNames.forEach(
                column -> {
                    // 若执行标签不在原表字段中，则置为NULL
                    if (resortLabelMap.containsKey(column)) {
                        LabelInfoDTO labelInfoDTO = resortLabelMap.get(column);
                        selectColumns.add(labelInfoDTO.getCode());
                    } else {
                        selectColumns.add(nullColumnSql(column));
                    }
                }
        );

        return selectColumns;
    }




    /**
     * @param name
     * @return
     */
    private String nullColumnSql(String name) {
        return "NULL AS " + name;
    }

}
