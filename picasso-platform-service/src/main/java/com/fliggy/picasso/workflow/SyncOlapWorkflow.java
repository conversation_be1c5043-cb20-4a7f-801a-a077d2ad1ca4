package com.fliggy.picasso.workflow;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import com.fliggy.picasso.action.ExecuteActionParam;
import com.fliggy.picasso.action.HoloProfileCreateAction;
import com.fliggy.picasso.action.HoloProfileForeignCreateAction;
import com.fliggy.picasso.action.HoloProfilePartitionCreateAction;
import com.fliggy.picasso.action.HoloProfileSyncAction;
import com.fliggy.picasso.action.OdpsProfileCreateAction;
import com.fliggy.picasso.action.OdpsSampleProfileLoadAction;
import com.fliggy.picasso.action.PhysicalProfileActionParam;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;
import com.fliggy.picasso.service.profile.PhysicalSampleProfileInfoDTO;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.jeasy.flows.work.WorkContext;
import org.jeasy.flows.work.WorkReport;
import org.jeasy.flows.work.WorkReportPredicate;
import org.jeasy.flows.workflow.WorkFlow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static org.jeasy.flows.engine.WorkFlowEngineBuilder.aNewWorkFlowEngine;
import static org.jeasy.flows.workflow.ConditionalFlow.Builder.aNewConditionalFlow;
import static org.jeasy.flows.workflow.ParallelFlow.Builder.aNewParallelFlow;
import static org.jeasy.flows.workflow.SequentialFlow.Builder.aNewSequentialFlow;

/**
 * <AUTHOR> tim on 2022/6/17.
 */
@Component
public class SyncOlapWorkflow {
    private ExecutorService executorService;
    private final static int FLOW_EXECUTOR_MAX_SIZE = 100;
    @Autowired
    private OdpsProfileCreateAction odpsProfileCreateAction;

    @Autowired
    private OdpsSampleProfileLoadAction odpsSampleProfileLoadAction;

    @Autowired
    private HoloProfileCreateAction holoProfileCreateAction;

    @Autowired
    private HoloProfileForeignCreateAction holoProfileForeignCreateAction;

    @Autowired
    private HoloProfileSyncAction holoProfileSyncAction;

    @Autowired
    private HoloProfilePartitionCreateAction holoProfilePartitionCreateAction;

    @PostConstruct
    public void init() {
        this.executorService = new ThreadPoolExecutor(0, FLOW_EXECUTOR_MAX_SIZE,
                60L, TimeUnit.SECONDS,
                new SynchronousQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("Sync-olap-Workflow-Pool-").build()
               );
    }

    /**
     * 执行物理画像表采样工作流
     * @param param 物理表相关的参数
     * @return WorkReport
     */
    public WorkReport run(ExecuteActionParam<PhysicalProfileActionParam> param) {
        PhysicalProfileInfoDTO profile = param.getData().getPhysicalProfileInfo();
        // 生成物理画像工作流
        WorkFlow workflow = syncProfileWorkflow(param);
        // 初始化上下文
        WorkContext context = initContext(profile);
        // 执行工作流
        return aNewWorkFlowEngine()
                .build()
                .run(workflow, context);
    }

    /**
     * 创建任务处理的结果
     * @param report WorkReport
     * @return String 异常=null
     */
    public String buildWorkResult(WorkReport report) {
        if (report == null ) {
            return "report is null";
        }
        StringBuilder builder = new StringBuilder();
        WorkContext context = report.getWorkContext();
        String profile = (String)context.getOrDefault(WorkflowConstants.CONTEXT_KEY_PROFILE, "unknown");
        WorkflowTasks tasks = (WorkflowTasks)context.getOrDefault(
            WorkflowConstants.CONTEXT_KEY_TASKS,
            new WorkflowTasks()
        );
        builder.append("物理画像:[").append(profile).append("]执行状态:[").append(report.getStatus()).append("]")
            .append(" - 子任务详情:[");
        tasks.getTasks().forEach(
            task -> {
                builder.append(task.getName()).append(":").append(task.isSuccess()).append(";");
            }
        );
        builder.append("]");
        return builder.toString();
    }

    /**
     * 初始化上下文
     *
     * @param profile 物理画像
     * @return 上下文
     */
    private WorkContext initContext(PhysicalProfileInfoDTO profile) {
        WorkContext context = new WorkContext();
        context.put(WorkflowConstants.CONTEXT_KEY_PROFILE, profile.getPhysicalProfileCode());
        context.put(WorkflowConstants.CONTEXT_KEY_TASKS, new WorkflowTasks());
        return context;
    }

    /**
     * 创建同步任务
     * @param param 物理表相关的参数
     * @return WorkFlow
     */
    private WorkFlow syncProfileWorkflow(ExecuteActionParam<PhysicalProfileActionParam> param) {
        PhysicalProfileActionParam physicalProfileActionParam = param.getData();
        PhysicalProfileInfoDTO profile = physicalProfileActionParam.getPhysicalProfileInfo();
        // 修改为采样表的参数
        PhysicalSampleProfileInfoDTO sampleProfile = PhysicalSampleProfileInfoDTO.convert2SampleDTO(profile);
        physicalProfileActionParam.setPhysicalProfileInfo(sampleProfile);
        return aNewConditionalFlow()
            .named(String.format("PhysicalSampleProfileWork profile[%s]", profile.getPhysicalProfileCode()))
            // 创建ODPS的采样数据
            .execute(new ExecuteActionWork(odpsProfileCreateAction, param))
            .when(WorkReportPredicate.COMPLETED)
            .then(
                aNewConditionalFlow()
                    .named(String.format("PhysicalSampleProfileWork profile[%s]", profile.getPhysicalProfileCode()))
                    // 将数据从原ODPS里面采样进结果表
                    .execute(new ExecuteActionWork(odpsSampleProfileLoadAction, param))
                    .when(WorkReportPredicate.COMPLETED)
                    // 将采样后面的DOPS数据同步到Holo里面
                    .then(holoSequentialFlow(param))
                    .build()
            )
            .build();
        //return aNewSequentialFlow()
        //    .named(String.format("SyncOlapProfileWork profile[%s]", profile.getPhysicalProfileCode()))
        //    // 同步holo数据
        //    .execute(holoSequentialFlow(param))
        //    .build();
    }

    /**
     * Holo建表/同步流程
     *
     * @param param
     * @return
     */
    public WorkFlow holoSequentialFlow(ExecuteActionParam<PhysicalProfileActionParam> param) {
        return aNewSequentialFlow()
            .named("Sync odps profile to hologres")
            // 并行创建Holo外表和Holo表
            .execute(
                aNewParallelFlow()
                    .named("Create HoloForeignTable & HoloTable in parallel")
                    .execute(
                        new ExecuteActionWork(holoProfileForeignCreateAction, param),
                        new ExecuteActionWork(holoProfileCreateAction, param)
                    )
                    .with(executorService)
                    .build()
            )
            // 创建Holo表分区子表
            .then(new ExecuteActionWork(holoProfilePartitionCreateAction, param))
            // 同步Holo表
            .then(new ExecuteActionWork(holoProfileSyncAction, param))
            .build();
    }
}
