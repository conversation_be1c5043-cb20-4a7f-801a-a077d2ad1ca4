package com.fliggy.picasso.hologres;

import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;

/**
 * <AUTHOR>
 * @date 2020/12/15 下午3:16
 */
public class HoloColumnTypeHelper {

    /**
     *
     */
    public static String TIMESTAMP_FORMAT = "yyyy-MM-dd hh24:mi:ss";

    /**
     * 字符串转时间戳
     *
     * @param oriColumn 原始字段类型
     * @return SQL转换脚本
     */
    public static String text2Timestamp(String oriColumn) {
        return String.format("to_timestamp(%s, 'yyyy-MM-dd hh24:mi:ss')", oriColumn);
    }

    /**
     * kv字符串转JSON字符串
     *
     * @param oriColumn 原始字段类型
     * @return SQL转换脚本
     */
    public static String arrayStr2Array(String oriColumn) {
        return String.format("STRING_TO_ARRAY(%s, ',')", oriColumn);
    }

    /**
     * kv字符串转JSON字符串
     *
     * @param oriColumn 原始字段类型
     * @return SQL转换脚本
     */
    public static String kvStr2JsonStr(String oriColumn) {
        return String.format("CASE WHEN LENGTH(%s)>0 THEN CONCAT('{\"',REPLACE(REPLACE(%s,',',',\"'),':','\":'),"
            + "'}')\n"
            + "     ELSE NULL\n"
            + "END", oriColumn, oriColumn);
    }

    /**
     * 生成类型转换SQL脚本
     *
     * @param oriColumn   原始字段名称
     * @param dataType    目标数据类型
     * @return 类型转换SQL脚本
     */
    public static String convertSql(String oriColumn, LabelBizDataTypeEnum dataType) {

        switch (dataType) {
            case ENUM:
            case NUMBER:
            case DATE:
            case KV:
                // KV类型在ODPS侧MERGE期间已经处理过了,这里不再处理
                return oriColumn;
            case MULTI_VALUE:
                return arrayStr2Array(oriColumn);

            default:
                //TODO 扩展类型转换支持
                throw new RuntimeException("Not Support Type Convert!");
        }
    }
}
