package com.fliggy.picasso.hologres;

import com.alibaba.fastjson.JSON;
import com.alibaba.security.SecurityUtil;

import com.fliggy.picasso.common.Constant;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.freemarker.TemplateEnum;
import com.fliggy.picasso.freemarker.TemplateReader;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/7 下午8:19
 */
@Component
public class HologresMetaService {

    private static final Logger LOG = LoggerFactory.getLogger(Constant.TASK_LOG);

    @Autowired
    @Qualifier(value = "hikariJdbcTemplate")
    private JdbcTemplate holoJdbcTemplate;

    @Autowired
    private TemplateReader templateReader;

    @AteyeInvoker(description = "手动查询hologres表的元数据", paraDesc = "表名")
    public String queryHoloTableMetaManually(String tableName) {
        List<HoloColumnMeta> result = queryTableMeta(tableName);
        if (CollectionUtils.isNullOrEmpty(result)) {
            return "查询结果为空！";
        }

        return JSON.toJSONString(result);
    }

    public List<HoloColumnMeta> queryTableMeta(String tableName) {

        if (StringUtils.isEmpty(tableName)) {
            throw new IllegalArgumentException("Table Can't Be Null!");
        }

        Map<String, String> params = Maps.newHashMap();
        params.put("table", SecurityUtil.trimSql(tableName));
        String sql = templateReader.read(TemplateEnum.HOLO_TABLE_META_QUERY, params);

        return holoJdbcTemplate.query(sql, rs -> {
            List<HoloColumnMeta> columns = Lists.newArrayList();
            try {
                while (rs.next()) {
                    HoloColumnMeta columnMeta = new HoloColumnMeta();
                    columnMeta.setNum(rs.getInt("attnum"));
                    columnMeta.setField(rs.getString("field"));
                    columnMeta.setType(rs.getString("type"));
                    columnMeta.setLength(rs.getString("length"));
                    columnMeta.setLengthvar(rs.getString("lengthvar"));
                    columnMeta.setNotnull(rs.getBoolean("notnull"));
                    columnMeta.setComment(rs.getString("comment"));
                    columns.add(columnMeta);
                }
            } catch (SQLException e) {
                String msg = String.format("Execute Query Table Meta Error! Table: %s", tableName);
                LOG.error(msg, e);
                throw new RuntimeException(msg, e);
            }
            return columns;
        });
    }

    @Data
    public static class HoloColumnMeta {
        private Integer num;
        private String field;
        private String type;
        private String length;
        private String lengthvar;
        private Boolean notnull;
        private String comment;
    }
}
