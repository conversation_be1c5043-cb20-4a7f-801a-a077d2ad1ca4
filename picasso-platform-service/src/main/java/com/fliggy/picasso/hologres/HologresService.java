package com.fliggy.picasso.hologres;

import javax.sql.DataSource;

import com.fliggy.picasso.common.Constant;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.taobao.ateye.annotation.AteyeInvoker;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/12/7 下午8:19
 */
@Component
public class HologresService {

    private static final Logger LOG = LoggerFactory.getLogger(Constant.TASK_LOG);

    @Autowired
    @Qualifier(value = "hikariJdbcTemplate")
    private JdbcTemplate holoJdbcTemplate;

    @Autowired
    @Qualifier(value = "hikariDataSource")
    private DataSource holoDataSource;

    public void execute(String sql) {

        if (StringUtils.isEmpty(sql)) {
            throw new IllegalArgumentException("SQL Can't Be Null!");
        }

        //sql = SecurityUtil.trimSql(sql);

        try {
            holoJdbcTemplate.execute(sql);
        } catch (Exception e) {
            String msg = String.format("Execute Sql Error! SQL: %s", sql);
            LOG.error(msg, e);
            throw new RuntimeException(msg, e);
        }
    }

    public <T> List<T> query(String sql, RowMapper<T> rowMapper) {
        return holoJdbcTemplate.query(sql, rowMapper);
    }

    @AteyeInvoker(description = "HOLO Query", paraDesc = "sql")
    public Object query(String sql) {
        if (StringUtils.isBlank(sql)) {
            throw new ParamErrorException("Query Holo failed, SQL is null.");
        }
        try {
            return holoJdbcTemplate.query(sql, resultSet -> {
                if (resultSet.next()) {
                    return resultSet.getObject(1);
                }
                return null;
            });
        } catch (Exception e) {
            String msg = String.format("[HOLO Query] Execute Sql Error! SQL: %s", sql);
            throw new RuntimeException(msg, e);
        }
    }
}
