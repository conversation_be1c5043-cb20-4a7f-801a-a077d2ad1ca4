package com.fliggy.picasso.employee.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EmployeeChargeInfo {

    private String empId;

    private String nickName;

    private String buNo;

    /**
     * I离职，A在职
     */
    private String workStatus;

    private String superEmpId;

    public EmployeeChargeInfo(String empId, String nickName, String buNo, String workStatus) {
        this.empId = empId;
        this.nickName = nickName;
        this.buNo = buNo;
        this.workStatus = workStatus;
    }
}
