package com.fliggy.picasso.employee;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/6 上午10:56
 */
public interface AmdpEmployeeService<T> {

    /**
     * @param searchKey
     * @return
     */
    List<T> getEmployeeList(String searchKey);

    /**
     * 根据工号获取花名
     *
     * @param empId 工号
     * @return 花名
     */
    default String getNameByEmpId(String empId) {
        return null;
    };

    default String getDingTalkIdByEmpId(String empId) {
        return null;
    };

}
