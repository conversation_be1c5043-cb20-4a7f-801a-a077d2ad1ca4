package com.fliggy.picasso.employee;

import com.alibaba.ihr.amdplatform.common.annotation.AmdpDomain;
import com.alibaba.ihr.amdplatform.common.annotation.AmdpDomainField;

import lombok.Data;

@Data
public class Amdp3256Employee {

    /**
     * 员工域
     */
    @AmdpDomain(
        code = "EMP_EMPLOYEE"
    )
    private EmpEmployee empEmployee;

    public EmpEmployee getEmpEmployee() {
        return this.empEmployee;
    }

    public void setEmpEmployee(EmpEmployee empEmployee) {
        this.empEmployee = empEmployee;
    }

    /**
     * 员工域
     */
    @AmdpDomain(
        code = "EMP_EMPLOYEE"
    )
    public static class EmpEmployee {
        /**
         * 员工工号
         */
        @AmdpDomainField(
            code = "workNo",
            domainCode = "EMP_EMPLOYEE"
        )
        private String workNo;

        /**
         * 花名
         */
        @AmdpDomainField(
            code = "nickName",
            domainCode = "EMP_EMPLOYEE"
        )
        private String nickName;

        /**
         * BUC.userId
         */
        @AmdpDomainField(
            code = "userId",
            domainCode = "EMP_EMPLOYEE"
        )
        private Integer userId;

        /**
         * 钉钉talkID
         */
        @AmdpDomainField(
            code = "dingtalkIdVice",
            domainCode = "EMP_EMPLOYEE"
        )
        private String dingtalkIdVice;

        public String getWorkNo() {
            return this.workNo;
        }

        public void setWorkNo(String workNo) {
            this.workNo = workNo;
        }

        public String getNickName() {
            return this.nickName;
        }

        public void setNickName(String nickName) {
            this.nickName = nickName;
        }

        public Integer getUserId() {
            return this.userId;
        }

        public void setUserId(Integer userId) {
            this.userId = userId;
        }

        public String getDingtalkIdVice() {
            return this.dingtalkIdVice;
        }

        public void setDingtalkIdVice(String dingtalkIdVice) {
            this.dingtalkIdVice = dingtalkIdVice;
        }
    }
}
