package com.fliggy.picasso.employee;

import com.alibaba.ihr.amdplatform.common.annotation.AmdpDomain;
import com.alibaba.ihr.amdplatform.common.annotation.AmdpDomainField;

import java.util.Date;
import java.util.List;

public class Amdp3271Employee {

    /**
     * 员工域
     */
    @AmdpDomain(
            code = "EMP_EMPLOYEE"
    )
    private EmpEmployee empEmployee;

    /**
     * 员工阿里任职历史域
     */
    @AmdpDomain(
            code = "EMP_JOB_HISTORY"
    )
    private List<EmpJobHistory> empJobHistory;

    public EmpEmployee getEmpEmployee() {
        return this.empEmployee;
    }

    public void setEmpEmployee(EmpEmployee empEmployee) {
        this.empEmployee = empEmployee;
    }

    public List<EmpJobHistory> getEmpJobHistory() {
        return this.empJobHistory;
    }

    public void setEmpJobHistory(List<EmpJobHistory> empJobHistory) {
        this.empJobHistory = empJobHistory;
    }

    /**
     * 员工域
     */
    @AmdpDomain(
            code = "EMP_EMPLOYEE"
    )
    public static class EmpEmployee {
        /**
         * 员工工号
         */
        @AmdpDomainField(
                code = "workNo",
                domainCode = "EMP_EMPLOYEE"
        )
        private String workNo;

        /**
         * BU编号
         */
        @AmdpDomainField(
                code = "buDeptNo",
                domainCode = "EMP_EMPLOYEE"
        )
        private String buDeptNo;

        /**
         * 花名
         */
        @AmdpDomainField(
                code = "nickName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String nickName;

        /**
         * 工作状态
         */
        @AmdpDomainField(
                code = "workStatus",
                domainCode = "EMP_EMPLOYEE"
        )
        private String workStatus;

        public String getWorkNo() {
            return this.workNo;
        }

        public void setWorkNo(String workNo) {
            this.workNo = workNo;
        }

        public String getBuDeptNo() {
            return this.buDeptNo;
        }

        public void setBuDeptNo(String buDeptNo) {
            this.buDeptNo = buDeptNo;
        }

        public String getNickName() {
            return this.nickName;
        }

        public void setNickName(String nickName) {
            this.nickName = nickName;
        }

        public String getWorkStatus() {
            return this.workStatus;
        }

        public void setWorkStatus(String workStatus) {
            this.workStatus = workStatus;
        }
    }

    /**
     * 员工阿里任职历史域
     */
    @AmdpDomain(
            code = "EMP_JOB_HISTORY"
    )
    public static class EmpJobHistory {
        /**
         * 职务历史ID
         */
        @AmdpDomainField(
                code = "jobPlanId",
                domainCode = "EMP_JOB_HISTORY"
        )
        private Long jobPlanId;

        /**
         * 员工工号
         */
        @AmdpDomainField(
                code = "workNo",
                domainCode = "EMP_JOB_HISTORY"
        )
        private String workNo;

        /**
         * 主管工号
         */
        @AmdpDomainField(
                code = "superWorkNo",
                domainCode = "EMP_JOB_HISTORY"
        )
        private String superWorkNo;

        /**
         * 开始时间
         */
        @AmdpDomainField(
                code = "startTime",
                domainCode = "EMP_JOB_HISTORY"
        )
        private Date startTime;

        /**
         * 结束时间
         */
        @AmdpDomainField(
                code = "endTime",
                domainCode = "EMP_JOB_HISTORY"
        )
        private Date endTime;

        /**
         * buNo编号
         */
        @AmdpDomainField(
                code = "buNo",
                domainCode = "EMP_JOB_HISTORY"
        )
        private String buNo;

        public Long getJobPlanId() {
            return this.jobPlanId;
        }

        public void setJobPlanId(Long jobPlanId) {
            this.jobPlanId = jobPlanId;
        }

        public String getWorkNo() {
            return this.workNo;
        }

        public void setWorkNo(String workNo) {
            this.workNo = workNo;
        }

        public String getSuperWorkNo() {
            return this.superWorkNo;
        }

        public void setSuperWorkNo(String superWorkNo) {
            this.superWorkNo = superWorkNo;
        }

        public Date getStartTime() {
            return this.startTime;
        }

        public void setStartTime(Date startTime) {
            this.startTime = startTime;
        }

        public Date getEndTime() {
            return this.endTime;
        }

        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }

        public String getBuNo() {
            return this.buNo;
        }

        public void setBuNo(String buNo) {
            this.buNo = buNo;
        }
    }
}
