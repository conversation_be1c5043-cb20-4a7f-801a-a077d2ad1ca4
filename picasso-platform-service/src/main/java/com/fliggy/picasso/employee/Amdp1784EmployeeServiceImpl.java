package com.fliggy.picasso.employee;

import java.util.List;
import java.util.Objects;

import com.alibaba.ihr.amdplatform.common.util.AmdpResponseUtil;
import com.alibaba.ihr.amdplatform.service.api.AmdpDataQueryService;
import com.alibaba.ihr.amdplatform.service.dto.QueryResultDTO;
import com.alibaba.ihr.amdplatform.service.dto.ResultDTO;
import com.alibaba.ihr.amdplatform.service.param.AuthParam;
import com.alibaba.ihr.amdplatform.service.param.DataQueryParam;
import com.alibaba.ihr.amdplatform.service.param.FilterField;

import com.fliggy.picasso.common.Constant;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2021/4/6 上午10:57
 */
@Service
public class Amdp1784EmployeeServiceImpl implements AmdpEmployeeService<Amdp1784Employee> {

    private static final Logger logger = LoggerFactory.getLogger(Amdp1784EmployeeServiceImpl.class);

    /**
     * 注入hsf服务接口
     */
    @Autowired
    private AmdpDataQueryService amdpDataQueryService;

    @Override
    public List<Amdp1784Employee> getEmployeeList(String searchKey) {

        if (StringUtils.isEmpty(searchKey)) {
            return Lists.newArrayList();
        }

        //1.鉴权参数
        AuthParam auth = new AuthParam(Constant.APP_NAME, AmdpConstants.AMDP_APP_SECRET);

        //2.聚合请求参数封装
        DataQueryParam queryParam = new DataQueryParam();
        //2.1 设置聚合id
        queryParam.setCombineId(AmdpConstants.AMDP_AGGREGATE_ID);
        //2.2 设置过滤字段（聚合里面的查询条件字段）
        List<FilterField> filterFieldList = Lists.newArrayList();
        //设置过滤字段名称和值
        FilterField filterField = new FilterField("searchKey", searchKey);
        filterFieldList.add(filterField);
        queryParam.setFilterFieldList(filterFieldList);

        //3 发起调用，获取结果
        ResultDTO<QueryResultDTO> resultDTO = amdpDataQueryService.queryDataSet(auth, queryParam);

        if (!resultDTO.isSuccess()) {
            throw new RuntimeException("查询用户列表失败! msg:" + resultDTO.getErrorMessage());
        }

        List<Amdp1784Employee> res = AmdpResponseUtil.convertCombineResultToBeanList(resultDTO, Amdp1784Employee.class);

        return res;
    }

    @Override
    public String getNameByEmpId(String empId) {
        if (StringUtils.isEmpty(empId)) {
            return null;
        }

        try {
            List<Amdp1784Employee> employeeList = getEmployeeList(empId);
            if (CollectionUtils.isEmpty(employeeList)) {
                return null;
            }

            Amdp1784Employee.EmpEmployee empEmployee = employeeList.get(0).getEmpEmployee();
            if (Objects.isNull(empEmployee)) {
                return null;
            }

            return StringUtils.isBlank(empEmployee.getNickName()) ? empEmployee.getName() : empEmployee.getNickName();
        } catch (Exception e) {
            logger.error("getNameByEmpId error, empId:{}", empId, e);
        }

        return null;
    }

    @AteyeInvoker(description = "测试获取用户姓名", paraDesc = "empId")
    public void testGetNameByEmpId(String empId) {
        String name = getNameByEmpId(empId);
        if (Objects.isNull(name)) {
            Ateye.out.println("name is null");
        } else {
            Ateye.out.println(name);
        }
    }

}
