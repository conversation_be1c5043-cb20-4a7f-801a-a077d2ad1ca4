package com.fliggy.picasso.employee;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/3/28 下午4:14
 */
public class UserContext {

    /**
     *
     */
    private static final ThreadLocal<UserContextDTO> BUC_USER_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 当前登录用户放入上下文
     *
     * @param bucUser 用户信息
     */
    public static void setBucUser(UserContextDTO bucUser) {
        BUC_USER_THREAD_LOCAL.set(bucUser);
    }

    /**
     * 获取当前登录用户
     *
     * @return Optional<EnhancedUser>
     */
    public static Optional<UserContextDTO> getBucUser() {
        return Optional.ofNullable(BUC_USER_THREAD_LOCAL.get());
    }

    public static void remove() {
        BUC_USER_THREAD_LOCAL.remove();
    }

    /**
     * empId相等判断，会去除0前缀
     *
     * @param empId1 工号1
     * @param empId2 工号2
     * @return 相等返回true, 不相等或任一empId为空返回false
     */
    public static boolean empIdEquals(String empId1, String empId2) {

        empId1 = formatEmpId(empId1);
        empId2 = formatEmpId(empId2);
        if (StringUtils.isBlank(empId1) || StringUtils.isBlank(empId2)) {
            return false;
        }
        return empId1.equals(empId2);
    }

    /**
     * 标准化工号，数字类型的工号，去掉前面的0
     *
     * @param empId 工号
     * @return 标准化工号
     */
    public static String formatEmpId(String empId) {
        if (StringUtils.isNumeric(empId)) {
            return String.valueOf(Long.valueOf(empId));
        }

        if (StringUtils.isNotBlank(empId)) {
            return empId.toUpperCase();
        } else {
            return empId;
        }
    }
}
