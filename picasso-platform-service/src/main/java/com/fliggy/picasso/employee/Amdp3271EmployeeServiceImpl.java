package com.fliggy.picasso.employee;

import com.alibaba.fastjson.JSON;
import com.alibaba.ihr.amdplatform.common.util.AmdpResponseUtil;
import com.alibaba.ihr.amdplatform.service.api.AmdpDataQueryService;
import com.alibaba.ihr.amdplatform.service.dto.QueryResultDTO;
import com.alibaba.ihr.amdplatform.service.dto.ResultDTO;
import com.alibaba.ihr.amdplatform.service.param.AuthParam;
import com.alibaba.ihr.amdplatform.service.param.DataQueryParam;
import com.alibaba.ihr.amdplatform.service.param.FilterField;
import com.fliggy.picasso.common.Constant;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.employee.entity.EmployeeChargeInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class Amdp3271EmployeeServiceImpl {

    @Autowired
    private AmdpDataQueryService amdpDataQueryService;

    @AteyeInvoker(description = "获取离职人员主管信息", paraDesc = "离职人员工号列表")
    public void testGetDimissionChargeEmployee(String empIds) {
        List<String> empIdList = new ArrayList<>(Arrays.asList(empIds.split(",")));
        Map<String, Employee> result = getDimissionChargeEmployee(empIdList);
        Ateye.out.println(JSON.toJSONString(result));
    }

    /**
     * 获取离职人员的主管信息
     * @param empIdList
     * @return
     */
    public Map<String, Employee> getDimissionChargeEmployee(List<String> empIdList) {
        if (CollectionUtils.isNullOrEmpty(empIdList)) {
            return Maps.newHashMap();
        }

        List<EmployeeChargeInfo> employeeChargeInfos = getEmployeeChargeInfoList(empIdList);
        if (CollectionUtils.isNullOrEmpty(employeeChargeInfos)) {
            return Maps.newHashMap();
        }

        Map<String, Employee> result = new HashMap<>();
        for (EmployeeChargeInfo employeeChargeInfo : employeeChargeInfos) {
            if (!isDimission(employeeChargeInfo.getWorkStatus(), employeeChargeInfo.getBuNo())) {
                continue;
            }
            Employee chargeEmployee = findChargeEmployee(employeeChargeInfo.getSuperEmpId());
            if (Objects.nonNull(chargeEmployee)) {
                result.put(employeeChargeInfo.getEmpId(), chargeEmployee);
            }
        }
        return result;
    }

    /**
     * 递归查找在职主管信息
     * @param superEmpId
     * @return
     */
    private Employee findChargeEmployee(String superEmpId) {
        while (superEmpId != null) {
            List<EmployeeChargeInfo> employeeChargeInfos = getEmployeeChargeInfoList(Collections.singletonList(superEmpId));
            if (CollectionUtils.isNullOrEmpty(employeeChargeInfos)) {
                return null;
            }
            EmployeeChargeInfo employeeChargeInfo = employeeChargeInfos.get(0);
            if (!isDimission(employeeChargeInfo.getWorkStatus(), employeeChargeInfo.getBuNo())) {
                return new Employee(employeeChargeInfo.getEmpId(), employeeChargeInfo.getNickName());
            }
            superEmpId = employeeChargeInfo.getSuperEmpId();
        }
        return null;
    }

    /**
     * 获取员工主管信息
     * @param empIdList
     * @return
     */
    private List<EmployeeChargeInfo> getEmployeeChargeInfoList(List<String> empIdList) {
        List<Amdp3271Employee> employeeList = getEmployeeList(empIdList);
        if (CollectionUtils.isNullOrEmpty(employeeList)) {
            return Lists.newArrayList();
        }

        List<EmployeeChargeInfo> result = Lists.newArrayList();
        for (Amdp3271Employee employee : employeeList) {
            if (Objects.isNull(employee.getEmpEmployee()) || CollectionUtils.isNullOrEmpty(employee.getEmpJobHistory())) {
                continue;
            }

            Amdp3271Employee.EmpEmployee empEmployee = employee.getEmpEmployee();
            EmployeeChargeInfo employeeChargeInfo = new EmployeeChargeInfo(empEmployee.getWorkNo(),
                    empEmployee.getNickName(), empEmployee.getBuDeptNo(), empEmployee.getWorkStatus());
            if (!isDimission(empEmployee.getWorkStatus(), empEmployee.getBuDeptNo())) {
                result.add(employeeChargeInfo);
                continue;
            }

            List<Amdp3271Employee.EmpJobHistory> sortedJobHistory = employee.getEmpJobHistory().stream()
                    .sorted(Comparator.comparing(Amdp3271Employee.EmpJobHistory::getStartTime).reversed()).collect(Collectors.toList());
            for (Amdp3271Employee.EmpJobHistory jobHistory : sortedJobHistory) {
                if (Objects.nonNull(jobHistory.getBuNo()) && "72609".equals(jobHistory.getBuNo())) {
                    employeeChargeInfo.setSuperEmpId(jobHistory.getSuperWorkNo());
                    break;
                }
            }
            result.add(employeeChargeInfo);
        }
        return result;
    }

    private boolean isDimission(String workStatus, String buNo) {
        // 离职
        if (Objects.nonNull(workStatus) && "I".equals(workStatus)) {
            return true;
        }
        // 转岗
        if (Objects.nonNull(buNo) && !"72609".equals(buNo)) {
            return true;
        }
        return false;
    }

    private List<Amdp3271Employee> getEmployeeList(List<String> empIdList) {
        AuthParam auth = new AuthParam(Constant.APP_NAME, AmdpConstants.AMDP_APP_SECRET);
        DataQueryParam queryParam = new DataQueryParam();
        queryParam.setCombineId(AmdpConstants.AMDP_3271_AGGREGATE_ID);
        FilterField workNoField = new FilterField("workNoList", empIdList);
        queryParam.setFilterFieldList(Collections.singletonList(workNoField));
        ResultDTO<QueryResultDTO> resultDTO = amdpDataQueryService.queryDataSet(auth, queryParam);
        if (Objects.isNull(resultDTO) || !resultDTO.isSuccess()) {
            return null;
        }

        return AmdpResponseUtil.convertCombineResultToBeanList(resultDTO, Amdp3271Employee.class);
    }
}
