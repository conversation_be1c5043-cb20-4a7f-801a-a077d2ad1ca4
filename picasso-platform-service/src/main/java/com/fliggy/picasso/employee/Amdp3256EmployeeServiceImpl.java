package com.fliggy.picasso.employee;

import com.alibaba.ihr.amdplatform.common.util.AmdpResponseUtil;
import com.alibaba.ihr.amdplatform.service.api.AmdpDataQueryService;
import com.alibaba.ihr.amdplatform.service.dto.QueryResultDTO;
import com.alibaba.ihr.amdplatform.service.dto.ResultDTO;
import com.alibaba.ihr.amdplatform.service.param.AuthParam;
import com.alibaba.ihr.amdplatform.service.param.DataQueryParam;
import com.alibaba.ihr.amdplatform.service.param.FilterField;
import com.fliggy.picasso.common.Constant;
import com.taobao.ateye.annotation.AteyeInvoker;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class Amdp3256EmployeeServiceImpl implements AmdpEmployeeService<Amdp3256Employee> {

    @Autowired
    private AmdpDataQueryService amdpDataQueryService;

    @Override
    public List<Amdp3256Employee> getEmployeeList(String empId) {
        AuthParam auth = new AuthParam(Constant.APP_NAME, AmdpConstants.AMDP_APP_SECRET);
        DataQueryParam queryParam = new DataQueryParam();
        queryParam.setCombineId(AmdpConstants.AMDP_3256_AGGREGATE_ID);
        FilterField workNoField = new FilterField("workNo", empId);
        queryParam.setFilterFieldList(Collections.singletonList(workNoField));
        ResultDTO<QueryResultDTO> resultDTO = amdpDataQueryService.queryDataSet(auth, queryParam);
        if (Objects.isNull(resultDTO) || !resultDTO.isSuccess()) {
            return null;
        }

        return AmdpResponseUtil.convertCombineResultToBeanList(resultDTO, Amdp3256Employee.class);
    }

    @AteyeInvoker(description = "根据工号获取阿里钉号", paraDesc = "工号")
    @Override
    public String getDingTalkIdByEmpId(String empId) {
        if (StringUtils.isEmpty(empId)){
            return null;
        }

        List<Amdp3256Employee> employeeList = getEmployeeList(empId);
        if (CollectionUtils.isEmpty(employeeList)){
            return null;
        }
        Amdp3256Employee amdp3256Employee = employeeList.get(0);
        return amdp3256Employee.getEmpEmployee().getDingtalkIdVice();
    }
}
