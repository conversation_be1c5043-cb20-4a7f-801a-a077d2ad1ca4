package com.fliggy.picasso.freemarker;

import freemarker.template.Template;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

/**
 * 模版读取类
 *
 * <AUTHOR>
 * @date 2020/12/1 下午3:54
 */
@Component
public class TemplateReader {

    private static final Logger LOG = LoggerFactory.getLogger(TemplateReader.class);

    @Autowired
    public FreeMarkerConfigurer freeMarkerConfigurer;

    /**
     * 读取指定模版
     *
     * @param templateEnum 指定模版
     * @param param        模版参数
     * @return 文本字符串
     */
    public String read(TemplateEnum templateEnum, Object param) {
        Template template;
        try {
            template = freeMarkerConfigurer.getConfiguration().getTemplate(templateEnum.getFileName());
            return FreeMarkerTemplateUtils.processTemplateIntoString(template, param);
        } catch (Exception e) {
            LOG.error("Read Template Error!", e);
            throw new RuntimeException("Read Template Error!", e);
        }
    }
}
