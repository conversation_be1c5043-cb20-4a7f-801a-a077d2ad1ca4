package com.fliggy.picasso.freemarker;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/12/1 下午3:58
 */
public enum TemplateEnum {

    /**
     * 创建ODPS物理画像表
     */
    ODPS_PROFILE_CREATE("odps/odps_phyprofile_create.ftl"),

    /**
     * 更新ODPS物理画像表
     */
    ODPS_PROFILE_UPDATE("odps/odps_phyprofile_update.ftl"),

    /**
     * 加载ODPS物理画像表
     */
    ODPS_PROFILE_DATA_LOAD("odps/odps_phyprofile_data_load.ftl"),

    /**
     * 加载ODPS物理画像表
     */
    ODPS_PROFILE_DATA_LOAD_NEW("odps/odps_phyprofile_data_load_new.ftl"),

    /**
     * 加载ODPS采样表
     */
    ODPS_PROFILE_SAMPLE_DATA_LOAD("odps/odps_phyprofile_sample_data_load.ftl"),

    /**
     * 加载ODPS物理画像JSON格式化表
     */
    ODPS_PROFILE_JSON_LOAD("odps/odps_phyprofile_json_load.ftl"),

    /**
     * 创建HOLO物理画像表
     */
    HOLO_PHYPROFILE_CREATE("hologres/holo_phyprofile_create.ftl"),

    /**
     * 创建HOLO物理画像表外表
     */
    HOLO_PHYPROFILE_FOREIGH_CREATE("hologres/holo_phyprofile_foreign_create.ftl"),

    /**
     * 更新HOLO物理画像表
     */
    HOLO_PHYPROFILE_UPDATE("hologres/holo_phyprofile_update.ftl"),

    /**
     * 更新HOLO物理画像表外表
     */
    HOLO_PHYPROFILE_FOREIGH_UPDATE("hologres/holo_phyprofile_foreign_update.ftl"),

    /**
     * 创建HOLO物理画像表分区表子表
     */
    HOLO_PHYPROFILE_PARTITION_CREATE("hologres/holo_phyprofile_partition_create.ftl"),

    /**
     * 同步HOLO物理画像表数据（从HOLO外表同步）
     */
    HOLO_PHYPROFILE_SYNC("hologres/holo_phyprofile_sync.ftl"),

    /**
     * 查询HOLO中表的元信息
     */
    HOLO_TABLE_META_QUERY("hologres/table_meta_query.ftl"),

    /**
     * 人群PAGE子任务
     */
    ODPS_GROUP_PAGE("odps/odps_group_page.ftl"),


    /**
     * 洞察分析 - 父分析sql模板
     */
    ODPS_ANALYSIS_PARENT_SELECT("odps/odps_analysis_parent_select.ftl"),

    /**
     * 洞察分析 - 子分析sql模板
     */
    ODPS_ANALYSIS_CHILD_SELECT("odps/odps_analysis_child_select.ftl"),

    /**
     * 洞察分析 - 批量分析sql模板
     */
    ODPS_ANALYSIS_BATCH("odps/odps_analysis_batch.ftl"),

    /**
     * 人群放大计算
     */
    ODPS_CROWD_ENLARGE("odps/odps_crowd_enlarge_cal.ftl"),

    /**
     * ha3格式化和产出odps
     */
    ODPS_PROFILE_HA3_FORMAT("odps/odps_profile_ha3_format.ftl"),

    /**
     * ha3格式化和产出odps
     */
    ODPS_PROFILE_HA3_FORMAT_NEW("odps/odps_profile_ha3_format_new.ftl"),


    ;

    @Getter
    private final String fileName;

    TemplateEnum(String fileName) {
        this.fileName = fileName;
    }
}
