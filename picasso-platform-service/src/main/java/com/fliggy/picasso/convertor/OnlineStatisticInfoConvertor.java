package com.fliggy.picasso.convertor;

import com.fliggy.picasso.common.enums.statistic.StatisticEntityTypeEnum;
import com.fliggy.picasso.common.enums.statistic.StatisticTimeDimEnum;
import com.fliggy.picasso.common.statistic.OnlineStatisticInfoVO;
import com.fliggy.picasso.dao.OnlineStatisticInfoDO;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class OnlineStatisticInfoConvertor {

    public static OnlineStatisticInfoVO convertToVO(OnlineStatisticInfoDO onlineStatisticInfoDO) {
        OnlineStatisticInfoVO vo = new OnlineStatisticInfoVO();
        vo.setId(onlineStatisticInfoDO.getId());
        vo.setGmtCreate(onlineStatisticInfoDO.getGmtCreate());
        vo.setGmtModified(onlineStatisticInfoDO.getGmtModified());
        vo.setEntityId(onlineStatisticInfoDO.getEntityId());
        if (StringUtils.isNotBlank(onlineStatisticInfoDO.getEntityType())) {
            vo.setEntityType(StatisticEntityTypeEnum.getByCode(onlineStatisticInfoDO.getEntityType()));
        }
        if (StringUtils.isNotBlank(onlineStatisticInfoDO.getStatisticDim())) {
            vo.setStatisticDim(StatisticTimeDimEnum.getByCode(onlineStatisticInfoDO.getStatisticDim()));
        }
        vo.setStatisticVal(onlineStatisticInfoDO.getStatisticVal());
        vo.setStatisticDate(onlineStatisticInfoDO.getStatisticDate());
        Boolean isValid = Objects.equals(onlineStatisticInfoDO.getIsValid(), 1);
        vo.setIsValid(isValid);
        Boolean isHeavy = Objects.equals(onlineStatisticInfoDO.getIsHeavy(), 1);
        vo.setIsHeavy(isHeavy);
        return vo;
    }

    public static OnlineStatisticInfoDO convertToDO(OnlineStatisticInfoVO onlineStatisticInfoVO) {
        OnlineStatisticInfoDO onlineStatisticInfoDO = new OnlineStatisticInfoDO();
        onlineStatisticInfoDO.setId(onlineStatisticInfoVO.getId());
        onlineStatisticInfoDO.setGmtCreate(onlineStatisticInfoVO.getGmtCreate());
        onlineStatisticInfoDO.setGmtModified(onlineStatisticInfoVO.getGmtModified());
        onlineStatisticInfoDO.setEntityId(onlineStatisticInfoVO.getEntityId());
        if (Objects.nonNull(onlineStatisticInfoVO.getEntityType())) {
            onlineStatisticInfoDO.setEntityType(onlineStatisticInfoVO.getEntityType().getCode());
        }
        if (Objects.nonNull(onlineStatisticInfoVO.getStatisticDim())) {
            onlineStatisticInfoDO.setStatisticDim(onlineStatisticInfoVO.getStatisticDim().getCode());
        }
        onlineStatisticInfoDO.setStatisticVal(onlineStatisticInfoVO.getStatisticVal());
        onlineStatisticInfoDO.setStatisticDate(onlineStatisticInfoVO.getStatisticDate());
        if (Objects.nonNull(onlineStatisticInfoVO.getIsValid())) {
            onlineStatisticInfoDO.setIsValid((byte) (onlineStatisticInfoVO.getIsValid() ? 1 : 0));
        }
        if (Objects.nonNull(onlineStatisticInfoVO.getIsHeavy())) {
            onlineStatisticInfoDO.setIsHeavy((byte) (onlineStatisticInfoVO.getIsHeavy() ? 1 : 0));
        }
        return onlineStatisticInfoDO;
    }
}
