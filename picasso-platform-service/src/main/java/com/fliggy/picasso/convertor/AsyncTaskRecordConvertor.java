package com.fliggy.picasso.convertor;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.crowd.record.AsyncTaskExtInfo;
import com.fliggy.picasso.common.domain.crowd.record.AsyncTaskRecordVO;
import com.fliggy.picasso.common.enums.AsyncTaskRecordTypeEnum;
import com.fliggy.picasso.common.enums.AsyncTaskStatusEnum;
import com.fliggy.picasso.dao.AsyncTaskRecordDO;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class AsyncTaskRecordConvertor {

    public static AsyncTaskRecordVO convertToVO(AsyncTaskRecordDO asyncTaskRecordDO) {
        AsyncTaskRecordVO vo = new AsyncTaskRecordVO();
        vo.setId(asyncTaskRecordDO.getId());
        vo.setGmtCreate(asyncTaskRecordDO.getGmtCreate());
        vo.setGmtModified(asyncTaskRecordDO.getGmtModified());
        if (StringUtils.isNotBlank(asyncTaskRecordDO.getOperator())) {
            vo.setOperator(JSON.parseObject(asyncTaskRecordDO.getOperator(), Employee.class));
        }
        vo.setEntity(asyncTaskRecordDO.getEntity());
        if (StringUtils.isNotBlank(asyncTaskRecordDO.getEntityType())) {
            vo.setEntityType(AsyncTaskRecordTypeEnum.getByCode(asyncTaskRecordDO.getEntityType()));
        }
        vo.setTaskId(asyncTaskRecordDO.getTaskId());
        if (StringUtils.isNotBlank(asyncTaskRecordDO.getTaskStatus())) {
            vo.setTaskStatus(AsyncTaskStatusEnum.findByCode(asyncTaskRecordDO.getTaskStatus()));
        }
        vo.setErrMsg(asyncTaskRecordDO.getErrMsg());
        if (StringUtils.isNotBlank(asyncTaskRecordDO.getExtInfo())) {
            vo.setExtInfo(JSON.parseObject(asyncTaskRecordDO.getExtInfo(), AsyncTaskExtInfo.class));
        }
        return vo;
    }

    public static AsyncTaskRecordDO convertToDO(AsyncTaskRecordVO asyncTaskRecordVO) {
        AsyncTaskRecordDO asyncTaskRecordDO = new AsyncTaskRecordDO();
        asyncTaskRecordDO.setId(asyncTaskRecordVO.getId());
        asyncTaskRecordDO.setGmtCreate(asyncTaskRecordVO.getGmtCreate());
        asyncTaskRecordDO.setGmtModified(asyncTaskRecordVO.getGmtModified());
        if (Objects.nonNull(asyncTaskRecordVO.getOperator())) {
            asyncTaskRecordDO.setOperator(JSON.toJSONString(asyncTaskRecordVO.getOperator()));
        }
        asyncTaskRecordDO.setEntity(asyncTaskRecordVO.getEntity());
        if (Objects.nonNull(asyncTaskRecordVO.getEntityType())) {
            asyncTaskRecordDO.setEntityType(asyncTaskRecordVO.getEntityType().getCode());
        }
        asyncTaskRecordDO.setTaskId(asyncTaskRecordVO.getTaskId());
        if (Objects.nonNull(asyncTaskRecordVO.getTaskStatus())) {
            asyncTaskRecordDO.setTaskStatus(asyncTaskRecordVO.getTaskStatus().getCode());
        }
        asyncTaskRecordDO.setErrMsg(asyncTaskRecordVO.getErrMsg());
        if (Objects.nonNull(asyncTaskRecordVO.getExtInfo())) {
            asyncTaskRecordDO.setExtInfo(JSON.toJSONString(asyncTaskRecordVO.getExtInfo()));
        }
        return asyncTaskRecordDO;
    }
}
