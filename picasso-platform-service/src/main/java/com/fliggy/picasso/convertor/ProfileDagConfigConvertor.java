package com.fliggy.picasso.convertor;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.common.domain.profile.ProfileDagConfigVO;
import com.fliggy.picasso.common.domain.profile.ProfileDagNode;
import com.fliggy.picasso.common.enums.profile.ProfileDagStatusEnum;
import com.fliggy.picasso.dao.ProfileDagConfigDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
public class ProfileDagConfigConvertor {

    /**
     * do to vo
     * @param profileDagConfigDO
     * @return
     */
    public static ProfileDagConfigVO convertToVO(ProfileDagConfigDO profileDagConfigDO) {
        ProfileDagConfigVO profileDagConfigVO = new ProfileDagConfigVO();
        BeanUtils.copyProperties(profileDagConfigDO, profileDagConfigVO);
        profileDagConfigVO.setDagStatus(ProfileDagStatusEnum.getByCode(profileDagConfigDO.getDagStatus()));
        profileDagConfigVO.setDagNodes(JSON.parseArray(profileDagConfigDO.getDagNodes(), ProfileDagNode.class));
        return profileDagConfigVO;
    }

    /**
     * vo to do
     * @param profileDagConfigVO
     * @return
     */
    public static ProfileDagConfigDO convertToDO(ProfileDagConfigVO profileDagConfigVO) {
        ProfileDagConfigDO profileDagConfigDO = new ProfileDagConfigDO();
        BeanUtils.copyProperties(profileDagConfigVO, profileDagConfigDO);
        profileDagConfigDO.setDagStatus(profileDagConfigVO.getDagStatus().getCode());
        profileDagConfigDO.setDagNodes(JSON.toJSONString(profileDagConfigVO.getDagNodes()));
        return profileDagConfigDO;
    }
}
