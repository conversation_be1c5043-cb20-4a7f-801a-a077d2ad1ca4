package com.fliggy.picasso.service.analysis.domain;

import java.util.List;

import com.fliggy.picasso.analysis.AnalysisV2Meta.TagInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * 分析模板，单个分析配置
 * 目前不支持下钻，如果需要，可以增加parent配置，并在解析时将下钻任务拆分出去
 */
@Data
public class AnalysisTemplateSingleConfigBO {
    /**
     * 要分析的画像标签列表
     */
    private List<TagInfo> tagInfoList;

    /**
     * 排序方式 默认DESC
     */
    private String orderBy = "DESC";

    /**
     * limit count 默认100
     */
    private Integer limitCount = 100;
}
