package com.fliggy.picasso.service.feature;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class FeatureInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     *   创建时间
     */
    @Getter
    @Setter
    private Date gmtCreate;

    /**
     *   修改时间
     */
    @Getter
    @Setter
    private Date gmtModified;

    /**
     *   特征标识
     */
    @Getter
    @Setter
    private String code;

    /**
     *   特征名称
     */
    @Getter
    @Setter
    private String name;

    /**
     *   特征描述
     */
    @Getter
    @Setter
    private String description;

    /**
     *   标签ID
     */
    @Getter
    @Setter
    private Long labelId;

    /**
     *   创建人
     */
    @Getter
    @Setter
    private String creator;

    /**
     *   修改人
     */
    @Getter
    @Setter
    private String modifier;

    /**
     *   删除状态
     */
    @Getter
    @Setter
    private Byte deleted;
}