package com.fliggy.picasso.service.buildsql.parseCondition.label;

import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import com.fliggy.picasso.common.enums.label.LabelOperatorEnum;
import com.fliggy.picasso.hologres.HologresMetaService;
import com.google.common.base.Joiner;
import com.taobao.ateye.annotation.Switch;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.fliggy.picasso.common.Constant.BLANK;
import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.*;

/**
 * 多值类型标签表达式生成器
 */
@Component
public class MultiValueLabelExpressionGenerator extends BaseLabelExpressionGenerator {

    @Switch(name = "use_overlap", description = "是否使用overlap语法，默认是true")
    public boolean use_overlap = true;

    @Resource
    public HologresMetaService hologresMetaService;

    private static final String HOLO_OFFLINE_TABLE = "trip_picasso_taobao_user_profile";
    private static final String HOLO_ONLINE_TABLE = "trip_picasso_taobao_user_rt_profile";

    public MultiValueLabelExpressionGenerator(ExpressionGeneratorResolver expressionGeneratorResolver) {
        super(expressionGeneratorResolver);
    }

    @Override
    String buildExpression(String labelName, List<String> enumValues, LabelOperatorEnum operatorType) {
        String expression;
        if (use_overlap) {
            expression = useOverLapGrammer(labelName, enumValues, operatorType);
        } else {
            expression = unUseOverLapGrammer(labelName, enumValues, operatorType);
        }
        return expression;
    }


    public String useOverLapGrammer(String labelName, List<String> enumValues, LabelOperatorEnum operatorType) {
        StringBuilder result = new StringBuilder();
        if (operatorType == LabelOperatorEnum.NOTIN) {
            result.append(NOT);
        }
        String fieldType = acquireFieldType(labelName);
        if (fieldType != null && fieldType.equals("text")) {
            labelName = String.format("string_to_array(%s,',')", labelName);
        }

        result.append(labelName).append(" && ARRAY");
        List<String> enumCodeList = new ArrayList<>();
        String tempValue;
        for (String value : enumValues) {
            if (value.split(BLANK).length == 2) {
                // 枚举值中不包含空格
                tempValue = value.split(BLANK)[0];
            } else {
                tempValue = value.substring(0, value.length() / 2);
            }
            enumCodeList.add("'" + tempValue + "'");
        }

        result.append("[").append(Joiner.on(",").join(enumCodeList)).append("]");
        if (operatorType == LabelOperatorEnum.NOTIN) {
            result.append(OR).append(labelName).append(" is null");
        }
        result.append(RIGHT_PAREN);
        return result.toString();
    }

    private String unUseOverLapGrammer(String labelName, List<String> enumValues, LabelOperatorEnum operatorType) {
        StringBuilder result = new StringBuilder();
        if (operatorType == LabelOperatorEnum.NOTIN) {
            result.append(NOT);
        }

        List<String> enumCodeList = new ArrayList<>();
        String tempValue;
        for (String value : enumValues) {
            if (value.split(BLANK).length == 2) {
                // 枚举值中不包含空格
                tempValue = value.split(BLANK)[0];
            } else {
                tempValue = value.substring(0, value.length() / 2);
            }
            enumCodeList.add("ARRAY['" + tempValue + "'] <@ " + labelName);
        }

        result.append("(")
                .append(Joiner.on(" OR ").join(enumCodeList))
                .append(")");

        if (operatorType == LabelOperatorEnum.NOTIN) {
            result.append(OR).append(labelName).append(" is null");
        }
        result.append(RIGHT_PAREN);
        return result.toString();
    }

    public String acquireFieldType(String labelName) {
        String[] split = labelName.split("\\.");
        labelName = split[split.length - 1];
        List<HologresMetaService.HoloColumnMeta> offlineHoloColumnMetas = hologresMetaService.queryTableMeta(HOLO_OFFLINE_TABLE);
        for (HologresMetaService.HoloColumnMeta holoColumnMeta : offlineHoloColumnMetas) {
            if (labelName.equals(holoColumnMeta.getField())) {
                return holoColumnMeta.getType();
            }
        }
        List<HologresMetaService.HoloColumnMeta> onlineHoloColumnMetas = hologresMetaService.queryTableMeta(HOLO_ONLINE_TABLE);
        for (HologresMetaService.HoloColumnMeta holoColumnMeta : onlineHoloColumnMetas) {
            if (labelName.equals(holoColumnMeta.getField())) {
                return holoColumnMeta.getType();
            }
        }
        return null;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        expressionGeneratorResolver.register(LabelBizDataTypeEnum.MULTI_VALUE, this);
    }
}
