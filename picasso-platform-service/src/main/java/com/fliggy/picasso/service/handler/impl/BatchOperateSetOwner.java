package com.fliggy.picasso.service.handler.impl;

import com.fliggy.picasso.common.constants.CrowdLabelBatchOperateActionEnum;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.label.CrowdLabelBatchOperateDO;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.service.handler.BatchOperateHandler;
import com.fliggy.picasso.service.handler.BatchOperateHandlerResolver;
import com.fliggy.picasso.service.label.LabelInfoService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BatchOperateSetOwner implements BatchOperateHandler, InitializingBean {

    private final BatchOperateHandlerResolver batchOperateHandlerResolve;
    private final LabelInfoService labelInfoService;

    @Autowired
    public BatchOperateSetOwner(BatchOperateHandlerResolver resolver, LabelInfoService labelInfoService) {
        this.batchOperateHandlerResolve = resolver;
        this.labelInfoService = labelInfoService;
    }

    @Override
    public boolean process(CrowdLabelBatchOperateDO crowdLabelBatchOperateDO) {
        CrowdLabelBatchOperateActionEnum action = crowdLabelBatchOperateDO.getBatchOperateAction();
        List<Long> ids = crowdLabelBatchOperateDO.getLabelIdList();
        Employee employee = crowdLabelBatchOperateDO.getEmployee();
        if (employee == null) {
            throw new ParamErrorException("参数异常employee为null。");
        }

        return labelInfoService.batchUpdateOwner(ids, action, employee);

    }

    @Override
    public void afterPropertiesSet() {
        batchOperateHandlerResolve.register(CrowdLabelBatchOperateActionEnum.BATCH_SET_BIZ_OWNER, this);
        batchOperateHandlerResolve.register(CrowdLabelBatchOperateActionEnum.BATCH_SET_DATA_OWNER, this);
        batchOperateHandlerResolve.register(CrowdLabelBatchOperateActionEnum.BATCH_SET_QUALITY_OWNER, this);
    }
}
