package com.fliggy.picasso.service.approval.impl;

import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoBO;
import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoDO;
import com.fliggy.picasso.common.enums.bpms.ApprovalApplySceneEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.dao.ApprovalInfoParam;
import com.fliggy.picasso.mapper.picasso.ApprovalInfoDAO;
import com.fliggy.picasso.service.approval.ApprovalInfoService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ApprovalInfoServiceImpl implements ApprovalInfoService {

    @Resource
    private ApprovalInfoDAO approvalInfoDAO;

    @Override
    public int insert(ApprovalInfoBO approvalInfoBO) {
        return approvalInfoDAO.insert(approvalInfoBO.convertToDO());
    }

    @Override
    public int updateStatus(ApprovalInfoBO approvalInfoBO) {
        if (Objects.isNull(approvalInfoBO)) {
            throw new ParamErrorException("approvalInfo param invalid");
        }

        ApprovalInfoDO updateDO = new ApprovalInfoDO();
        updateDO.setId(approvalInfoBO.getId());
        updateDO.setGmtModified(new Date());
        updateDO.setApprovalStatus(approvalInfoBO.getApprovalStatus().name());
        return approvalInfoDAO.updateByPrimaryKeySelective(updateDO);
    }

    @Override
    public Boolean updateStatusWhileDiff(ApprovalInfoBO approvalInfoBO) {
        if (Objects.isNull(approvalInfoBO) || Objects.isNull(approvalInfoBO.getId())) {
            throw new ParamErrorException("approvalInfo param invalid");
        }

        ApprovalInfoDO originDO = approvalInfoDAO.selectByPrimaryKey(approvalInfoBO.getId());
        if (Objects.isNull(originDO)) {
            throw new ParamErrorException("approvalInfo not exist");
        }

        if (Objects.equals(originDO.getApprovalStatus(), approvalInfoBO.getApprovalStatus().name())) {
            return true;
        }
        return updateStatus(approvalInfoBO) > 0;
    }

    @Override
    public ApprovalInfoBO queryByApprovalId(String approvalId) {
        if (StringUtils.isBlank(approvalId)) {
            return null;
        }

        ApprovalInfoParam param = new ApprovalInfoParam();
        ApprovalInfoParam.Criteria criteria = param.createCriteria();
        criteria.andApprovalIdEqualTo(approvalId);
        List<ApprovalInfoDO> approvalInfoDOList = approvalInfoDAO.selectByParamWithBLOBs(param);
        if (CollectionUtils.isNullOrEmpty(approvalInfoDOList)) {
            return null;
        }
        return approvalInfoDOList.get(0).convertToBO();
    }

    @Override
    public List<ApprovalInfoBO> queryByApprovalIds(List<String> approvalIds) {
        if (CollectionUtils.isNullOrEmpty(approvalIds)) {
            return Lists.newArrayList();
        }

        ApprovalInfoParam param = new ApprovalInfoParam();
        ApprovalInfoParam.Criteria criteria = param.createCriteria();
        criteria.andApprovalIdIn(approvalIds);
        List<ApprovalInfoDO> approvalInfoDOList = approvalInfoDAO.selectByParamWithBLOBs(param);
        if (CollectionUtils.isNullOrEmpty(approvalInfoDOList)) {
            return Lists.newArrayList();
        }
        return approvalInfoDOList.stream().map(ApprovalInfoDO::convertToBO).collect(Collectors.toList());
    }

    @Override
    public List<ApprovalInfoBO> queryByEntityIdAndApplyScene(Long entityId, ApprovalApplySceneEnum applyScene) {
        if (Objects.isNull(entityId) || Objects.isNull(applyScene)) {
            return new ArrayList<>();
        }

        ApprovalInfoParam param = new ApprovalInfoParam();
        ApprovalInfoParam.Criteria criteria = param.createCriteria();
        criteria.andEntityIdEqualTo(entityId);
        criteria.andApplySceneEqualTo(applyScene.getCode());
        param.appendOrderByClause(ApprovalInfoParam.OrderCondition.ID, ApprovalInfoParam.SortType.DESC);
        List<ApprovalInfoDO> approvalInfoDOList = approvalInfoDAO.selectByParamWithBLOBs(param);
        if (CollectionUtils.isNullOrEmpty(approvalInfoDOList)) {
            return new ArrayList<>();
        }
        return approvalInfoDOList.stream().map(ApprovalInfoDO::convertToBO).collect(Collectors.toList());
    }

    @Override
    public ApprovalInfoBO queryLatestByEntityIdAndApplyScene(Long entityId, ApprovalApplySceneEnum applyScene) {
        if (Objects.isNull(entityId) || Objects.isNull(applyScene)) {
            return null;
        }

        List<ApprovalInfoBO> approvalInfoBOList = queryByEntityIdAndApplyScene(entityId, applyScene);
        if (CollectionUtils.isNullOrEmpty(approvalInfoBOList)) {
            return null;
        }
        return approvalInfoBOList.get(0);
    }

    @Override
    public List<ApprovalInfoBO> queryByEntityId(Long entityId) {
        if (Objects.isNull(entityId)) {
            return new ArrayList<>();
        }

        List<ApprovalInfoDO> approvalInfoDOList = approvalInfoDAO.queryByEntityId(entityId);
        return approvalInfoDOList.stream().map(ApprovalInfoDO::convertToBO).collect(Collectors.toList());
    }
}
