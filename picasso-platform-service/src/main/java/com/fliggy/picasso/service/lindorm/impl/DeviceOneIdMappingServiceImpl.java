package com.fliggy.picasso.service.lindorm.impl;

import com.alibaba.lindorm.client.dml.ColumnValue;
import com.alibaba.lindorm.client.dml.Row;
import com.alibaba.lindorm.client.exception.LindormException;
import com.fliggy.picasso.service.lindorm.column.OneIdMappingColumnEnum;
import com.fliggy.picasso.service.lindorm.domain.OneIdMappingDO;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.fliggy.picasso.service.lindorm.column.OneIdMappingColumnEnum.DECIDE_ONE_ID_REVERSE;

@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Service
public class DeviceOneIdMappingServiceImpl extends AbstractOneIdMappingServiceImpl{

    @Value("${lindorm.device.mapping.tableName}")
    private String tableName;

    public DeviceOneIdMappingServiceImpl() {
        super.setOneIdReverseLindormColumn(DECIDE_ONE_ID_REVERSE.getCode());
    }

    @Override
    public Set<String> idMappingToOneId(String sourceIdType, String sourceId) throws LindormException {
        super.setTableName(tableName);
        return super.idMappingToOneId(sourceIdType, sourceId);
    }

    @Override
    public String idMappingFromOneId(String oneId, String targetIdType) throws LindormException {
        super.setTableName(tableName);
        return super.idMappingFromOneId(oneId, targetIdType);
    }

    @Override
    public Set<String> idMapping(String sourceIdType, String sourceId, String targetIdType) throws LindormException {
        super.setTableName(tableName);
        return super.idMapping(sourceIdType, sourceId, targetIdType);
    }

    @Override
    public int batchUpsert(List<OneIdMappingDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }

        // 过滤主键不完整的数据
        records.removeIf(this::invalidPrimaryKey);

        // 转为lindorm需要的row
        List<Row> rows = batchConvertToRows(records);

        try {
            return tripUgdLindormServiceImpl.batchUpsert(tableName, rows);
        } catch (Exception e) {
            log.error("lindorm batch upsert error", e);
            return 0;
        }
    }

    @Override
    public int delete(OneIdMappingDO record) {
        if (invalidPrimaryKey(record)) {
            return 0;
        }

        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap.put(record.getOneIdType().getColumn(), record.getOneIdReverse());
        conditionMap.put(OneIdMappingColumnEnum.INDEX.getCode(), 1);
        conditionMap.put(OneIdMappingColumnEnum.KEY_TYPE.getCode(), record.getKeyType());
        try {
            return tripUgdLindormServiceImpl.delete(tableName, conditionMap);
        } catch (LindormException e) {
            log.error("lindorm delete error", e);
            return 0;
        }
    }

    @Override
    public int batchDelete(List<OneIdMappingDO> records) {
        AtomicInteger cnt = new AtomicInteger();
        records.forEach(record -> cnt.addAndGet(delete(record)));
        return cnt.get();
    }

    /**
     * 判断主键是否合法
     *
     * @param record 记录
     * @return 是否合法
     */
    private boolean invalidPrimaryKey(OneIdMappingDO record) {
        return Objects.isNull(record) || StringUtils.isBlank(record.getOneId())
                || StringUtils.isBlank(record.getKeyType());
    }

    /**
     * 将记录转换为Row
     *
     * @param record 记录
     * @return row
     */
    private Row convertToRow(OneIdMappingDO record) {
        if (Objects.isNull(record)) {
            return null;
        }

        try {
            return new Row().add(new ColumnValue(record.getOneIdType().getColumn(), record.getOneIdReverse()))
                    .add(new ColumnValue(OneIdMappingColumnEnum.INDEX.getCode(), 1))
                    .add(new ColumnValue(OneIdMappingColumnEnum.KEY_TYPE.getCode(), record.getKeyType()))
                    .add(new ColumnValue(OneIdMappingColumnEnum.KEY_VALUE.getCode(), record.getKeyValue()));
        } catch (Exception e) {
            log.error("DeviceOneIdMappingServiceImpl convertToRow exception, record={}", record, e);
            return null;
        }
    }

    /**
     * 将记录列表转换为Row列表
     *
     * @param records 记录列表
     * @return row列表
     */
    private List<Row> batchConvertToRows(List<OneIdMappingDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayListWithCapacity(0);
        }

        return records.stream().map(this::convertToRow).collect(Collectors.toList());
    }
}
