package com.fliggy.picasso.service.buildsql.parseCondition.table;

import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.entity.condition.GroupConditionDTO;

import java.util.List;

/**
 * 表对应表达式生成接口
 */
public interface TableExpressionGenerateService {

    /**
     * 生成表对应的表达式
     *
     * @param labelCrowdConditions
     * @return
     */
    List<String> generateTableExpression(LabelCrowdConditions labelCrowdConditions, List<GroupConditionDTO> groupConditionDTOS);

}
