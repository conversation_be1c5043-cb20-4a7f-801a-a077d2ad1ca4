package com.fliggy.picasso.service.approval.impl;

import com.fliggy.picasso.common.enums.bpms.ApprovalApplySceneEnum;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class ApprovalServiceRouter {

    private final Map<ApprovalApplySceneEnum, ? extends AbstractApprovalServiceImpl> approvalServiceMap;

    @Autowired
    public ApprovalServiceRouter(List<? extends AbstractApprovalServiceImpl> approvalServices) {
        approvalServiceMap = Maps.uniqueIndex(approvalServices, AbstractApprovalServiceImpl::getApplyScene);
    }

    public AbstractApprovalServiceImpl choose(ApprovalApplySceneEnum applyScene) {
        return approvalServiceMap.get(applyScene);
    }
}
