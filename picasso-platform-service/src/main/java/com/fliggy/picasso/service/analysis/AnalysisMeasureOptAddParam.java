package com.fliggy.picasso.service.analysis;

import java.util.Date;
import java.util.List;

import com.fliggy.olap.client.domain.ConditionParam;
import com.fliggy.olap.client.domain.OrderParam;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/3/25 下午3:46
 */
@Data
public class AnalysisMeasureOptAddParam {

    private Long analysisId;

    private List<String> dimensions;
    private List<String> measures;
    private ConditionParam filter;

    private List<OrderParam> orders;

    private Date start;
    private Date end;

    private int limit;
}
