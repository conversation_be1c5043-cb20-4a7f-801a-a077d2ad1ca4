package com.fliggy.picasso.service.analysis;

import java.util.List;

import com.aliyun.odps.OdpsException;
import com.fliggy.picasso.domain.AnalysisTemplateDO;
import com.fliggy.picasso.query.AnalysisTemplateQuery;
import com.fliggy.picasso.service.analysis.domain.AnalysisTemplateAnalysisRequest;
import com.fliggy.picasso.service.analysis.domain.AnalysisTemplateVO;
import com.github.pagehelper.PageInfo;

public interface AnalysisTemplateService {

    /**
     * 增
     *
     * @param record record
     * @return 主键
     */
    Long insert(AnalysisTemplateDO record);

    /**
     * 主键删除
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 改
     *
     * @param record record
     * @return 影响行数
     */
    int updateById(AnalysisTemplateDO record);

    /**
     * 主键查询
     *
     * @param id 主键
     * @return 查询结果
     */
    AnalysisTemplateDO queryById(Long id);

    /**
     * 条件查询
     *
     * @param query 查询条件
     * @return 查询结果
     */
    PageInfo<AnalysisTemplateVO> pageQuery(AnalysisTemplateQuery query);

    /**
     * 分析
     * @param request 请求参数
     * @return 下发并正确执行任务数量
     */
    int analysis(AnalysisTemplateAnalysisRequest request) throws OdpsException;
}
