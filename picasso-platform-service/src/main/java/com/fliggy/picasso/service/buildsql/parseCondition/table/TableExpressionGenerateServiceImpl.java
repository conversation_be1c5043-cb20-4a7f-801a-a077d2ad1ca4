package com.fliggy.picasso.service.buildsql.parseCondition.table;

import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.entity.condition.GroupConditionDTO;
import com.fliggy.picasso.entity.condition.TableConditionDTO;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.fliggy.picasso.common.constants.HoloConstants.HOLO_PROJECT;

/**
 * 表对应表达式生成接口实现类
 */
@Component
public class TableExpressionGenerateServiceImpl implements TableExpressionGenerateService {

    @Override
    public List<String> generateTableExpression(LabelCrowdConditions labelCrowdConditions, List<GroupConditionDTO> groupCondition) {
        List<String> result = new ArrayList<>();
        List<LabelCrowdConditions.LabelGroup> group = labelCrowdConditions.getGroup();
        for (int i = 0; i < group.size(); i++) {
            String tableExpression;
            GroupConditionDTO groupConditionDTO = groupCondition.get(i);
            List<TableConditionDTO> tableConditions = groupConditionDTO.getTableConditions();
            //拼接表对应的SQL表达式
            if (tableConditions.size() == 1) {
                TableConditionDTO tableConditionDTO = tableConditions.get(0);
                String lastPartition = tableConditionDTO.getLastPartition();
                String olapTable = HOLO_PROJECT + tableConditionDTO.getTableName();
                if (tableConditionDTO.useMaxPt()) {
                    tableExpression = String.format(" %s where ds = max_pt('%s')", olapTable, olapTable);
                } else {
                    tableExpression = String.format(" %s where ds = '%s'", olapTable, lastPartition);
                }
            } else {
                tableExpression = buildMultiTableExpression(tableConditions);
            }
            result.add(tableExpression);
        }
        return result;
    }

    private String buildMultiTableExpression(List<TableConditionDTO> tableConditions) {
        TableConditionDTO firstTableCondition = tableConditions.get(0);
        String firstTableName = firstTableCondition.getTableName();
        String firstTableDs = firstTableCondition.getLastPartition();
        String firstTableAlias = firstTableCondition.getAlias();
        String firstTablePrimaryKey = firstTableCondition.getPrimaryKey();
        String tableExpression = String.format("%s as %s", firstTableName, firstTableAlias);
        String whereExpression;
        if (firstTableCondition.useMaxPt()) {
            whereExpression = String.format(" where %s.ds=max_pt('%s')", firstTableAlias, firstTableName);
        } else {
            whereExpression = String.format(" where %s.ds='%s'", firstTableAlias, firstTableDs);
        }

        for (int i = 1; i < tableConditions.size(); i++) {
            TableConditionDTO tableConditionDTO = tableConditions.get(i);
            String alias = tableConditionDTO.getAlias();
            String tableName = HOLO_PROJECT + tableConditionDTO.getTableName();
            String tableNameAlias = String.format("%s as %s", tableName, alias);
            String tableDs = tableConditionDTO.getLastPartition();
            String key = tableConditionDTO.getPrimaryKey();
            tableExpression = String.format("( %s join %s on %s = %s )",
                    tableExpression, tableNameAlias, firstTableAlias + "." + firstTablePrimaryKey, alias + "." + key);
            if (tableConditionDTO.useMaxPt()) {
                whereExpression = String.format("%s and %s.ds=max_pt('%s') ", whereExpression, alias, tableName);
            } else {
                whereExpression = String.format("%s and %s.ds='%s' ", whereExpression, alias, tableDs);
            }
        }

        return String.format("%s %s", tableExpression, whereExpression);
    }

    /**
     * 获取圈选条件中一个group对应所有表相关条件
     *
     * @param labels
     * @param labelAndPhysicalProfileInfo
     * @return
     */
    private List<PhysicalProfileInfoDTO> getAllTableInfo(List<LabelCrowdConditions.LabelValue> labels,
                                                         Map<String, List<PhysicalProfileInfoDTO>> labelAndPhysicalProfileInfo) {
        List<PhysicalProfileInfoDTO> tableConditions = new ArrayList<>();
        //多个标签对应了几张表
        for (LabelCrowdConditions.LabelValue label : labels) {
            String name = label.getName();
            List<PhysicalProfileInfoDTO> physicalProfileInfoDTOS = labelAndPhysicalProfileInfo.get(name);
            if (physicalProfileInfoDTOS == null || physicalProfileInfoDTOS.isEmpty()) {
                throw new RuntimeException(name + "对应的物理表没有查到！");
            }
            PhysicalProfileInfoDTO physicalProfileInfoDTO = physicalProfileInfoDTOS.get(0);
            Boolean tableHasAlreadyExist = tableHasAlreadyExist(physicalProfileInfoDTO, tableConditions);
            if (!tableHasAlreadyExist) {
                tableConditions.add(physicalProfileInfoDTO);
            }
        }
        return tableConditions;
    }

    /**
     * 判断表信息是否已经在集合中
     *
     * @param physicalProfileInfoDTO
     * @param tableConditions
     * @return
     */
    private Boolean tableHasAlreadyExist(PhysicalProfileInfoDTO physicalProfileInfoDTO, List<PhysicalProfileInfoDTO> tableConditions) {
        if (tableConditions.isEmpty()) {
            return false;
        }

        for (int i = 0; i < tableConditions.size(); i++) {
            PhysicalProfileInfoDTO tableCondition = tableConditions.get(i);
            if (tableCondition.getOlapTable().equals(physicalProfileInfoDTO.getOlapTable())) {
                return true;
            }
        }
        return false;
    }


}
