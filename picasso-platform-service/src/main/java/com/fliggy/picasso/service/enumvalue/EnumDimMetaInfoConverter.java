package com.fliggy.picasso.service.enumvalue;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.common.constants.label.LabelEnumValueSourceTypeEnum;
import com.fliggy.picasso.common.domain.label.enumvalue.EnumDimMetaInfoDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class EnumDimMetaInfoConverter {

    /**
     * DTO模型转换成DO模型
     *
     * @param enumDimMetaInfoDTO
     */
    public EnumDimMetaInfoDO convertFromDTO(EnumDimMetaInfoDTO enumDimMetaInfoDTO) {
        EnumDimMetaInfoDO result = new EnumDimMetaInfoDO();
        BeanUtils.copyProperties(enumDimMetaInfoDTO, result);

        if (enumDimMetaInfoDTO.getCreator() != null) {
            result.setCreator(JSON.toJSONString(enumDimMetaInfoDTO.getCreator()));
        }

        if (enumDimMetaInfoDTO.getOperator() != null) {
            result.setOperator(JSON.toJSONString(enumDimMetaInfoDTO.getOperator()));
        }

        if (enumDimMetaInfoDTO.getType() != null) {
            result.setType(enumDimMetaInfoDTO.getType().name());
        }
        return result;
    }

    /**
     * DO模型转换成DTO模型
     *
     * @param enumDimMetaInfoDO
     */
    public EnumDimMetaInfoDTO convertFromDO(EnumDimMetaInfoDO enumDimMetaInfoDO) {
        EnumDimMetaInfoDTO enumDimMetaInfoDTO = new EnumDimMetaInfoDTO();
        BeanUtils.copyProperties(enumDimMetaInfoDO, enumDimMetaInfoDTO);

        if (StringUtils.isNotEmpty(enumDimMetaInfoDO.getType())) {
            enumDimMetaInfoDTO.setType(LabelEnumValueSourceTypeEnum.fromName(enumDimMetaInfoDO.getType()));
        }

        return enumDimMetaInfoDTO;
    }
}