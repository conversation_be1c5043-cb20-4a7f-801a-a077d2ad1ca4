package com.fliggy.picasso.service.analysis;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.analysis.*;
import com.fliggy.picasso.common.enums.DeletedEnum;
import com.fliggy.picasso.common.utils.NumberUtils;
import com.fliggy.picasso.dao.AnalysisV2DO;
import com.fliggy.picasso.dao.AnalysisV2Param.Criteria;
import com.fliggy.picasso.dao.AnalysisV2Param;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.fliggy.picasso.employee.UserContext;
import com.fliggy.picasso.employee.UserContextDTO;
import com.fliggy.picasso.entity.template.OdpsAnalysisChildSelectParam;
import com.fliggy.picasso.entity.template.OdpsAnalysisParentSelectParam;
import com.fliggy.picasso.exception.AnalysisException;
import com.fliggy.picasso.freemarker.TemplateEnum;
import com.fliggy.picasso.mapper.picasso.AnalysisV2DAO;
import com.fliggy.picasso.utils.PicassoSpringContextUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.mtop.commons.utils.CollectionUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class AnalysisV2ServiceImpl implements AnalysisV2Service {
    @Autowired
    private AnalysisV2DAO analysisV2DAO;

    @Autowired
    private AnalysisV2Converter analysisV2Converter;

    /**
     * 根据参数统计总数
     *
     * @param param
     */
    @Override
    public long count(AnalysisV2Parameter param) {
        AnalysisV2Param analysisV2Param = new AnalysisV2Param();
        Criteria criteria = analysisV2Param.createCriteria();
        buildCriteria(criteria, param);
        return analysisV2DAO.countByParam(analysisV2Param);
    }

    /**
     * 根据参数查询
     *
     * @param param
     */
    @Override
    public AnalysisV2DTO find(AnalysisV2Parameter param) {
        AnalysisV2Param analysisV2Param = new AnalysisV2Param();
        Criteria criteria = analysisV2Param.createCriteria();
        buildCriteria(criteria, param);
        List<AnalysisV2DO> list = analysisV2DAO.selectByParam(analysisV2Param);
        if (null == list || list.isEmpty()) {
            return null;
        }
        return analysisV2Converter.convertFromDO(list.get(0));
    }


    @Override
    public List<AnalysisV2DTO> listBrief(AnalysisV2Parameter param) {
        AnalysisV2Param analysisV2Param = new AnalysisV2Param();
        Criteria criteria = analysisV2Param.createCriteria();
        buildCriteria(criteria, param);
        analysisV2Param.appendOrderByClause(AnalysisV2Param.OrderCondition.GMTCREATE, AnalysisV2Param.SortType.DESC);
        List<AnalysisV2DO> list = analysisV2DAO.selectListByParam(analysisV2Param);
        if (null == list || list.isEmpty()) {
            return new ArrayList<>();
        }
        List<AnalysisV2DTO> result = new ArrayList<>();
        for (AnalysisV2DO record : list) {
            AnalysisV2DTO analysisV2DTO = analysisV2Converter.convertFromDO(record);
            result.add(analysisV2DTO);
        }
        return result;
    }

    /**
     * 列表查询
     *
     * @param param
     */
    @Override
    public List<AnalysisV2DTO> list(AnalysisV2Parameter param) {
        AnalysisV2Param analysisV2Param = new AnalysisV2Param();
        Criteria criteria = analysisV2Param.createCriteria();
        buildCriteria(criteria, param);
        analysisV2Param.appendOrderByClause(AnalysisV2Param.OrderCondition.GMTCREATE, AnalysisV2Param.SortType.DESC);
        List<AnalysisV2DO> list = analysisV2DAO.selectByParam(analysisV2Param);
        if (null == list || list.isEmpty()) {
            return new ArrayList<>();
        }
        List<AnalysisV2DTO> result = new ArrayList<>();
        for (AnalysisV2DO record : list) {
            AnalysisV2DTO analysisV2DTO = analysisV2Converter.convertFromDO(record);
            result.add(analysisV2DTO);
        }
        return result;
    }

    private void buildCriteria(Criteria criteria, AnalysisV2Parameter param) {
        if (StringUtils.isNotBlank(param.getCreator())) {
            criteria.andCreatorLike("%" + param.getCreator() + "%");
        }
        if (Objects.nonNull(param.getId())) {
            criteria.andIdEqualTo(param.getId());
        }
        if (Objects.nonNull(param.getCrowdId())) {
            criteria.andCrowdIdEqualTo(param.getCrowdId());
        }
        if (StringUtils.isNotBlank(param.getName())) {
            criteria.andNameLike("%" + param.getName() + "%");
        }
        if (StringUtils.isNotBlank(param.getCrowdName())) {
            criteria.andCrowdNameLike("%" + param.getCrowdName() + "%");
        }
        if (StringUtils.isNotBlank(param.getStatus())) {
            criteria.andStatusEqualTo(param.getStatus());
        }
        if (StringUtils.isNotBlank(param.getCreator())) {
            criteria.andCreatorLike("%" + param.getCreator() + "%");
        }
        // parentId 为0
        criteria.andParentIdEqualTo(Objects.isNull(param.getParentId()) ? 0L : param.getParentId());
        // idDel = 0
        criteria.andIsDeletedEqualTo((byte) 0);
    }

    /**
     * 创建
     *
     * @param analysisV2CreateRequest
     * @return
     */
    @Transactional
    @Override
    public Long create(AnalysisV2CreateRequest analysisV2CreateRequest) {
        AnalysisV2DO record = new AnalysisV2DO();
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setParentId(analysisV2CreateRequest.getParentId());
        record.setName(analysisV2CreateRequest.getName());
        record.setCrowdName(analysisV2CreateRequest.getCrowdName());
        record.setDescription(Objects.isNull(analysisV2CreateRequest.getDescription()) ? "" : analysisV2CreateRequest.getDescription());
        record.setType(AnalysisV2TypeEnum.INSIGHT_ANALYSIS.getType());
        record.setStatus(AnalysisV2StatusEnum.INIT.getStatus());
        String creator = getCreator();
        record.setCreator(StringUtils.isNotBlank(creator) ? creator : analysisV2CreateRequest.getCreator());
        record.setIsDeleted(DeletedEnum.fromBoolean(false));
        record.setCrowdId(analysisV2CreateRequest.getCrowdId());
        record.setMeta(JSON.toJSONString(analysisV2CreateRequest.getMeta()));
        record.setFailTimes((byte) 0);
        if (analysisV2CreateRequest.getParentId() != 0 && Objects.isNull(analysisV2CreateRequest.getParentMetaAndValue())) {
            // 创建集下的父分析
            AnalysisV2DO parentAnaDO = findById(analysisV2CreateRequest.getParentId());
            record.setCrowdId(parentAnaDO.getCrowdId());
            record.setCrowdName(parentAnaDO.getCrowdName());
            record.setName(analysisV2CreateRequest.getParentId() + "-父分析");
            record.setDescription(analysisV2CreateRequest.getParentId() + "-父分析");
            record.setParentMeta(JSON.toJSONString(analysisV2CreateRequest.getParentMetaAndValue()));
        }
        if (analysisV2CreateRequest.getParentId() != 0 && Objects.nonNull(analysisV2CreateRequest.getParentMetaAndValue())) {
            // 如果是下钻则补下钻的数据
            AnalysisV2DO parentAnaDO = findById(analysisV2CreateRequest.getParentId());
            record.setCrowdId(parentAnaDO.getCrowdId());
            record.setCrowdName(parentAnaDO.getCrowdName());
            record.setName(analysisV2CreateRequest.getParentId() + "-子分析");
            record.setDescription(analysisV2CreateRequest.getParentId() + "-子分析");
            // 判断下钻的柱不能是未知
            String nestedValue = analysisV2CreateRequest.getParentMetaAndValue().getTagInfoList().get(0).getValue();
            if ("未知".equals(nestedValue) || "".equals(nestedValue)) {
                throw new AnalysisException("下钻的柱子值不能是未知或者空.", null);
            }
            record.setParentMeta(JSON.toJSONString(analysisV2CreateRequest.getParentMetaAndValue()));
        }
        int insert = analysisV2DAO.insert(record);
        if (insert != 1) {
            throw new AnalysisException("初始化保存db异常.", null);
        }
        // 创建分析集的话返回
        if (analysisV2CreateRequest.getParentId() == 0) {
            return record.getId();
        }
        IAnalysisV2Handler<AnalysisV2DO, Boolean, AnalysisV2StatusEnum> iAnalysisV2Handler = AnalysisV2HandlerFactory.getIAnalysisV2Handler(AnalysisV2StatusEnum.INIT);
        Boolean initResult = iAnalysisV2Handler.handle(record);
        if (initResult) {
            return record.getId();
        } else {
            throw new AnalysisException("初始化异常.", null);
        }
    }

    @Override
    public Long insert(AnalysisV2DO record) {
        int num = analysisV2DAO.insert(record);
        if (num > 0) {
            return record.getId();
        } else {
            return null;
        }
    }

    /**
     * 从cookie中获取创建人
     * @return
     */
    private static String getCreator() {
        // 与前端保持一致
        UserContextDTO userContextDTO = UserContext.getBucUser().orElse(null);
        if (Objects.isNull(userContextDTO)){
            return "";
        }
        // wb同学花名为空则取工号
        if (StringUtils.isBlank(userContextDTO.getNickNameCN())){
            return userContextDTO.getEmpId();
        }
        return userContextDTO.getNickNameCN();
    }

    @AteyeInvoker(description = "createAnalysisV2Ateye", paraDesc = "parentId&name&crowdName&description&crowdId&meta&parentMeta")
    public void createAnalysisV2Ateye(Long parentId, String name, String crowdName, String description, Long crowdId, String meta, String parentMeta) {
        AnalysisV2CreateRequest request = new AnalysisV2CreateRequest();
        request.setName(name);
        request.setCrowdName(crowdName);
        request.setDescription(description);
        request.setCreator("长弘");
        request.setCrowdId(crowdId);
        request.setMeta(JSON.parseObject(meta, AnalysisV2Meta.class));
        request.setParentId(parentId);
        request.setParentMetaAndValue(JSON.parseObject(parentMeta, AnalysisV2Meta.class));
        Long id = create(request);
        IAnalysisV2Handler<AnalysisV2DO, Boolean, AnalysisV2StatusEnum> iAnalysisV2Handler = AnalysisV2HandlerFactory.getIAnalysisV2Handler(AnalysisV2StatusEnum.INIT);
        AnalysisV2Service analysisV2Service = PicassoSpringContextUtils.getBeanBySimpleClassName(this.getClass().getSimpleName());
        iAnalysisV2Handler.handle(analysisV2Service.findById(id));
    }

    @AteyeInvoker(description = "删除测试数据", paraDesc = "ids")
    public boolean delTestData(String ids) {
        String[] idList = ids.split(",");
        for (String s : idList) {
            analysisV2DAO.deleteByPrimaryKey(Long.parseLong(s));
        }
        return true;
    }

    public static void main(String[] args) {
        List<String> list = new ArrayList<>();
        for (int i = 10000; i < 10088; i++) {
            list.add(String.valueOf(i));
        }
        System.out.println(String.join(",", list));
    }

    /**
     * 修改
     *
     * @param dto
     * @param param
     */
    @Override
    public void updateSelective(AnalysisV2DTO dto, AnalysisV2Parameter param) {
        AnalysisV2DO record = analysisV2Converter.convertFromDTO(dto);
        record.setGmtModified(new Date());
        AnalysisV2Param analysisV2Param = new AnalysisV2Param();
        Criteria criteria = analysisV2Param.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        analysisV2DAO.updateByParamSelective(record, analysisV2Param);
    }

    /**
     * 修改分析名称
     *
     * @param analysisV2CreateRequest
     * @return
     */
    @Override
    public int updateSelectiveById(AnalysisV2CreateRequest analysisV2CreateRequest) {
        AnalysisV2DO record = new AnalysisV2DO();
        record.setId(analysisV2CreateRequest.getId());
        // 目前只支持修改分析名称
        record.setName(analysisV2CreateRequest.getName());
        record.setCreator(analysisV2CreateRequest.getCreator());
        return analysisV2DAO.updateByPrimaryKeySelective(record);
    }

    /**
     * 通过对比旧状态来修改
     */
    @Override
    public int updateSelective(AnalysisV2DO analysisV2DO, String oldStatus) {
        AnalysisV2Param analysisV2Param = new AnalysisV2Param();
        Criteria criteria = analysisV2Param.createCriteria();
        if (Objects.nonNull(analysisV2DO.getId())) {
            criteria.andIdEqualTo(analysisV2DO.getId());
        }
        criteria.andStatusEqualTo(oldStatus);
        analysisV2DO.setGmtModified(new Date());
        return analysisV2DAO.updateByParamSelective(analysisV2DO, analysisV2Param);
    }

    @Override
    public AnalysisV2DO findById(Long id) {
        return analysisV2DAO.selectByPrimaryKey(id);
    }

    @Override
    public AnalysisV2DTO findDtoById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }

        AnalysisV2DO analysisV2DO = findById(id);
        if (Objects.isNull(analysisV2DO)) {
            return null;
        }
        return analysisV2Converter.convertFromDO(analysisV2DO);
    }

    @Override
    public List<AnalysisV2DTO> findByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        AnalysisV2Param analysisV2Param = new AnalysisV2Param();
        Criteria criteria = analysisV2Param.createCriteria();
        criteria.andIdIn(ids);
        List<AnalysisV2DO> analysisV2DOList = analysisV2DAO.selectByParam(analysisV2Param);
        if (CollectionUtils.isEmpty(analysisV2DOList)) {
            return new ArrayList<>();
        }
        return analysisV2DOList.stream().map(analysisV2DO -> analysisV2Converter.convertFromDO(analysisV2DO)).collect(Collectors.toList());
    }

    public List<AnalysisV2DO> findByParentId(Long parentId) {
        AnalysisV2Param analysisV2Param = new AnalysisV2Param();
        Criteria criteria = analysisV2Param.createCriteria();
        criteria.andParentIdEqualTo(parentId);
//        analysisV2Param.appendOrderByClause(AnalysisV2Param.OrderCondition.GMTCREATE, AnalysisV2Param.SortType.DESC);
        List<AnalysisV2DO> list = analysisV2DAO.selectByParam(analysisV2Param);
        if (null == list || list.isEmpty()) {
            return new ArrayList<>();
        }
        return list;
    }

    public List<AnalysisV2DO> findSelfAndChildById(Long id) {
        List<AnalysisV2DO> result = new ArrayList<>();
        return doFindSelfAndChildById(id, result);
    }


    /**
     * 根据分析集id查集下面的所有分析
     *
     * @param id
     * @return
     */
    public List<AnalysisV2DTO> findByPoolId(Long id) {
        List<AnalysisV2DO> result = new ArrayList<>();
        List<AnalysisV2DTO> resultDTO = new ArrayList<>();
        List<AnalysisV2DO> andSetByParentId = findAndSetByParentId(id, result);
        if (CollectionUtils.isEmpty(andSetByParentId)) {
            return resultDTO;
        }
        andSetByParentId = andSetByParentId.stream().filter(p -> p.getIsDeleted().intValue() == 0).collect(Collectors.toList());
        for (AnalysisV2DO record : andSetByParentId) {
            AnalysisV2DTO analysisV2DTO = analysisV2Converter.convertFromDO(record);
            resultDTO.add(analysisV2DTO);
        }
        return resultDTO;
    }

    public List<AnalysisV2DO> findAndSetByParentId(Long id, List<AnalysisV2DO> analysisV2DOList) {
        // 如果当前分析带下钻超过100个 报异常
        if (CollectionUtils.isNotEmpty(analysisV2DOList) && analysisV2DOList.size() > 100) {
            throw new AnalysisException("分析带下钻已超过100个", null);
        }
        // 查询parentId = id 的列表
        List<AnalysisV2DO> byParentId = findByParentId(id);
        if (CollectionUtils.isEmpty(byParentId)) {
            return new ArrayList<>(); // 这里一定要返回空 轻易别改
        }
        analysisV2DOList.addAll(byParentId);
        // 遍历列表，查所有的下钻分析
        for (AnalysisV2DO analysisV2DO : byParentId) {
            findAndSetByParentId(analysisV2DO.getId(), analysisV2DOList);
        }
        return analysisV2DOList;
    }

    private List<AnalysisV2DO> doFindSelfAndChildById(Long id, List<AnalysisV2DO> analysisV2DOList) {
        AnalysisV2DO parentAnalysisV2DO = analysisV2DAO.selectByPrimaryKey(id);
        if (Objects.isNull(parentAnalysisV2DO) || Objects.isNull(parentAnalysisV2DO.getIsDeleted())
                || parentAnalysisV2DO.getIsDeleted() == 1) {
            return analysisV2DOList;
        }
        analysisV2DOList.add(parentAnalysisV2DO);
        List<AnalysisV2DO> nestedAnaById = findNestedAnaById(id);
        if (CollectionUtil.isEmpty(nestedAnaById)) {
            return analysisV2DOList;
        }
        // 继续遍历下钻的分析是否有子分析
        for (AnalysisV2DO analysisV2DO : nestedAnaById) {
            doFindSelfAndChildById(analysisV2DO.getId(), analysisV2DOList);
        }
        return analysisV2DOList;
    }

    /**
     * 查询当前分析的下钻分析列表
     *
     * @param id
     * @return
     */
    private List<AnalysisV2DO> findNestedAnaById(Long id) {
        AnalysisV2Param analysisV2Param = new AnalysisV2Param();
        Criteria criteria = analysisV2Param.createCriteria();
        criteria.andParentIdEqualTo(id);
        List<AnalysisV2DO> list = analysisV2DAO.selectByParam(analysisV2Param);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }


    @Override
    public AnalysisV2HandleParam initAnalysis(AnalysisV2DO analysisV2DO) {

        // 判断是主分析还是子分析
        Long parentId = analysisV2DO.getParentId();
        boolean isParentAnalysis = StringUtils.isEmpty(analysisV2DO.getParentMeta()) || "null".equals(analysisV2DO.getParentMeta());
        String meta = analysisV2DO.getMeta();
        AnalysisV2Meta analysisV2Meta = JSON.parseObject(meta, AnalysisV2Meta.class);
        List<String> lableName = analysisV2Meta.getTagInfoList().stream().map(AnalysisV2Meta.TagInfo::getName).collect(Collectors.toList());
        String columns = String.join(",", lableName);
        Long crowdId = analysisV2DO.getCrowdId();
        Integer limitCount = analysisV2Meta.getLimitCount();
        AnalysisV2HandleParam analysisV2HandleParam;
        if (isParentAnalysis) {

            OdpsAnalysisParentSelectParam odpsParam = OdpsAnalysisParentSelectParam.builder()
                    .tagNames(columns)
                    .crowdId(String.valueOf(crowdId))
                    .limitCount(String.valueOf(limitCount))
                    .sc(analysisV2Meta.getOrderBy())
                    .build();
            analysisV2HandleParam = AnalysisV2HandleParam.builder()
                    .analysisV2DO(analysisV2DO)
                    .templateEnum(TemplateEnum.ODPS_ANALYSIS_PARENT_SELECT)
                    .templateParam(odpsParam)
                    .build();
        } else {
            AnalysisV2Meta parentMetaAndValue = JSON.parseObject(analysisV2DO.getParentMeta(), AnalysisV2Meta.class);
            if (Objects.isNull(parentMetaAndValue) || CollectionUtil.isEmpty(parentMetaAndValue.getTagInfoList())
                    || null != parentMetaAndValue.getTagInfoList().stream().filter(p -> StringUtils.isBlank(p.getValue())).findAny().orElse(null)) {
                throw new AnalysisException("下钻分析时父分析结构必须传值", null);
            }
            // 查询父的tagList
            AnalysisV2DO parentAnalysisDO = findById(parentId);
            String parentAnalysisDOMeta = parentAnalysisDO.getMeta();
            AnalysisV2Meta parentMeta = JSON.parseObject(parentAnalysisDOMeta, AnalysisV2Meta.class);
            // 父分析的tag校验
            boolean checkResult = checkParentTagIsMatch(parentMetaAndValue, parentMeta);
            if (!checkResult) {
                throw new AnalysisException("下钻分析时传入的父分析结构与原结构不符", null);
            }
            OdpsAnalysisChildSelectParam childParam = OdpsAnalysisChildSelectParam.builder()
                    .childTagNames(columns)
                    .crowdId(String.valueOf(crowdId))
                    .limitCount(String.valueOf(limitCount))
                    .sc(analysisV2Meta.getOrderBy())
                    .parentTagList(parentMetaAndValue.getTagInfoList())
                    .build();
            analysisV2HandleParam = AnalysisV2HandleParam.builder()
                    .analysisV2DO(analysisV2DO)
                    .templateEnum(TemplateEnum.ODPS_ANALYSIS_CHILD_SELECT)
                    .templateParam(childParam)
                    .build();
        }
        return analysisV2HandleParam;
    }


    /**
     * 下钻分析 父子结构校验
     *
     * @param parentMetaAndValue
     * @param parentMeta
     * @return
     */
    private boolean checkParentTagIsMatch(AnalysisV2Meta parentMetaAndValue, AnalysisV2Meta parentMeta) {

        if (Objects.isNull(parentMetaAndValue) || Objects.isNull(parentMeta)) {
            return false;
        }
        List<AnalysisV2Meta.TagInfo> inputTagInfo = parentMetaAndValue.getTagInfoList();
        List<AnalysisV2Meta.TagInfo> dbTagInfo = parentMeta.getTagInfoList();
        // 判断字段多少
        if (inputTagInfo.size() != dbTagInfo.size()) {
            return false;
        }
        // 判断字段是否相等
        Map<String, AnalysisV2Meta.TagInfo> dbTagInfoMap = dbTagInfo.stream().collect(Collectors.toMap(AnalysisV2Meta.TagInfo::getName, Function.identity()));
        AnalysisV2Meta.TagInfo tagInfo = inputTagInfo.stream().filter(p -> !dbTagInfoMap.containsKey(p.getName())).findFirst().orElse(null);
        return Objects.isNull(tagInfo);
    }

    @Override
    public AnalysisV2HandleResult handleAnalysisV2(AnalysisV2DO analysisV2DO) {
        AnalysisV2StatusEnum byStatus = AnalysisV2StatusEnum.getByStatus(analysisV2DO.getStatus());
        IAnalysisV2Handler iAnalysisV2Handler = AnalysisV2HandlerFactory.getIAnalysisV2Handler(byStatus);
        AnalysisV2HandleResult handle = (AnalysisV2HandleResult) iAnalysisV2Handler.handle(analysisV2DO);
        return handle;
    }

    @Override
    public List<AnalysisV2DO> selectRecordByStatusAndFailTimes(String status, Integer failTimes) {
        AnalysisV2Param analysisV2Param = new AnalysisV2Param();
        Criteria criteria = analysisV2Param.createCriteria();
        criteria.andStatusEqualTo(status);
        criteria.andFailTimesLessThanWhenPresent((byte) failTimes.intValue());
        criteria.andParentIdNotEqualTo(0L);
        List<AnalysisV2DO> analysisV2DOS = analysisV2DAO.selectByParam(analysisV2Param);
        if (CollectionUtils.isEmpty(analysisV2DOS)) {
            return new ArrayList<>();
        }
        return analysisV2DOS;
    }

    @Override
    @AteyeInvoker(description = "洞察分析重跑", paraDesc = "id")
    public Boolean reRun(Long id) {
        // 重跑
        AnalysisV2DO analysisV2DO = findById(id);
        // 如果当前状态是运行中或者初始化中，则跑下分析任务
        // 如果重跑完成将状态重置为INIT，清除失败次数 = 0
        if (AnalysisV2StatusEnum.FINISH.getStatus().equals(analysisV2DO.getStatus())) {
            analysisV2DO.setStatus(AnalysisV2StatusEnum.INIT.getStatus());
            analysisV2DO.setFailTimes((byte) 0);
            updateSelective(analysisV2DO, AnalysisV2StatusEnum.FINISH.getStatus());
        }
        AnalysisV2HandlerFactory.getIAnalysisV2Handler(AnalysisV2StatusEnum.getByStatus(analysisV2DO.getStatus())).handle(analysisV2DO);
        return true;
    }

    @Override
    public Boolean delById(Long id) {
        AnalysisV2DO analysisV2DO = findById(id);
        analysisV2DO.setIsDeleted((byte) 1);
        updateSelective(analysisV2DO, analysisV2DO.getStatus());
        return true;
    }

    /**
     * 初始化常规分析
     *
     * @param rootDO 根节点信息
     * @return record
     */
    public static AnalysisV2DO initNormalRecord(AnalysisV2DO rootDO, AnalysisV2Meta meta) {
        if (Objects.isNull(rootDO) || !NumberUtils.validLong(rootDO.getId())) {
            throw new AnalysisException("root信息非法", null);
        }

        Long parentId = rootDO.getId();

        AnalysisV2DO record = new AnalysisV2DO();
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setParentId(parentId);
        record.setType(AnalysisV2TypeEnum.INSIGHT_ANALYSIS.getType());
        record.setStatus(AnalysisV2StatusEnum.INIT.getStatus());
        record.setIsDeleted(DeletedEnum.fromBoolean(false));
        record.setFailTimes((byte) 0);
        if (Objects.nonNull(meta)) {
            record.setMeta(JSON.toJSONString(meta));
        }

        record.setCrowdId(rootDO.getCrowdId());
        record.setCrowdName(rootDO.getCrowdName());
        record.setName(parentId + "-父分析");
        record.setDescription(parentId + "-父分析");
        record.setCreator(getCreator());


        return record;
    }
}