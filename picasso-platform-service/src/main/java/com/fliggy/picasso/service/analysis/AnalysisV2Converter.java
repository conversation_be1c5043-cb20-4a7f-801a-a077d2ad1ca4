package com.fliggy.picasso.service.analysis;

import com.fliggy.picasso.dao.AnalysisV2DO;
import com.fliggy.picasso.employee.UserContext;
import com.fliggy.picasso.employee.UserContextDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class AnalysisV2Converter {

    /**
     * DTO模型转换成DO模型
     *
     * @param analysisV2DTO
     */
    public AnalysisV2DO convertFromDTO(AnalysisV2DTO analysisV2DTO) {
        AnalysisV2DO analysisV2DO = new AnalysisV2DO();
        BeanUtils.copyProperties(analysisV2DTO, analysisV2DO);
        return analysisV2DO;
    }

    /**
     * DO模型转换成DTO模型
     *
     * @param analysisV2DO
     */
    public AnalysisV2DTO convertFromDO(AnalysisV2DO analysisV2DO) {
        AnalysisV2DTO analysisV2DTO = new AnalysisV2DTO();
        BeanUtils.copyProperties(analysisV2DO, analysisV2DTO);
        UserContextDTO userContextDTO = UserContext.getBucUser().orElse(null);
        if (Objects.nonNull(userContextDTO)) {
            analysisV2DTO.setIsAdmin(
                    analysisV2DO.getCreator().contains(userContextDTO.getEmpId())
                            || (StringUtils.isNotBlank(userContextDTO.getNickNameCN()) && analysisV2DO.getCreator().contains(userContextDTO.getNickNameCN()))
            );
        }
        return analysisV2DTO;
    }
}