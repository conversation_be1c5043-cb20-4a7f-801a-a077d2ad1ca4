package com.fliggy.picasso.service.handler;

import com.fliggy.picasso.common.enums.label.LabelClassifyTabEnum;
import com.fliggy.picasso.service.handler.impl.AbstractLabelMarketSearchHandler;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class LabelMarketSearchHandlerResolver {

    private final Map<LabelClassifyTabEnum, ? extends AbstractLabelMarketSearchHandler> map;

    @Autowired
    public LabelMarketSearchHandlerResolver(List<? extends AbstractLabelMarketSearchHandler> handlers) {
        map = Maps.uniqueIndex(handlers, AbstractLabelMarketSearchHandler::getLabelClassifyTab);
    }

    public AbstractLabelMarketSearchHandler resolve(LabelClassifyTabEnum tab) {
        return map.get(tab);
    }
}
