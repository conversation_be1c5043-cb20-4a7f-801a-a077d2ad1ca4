package com.fliggy.picasso.service.handler.impl;

import com.fliggy.picasso.common.constants.CrowdLabelBatchOperateActionEnum;
import com.fliggy.picasso.common.domain.label.CrowdLabelBatchOperateDO;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.service.handler.BatchOperateHandler;
import com.fliggy.picasso.service.handler.BatchOperateHandlerResolver;
import com.fliggy.picasso.service.label.LabelInfoService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/24
 */
@Component
public class BatchOperateSecurityLevel implements BatchOperateHandler, InitializingBean {

    private final BatchOperateHandlerResolver resolver;
    private final LabelInfoService labelInfoService;

    @Autowired
    public BatchOperateSecurityLevel(BatchOperateHandlerResolver resolver, LabelInfoService labelInfoService) {
        this.resolver = resolver;
        this.labelInfoService = labelInfoService;
    }

    @Override
    public boolean process(CrowdLabelBatchOperateDO crowdLabelBatchOperateDO) {
        if (crowdLabelBatchOperateDO == null) {
            throw new ParamErrorException("批量操作参数不能为空！");
        }

        List<Long> labelIdList = crowdLabelBatchOperateDO.getLabelIdList();
        Byte securityLevel = crowdLabelBatchOperateDO.getSecurityLevel();
        if (CollectionUtils.isNullOrEmpty(labelIdList) || securityLevel == null) {
            return false;
        }

        for (Long labelId : labelIdList) {
            LabelInfoDTO labelInfoDTO = labelInfoService.find(labelId);
            labelInfoDTO.setSecurityLevel(securityLevel);
            labelInfoService.updateSelectiveById(labelId, labelInfoDTO);
        }

        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        resolver.register(CrowdLabelBatchOperateActionEnum.BATCH_SET_SECURITY_LEVEL, this);
    }
}
