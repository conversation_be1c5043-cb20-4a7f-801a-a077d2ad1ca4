package com.fliggy.picasso.service.profile;

import java.io.Serializable;
import java.util.Date;

import com.fliggy.picasso.common.domain.Employee;
import lombok.Getter;
import lombok.Setter;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class LogicProfileInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     *   创建时间
     */
    @Getter
    @Setter
    private Date gmtCreate;

    /**
     *   修改时间
     */
    @Getter
    @Setter
    private Date gmtModified;

    /**
     *   画像标识
     */
    @Getter
    @Setter
    private String code;

    /**
     *   画像名称
     */
    @Getter
    @Setter
    private String name;

    /**
     *   画像描述
     */
    @Getter
    @Setter
    private String description;

    /**
     *   主键列
     */
    @Getter
    @Setter
    private String primaryKey;

    /**
     *   创建人
     */
    @Getter
    @Setter
    private Employee creator;

    /**
     *   修改人
     */
    @Getter
    @Setter
    private Employee modifier;

    /**
     *   删除状态
     */
    @Getter
    @Setter
    private Byte deleted;
}