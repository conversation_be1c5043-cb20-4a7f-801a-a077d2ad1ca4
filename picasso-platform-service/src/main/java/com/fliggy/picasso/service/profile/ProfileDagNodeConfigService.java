package com.fliggy.picasso.service.profile;

import com.fliggy.picasso.common.domain.profile.ProfileDagConfigVO;
import com.fliggy.picasso.common.enums.ProfileCodeEnum;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProfileDagNodeConfigService {

    /**
     * 新增
     * @param profileDagConfigVO
     * @return
     */
    long insert(ProfileDagConfigVO profileDagConfigVO);

    /**
     * 更新
     * @param profileDagConfigVO
     * @return
     */
    int update(ProfileDagConfigVO profileDagConfigVO);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    ProfileDagConfigVO selectById(Long id);

    /**
     * 查询所有需要运行的dag节点
     * @return
     */
    List<ProfileDagConfigVO> queryAllLatestConfigs();
}
