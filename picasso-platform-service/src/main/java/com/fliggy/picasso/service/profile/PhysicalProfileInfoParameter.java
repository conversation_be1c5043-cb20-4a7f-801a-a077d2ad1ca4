package com.fliggy.picasso.service.profile;

import java.io.Serializable;

import com.fliggy.picasso.common.enums.PhysicalProfileType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@Data
public class PhysicalProfileInfoParameter implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    private Long id;

    /**
     *   画像ID
     */
    private Long profileId;

    /**
     *   画像Code
     */
    private String profileCode;

    /**
     *   画像名称
     */
    private String profileName;

    /**
     *   主键列
     */
    private String primaryKey;

    /**
     *   物理画像标识
     */
    private String physicalProfileCode;

    /**
     *   物理画像名称
     */
    private String physicalProfileName;

    /**
     *   物理画像表类型
     */
    private PhysicalProfileType type;

    /**
     *   ODPS表名
     */
    private String odpsTable;

    /**
     *   OLAP引擎表名
     */
    private String olapTable;

    /**
     *   创建人
     */
    private String creator;

    /**
     *   修改人
     */
    private String modifier;

    /**
     *   删除状态
     */
    private Byte deleted;

    /**
     *   状态
     */
    private Byte status;
}