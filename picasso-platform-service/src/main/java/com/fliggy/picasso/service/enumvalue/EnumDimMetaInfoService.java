package com.fliggy.picasso.service.enumvalue;

import com.fliggy.picasso.common.domain.label.enumvalue.EnumDimMetaInfoDO;
import com.fliggy.picasso.common.domain.label.enumvalue.EnumDimMetaInfoParameter;
import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;

import java.util.List;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
public interface EnumDimMetaInfoService {

    /**
     * 根据参数统计总数
     *
     * @param param
     */
    long count(EnumDimMetaInfoParameter param);

    /**
     * 根据参数查询
     *
     * @param param
     */
    EnumDimMetaInfoDTO find(EnumDimMetaInfoParameter param);

    /**
     * @param labelCode
     * @return
     */
    EnumDimMetaInfoDO findByCode(String labelCode);

    /**
     * @param id
     * @return
     */
    EnumDimMetaInfoDTO findById(Long id);

    EnumDimMetaInfoDO queryById(Long id);

    /**
     * 列表查询
     *
     * @param param
     */
    List<EnumDimMetaInfoDTO> list(EnumDimMetaInfoParameter param);

    /**
     * 分区查询
     *
     * @param param
     * @return
     */
    List<EnumDimMetaInfoDO> pageQuery(EnumDimMetaInfoParameter param, Integer offset, Integer pageSize);

    /**
     * 创建
     *
     * @param param
     */
    long create(EnumDimMetaInfoParameter param);

    /**
     * @param enumDimMetaInfoDTO
     */
    long create(EnumDimMetaInfoDTO enumDimMetaInfoDTO);

    /**
     * 选择性修改
     *
     * @param dto
     * @param param
     */
    boolean updateSelective(EnumDimMetaInfoDTO dto, EnumDimMetaInfoParameter param);

    boolean hasEnumValue(LabelBizDataTypeEnum labelDataType);
}