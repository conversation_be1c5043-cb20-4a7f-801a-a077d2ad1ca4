package com.fliggy.picasso.service.analysis;

import java.util.List;

import com.github.pagehelper.PageInfo;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
public interface AnalysisMetaInfoService {


    /**
     * 列表查询
     *
     * @param param
     */
    List<AnalysisMetaInfoDTO> list(AnalysisMetaInfoParameter param);

    void add(AnalysisMetaInfoDTO analysisMetaInfoDTO);

    /**
     * 创建
     *
     * @param param
     */
    void create(AnalysisMetaInfoDTO param);


    /**
     * 更新
     *
     * @param analysisMetaInfoDTO
     */
    void updateSelectiveById(AnalysisMetaInfoDTO analysisMetaInfoDTO);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 分页查询
     *
     * @param param
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageInfo<AnalysisMetaInfoDTO> pageQuery(AnalysisMetaInfoParameter param, Integer pageNum, Integer pageSize);

    /**
     * @param id
     * @return
     */
    AnalysisMetaInfoDTO findById(Long id);

    /**
     * 查询分析详情以及分析的人群概述信息
     *
     * @param id
     * @return
     */
    AnalysisMetaInfoVo queryAnalysisMetaInfo(Long id);

}