package com.fliggy.picasso.service.approval.impl;

import com.alibaba.alipmc.api.model.bpm.ProcessInstance;
import com.alibaba.alipmc.api.model.bpm.ProcessInstanceStatus;
import com.fliggy.picasso.bpms.service.BpmsCommonService;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.constants.label.LabelDataSourceEnum;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoBO;
import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoConfig;
import com.fliggy.picasso.common.domain.label.LabelDataSourceConfig;
import com.fliggy.picasso.common.enums.bpms.ApprovalApplySceneEnum;
import com.fliggy.picasso.common.enums.bpms.BpmsApprovalStatusEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.entity.bo.CrowdInfoBO;
import com.fliggy.picasso.service.approval.ApprovalInfoService;
import com.fliggy.picasso.service.approval.ApprovalService;
import com.fliggy.picasso.service.category.CategoryService;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.record.PicassoRecordService;
import com.fliggy.picasso.utils.AdexFluxLogUtils;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.*;

import static com.fliggy.picasso.common.Constant.SERVICE_LOG;

public abstract class AbstractApprovalServiceImpl implements ApprovalService {

    protected static final Logger log = LoggerFactory.getLogger(SERVICE_LOG);

    public static String BPMS_AUTH_KEY = "1$picasso-platform$5069bd23-a7a6-455f-a113-62be00541e07";
    protected static String HIGH_RISK_LEVEL_CROWD_BPMS_CODE = "high_risk_level_crowd";
    protected static String ADEX_LABEL_USER_BPMS_CODE = "adex_label_use_approval";
    protected static String ZHUGE_LABEL_UPDATE_BPMS_CODE = "zhuge_label_upsert_approve";

    @Resource
    protected CrowdService crowdService;
    @Resource
    protected LabelInfoService labelInfoService;
    @Resource
    protected BpmsCommonService bpmsCommonService;
    @Resource
    protected ApprovalInfoService approvalInfoService;
    @Resource
    protected PicassoRecordService picassoRecordService;
    @Resource
    protected CategoryService categoryService;
    @Resource
    protected AdexFluxLogUtils adexFluxLogUtils;

    @Switch(description = "mock bpms审批结果，默认SUCCESS，拒绝为REJECTED，取消为CANCELED，出错为ERROR")
    public String mock_bpms_approval_result = "SUCCESS";
    @Switch(description = "是否打印debug日志，默认关闭")
    public boolean print_approval_debug_log = false;

    @Override
    public Boolean approvalJudge(CrowdInfoBO crowdInfoBO) {
        return false;
    }

    /**
     * 判断审批是否已在运行中
     * @param groupId
     * @param applyScene
     * @return
     */
    protected Boolean isApprovalRunning(Long groupId, ApprovalApplySceneEnum applyScene) {
        ApprovalInfoBO approvalInfoBO = approvalInfoService.queryLatestByEntityIdAndApplyScene(groupId, applyScene);
        return Objects.nonNull(approvalInfoBO) && Objects.nonNull(approvalInfoBO.getApprovalStatus())
                && Objects.equals(BpmsApprovalStatusEnum.APPROVAL_RUNNING, approvalInfoBO.getApprovalStatus());
    }

    /**
     * 判断审批是否成功
     * @param groupId
     * @param applyScene
     * @return
     */
    protected Boolean isApprovalSuccess(Long groupId, ApprovalApplySceneEnum applyScene) {
        ApprovalInfoBO approvalInfoBO = approvalInfoService.queryLatestByEntityIdAndApplyScene(groupId, applyScene);
        return Objects.nonNull(approvalInfoBO) && Objects.nonNull(approvalInfoBO.getApprovalStatus())
                && Objects.equals(BpmsApprovalStatusEnum.APPROVAL_SUCCESS, approvalInfoBO.getApprovalStatus());
    }

    /**
     * 判断审批是否已存在
     * @param groupId
     * @param applyScene
     * @return
     */
    protected Boolean isApprovalExist(Long groupId, ApprovalApplySceneEnum applyScene) {
        ApprovalInfoBO approvalInfoBO = approvalInfoService.queryLatestByEntityIdAndApplyScene(groupId, applyScene);
        return Objects.nonNull(approvalInfoBO);
    }

    @Override
    public Boolean useAdexLabel(Long crowdId) {
        CrowdMetaInfoDO crowdMetaInfoDO = crowdService.queryById(crowdId);
        if (Objects.isNull(crowdMetaInfoDO) || Objects.isNull(crowdMetaInfoDO.getConditions())) {
            return false;
        }

        List<LabelInfoDTO> allLabelList = getAllLabelList(crowdMetaInfoDO.getConditions());
        for (LabelInfoDTO labelInfoDTO:allLabelList) {
            if (Objects.isNull(labelInfoDTO.getExtInfo()) || Objects.isNull(labelInfoDTO.getExtInfo().getDataSourceConfig())) {
                continue;
            }

            LabelDataSourceConfig dataSourceConfig = labelInfoDTO.getExtInfo().getDataSourceConfig();
            if (Objects.nonNull(dataSourceConfig.getDataSource()) && Objects.equals(LabelDataSourceEnum.ADEX, dataSourceConfig.getDataSource())) {
                return true;
            }
        }
        return false;
    }

    public Boolean useNeedApprovalAdexLabel(CrowdInfoBO crowdInfoBO) {
        if (Objects.isNull(crowdInfoBO) || Objects.isNull(crowdInfoBO.getConditions())) {
            return false;
        }

        List<LabelInfoDTO> allLabelList = getAllLabelList(crowdInfoBO.getConditions());
        for (LabelInfoDTO labelInfoDTO:allLabelList) {
            if (Objects.isNull(labelInfoDTO.getExtInfo()) || Objects.isNull(labelInfoDTO.getExtInfo().getDataSourceConfig())) {
                continue;
            }

            LabelDataSourceConfig dataSourceConfig = labelInfoDTO.getExtInfo().getDataSourceConfig();
            if (Objects.isNull(dataSourceConfig.getDataSource()) || Objects.isNull(dataSourceConfig.getNeedApproval())) {
                continue;
            }

            // 需要审核的流通中心标签
            if (Objects.equals(LabelDataSourceEnum.ADEX, dataSourceConfig.getDataSource()) && dataSourceConfig.getNeedApproval()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 审批结果处理
     *
     * @param approvalInfoBO
     */
    protected ApprovalInfoBO resolveProcessInstanceResult(ApprovalInfoBO approvalInfoBO, ProcessInstance result) {
        if (Objects.isNull(approvalInfoBO)) {
            throw new ParamErrorException("approvalInfoVO params is invalid");
        }

        approvalInfoBO.setApprovalId(result.getProcessInstanceId());
        approvalInfoBO.setApprovalStatus(resolveApprovalStatus(result));
        return approvalInfoBO;
    }

    /**
     * 解析BPMS审批状态
     * @param result
     * @return
     */
    protected BpmsApprovalStatusEnum resolveApprovalStatus(ProcessInstance result) {
        if (Objects.isNull(result) || Objects.isNull(result.getProcessInstanceStatus())) {
            return BpmsApprovalStatusEnum.APPROVAL_ERROR;
        }

        switch (result.getProcessInstanceStatus()) {
            case RUNNING:
            case NEW:
                return BpmsApprovalStatusEnum.APPROVAL_RUNNING;
            case PAUSED:
            case CANCELED:
            case TERMINATED:
                return BpmsApprovalStatusEnum.APPROVAL_CANCELLED;
            case COMPLETED:
                String outResult = result.getOutResult();
                if (BpmsApprovalStatusEnum.SUCCESS_OUT_RESULT.equals(outResult)) {
                    return BpmsApprovalStatusEnum.APPROVAL_SUCCESS;
                }
                return BpmsApprovalStatusEnum.APPROVAL_REJECTED;
            case ERROR:
            default:
                return BpmsApprovalStatusEnum.APPROVAL_ERROR;
        }
    }

    /**
     * 获取所有标签
     * @param conditions
     * @return
     */
    protected List<LabelInfoDTO> getAllLabelList(LabelCrowdConditions conditions) {
        return labelInfoService.findByCodes(getAllLabelNames(conditions));
    }

    private List<String> getAllLabelNames(LabelCrowdConditions conditions) {
        if (Objects.isNull(conditions)) {
            return Collections.emptyList();
        }

        Set<String> allLabelNames = new HashSet<>();
        for (LabelCrowdConditions.LabelGroup labelGroup:conditions.getGroup()) {
            if (Objects.isNull(labelGroup) || CollectionUtils.isNullOrEmpty(labelGroup.getLabel())) {
                continue;
            }

            for (LabelCrowdConditions.LabelValue labelValue:labelGroup.getLabel()) {
                if (Objects.isNull(labelValue) || StringUtils.isBlank(labelValue.getName())) {
                    continue;
                }
                allLabelNames.add(labelValue.getName());
            }
        }
        return new ArrayList<>(allLabelNames);
    }

    /**
     * 工号不足6位补零
     * @param empId
     * @return
     */
    protected String empIdAddUpZero(String empId) {
        if (StringUtils.isBlank(empId)) {
            throw new ParamErrorException("empId is empty");
        }

        if (!StringUtils.isNumeric(empId)) {
            return empId;
        }

        return String.format("%06d", Integer.parseInt(empId));
    }

    /**
     * 获取BPMS初始化数据
     * @param approvalInfoBO
     * @return
     */
    protected Map<String, String> getInitData(ApprovalInfoBO approvalInfoBO) {
        if (Objects.isNull(approvalInfoBO) || Objects.isNull(approvalInfoBO.getApplyScene()) || Objects.isNull(approvalInfoBO.getApprovalInfo())) {
            throw new ParamErrorException("applyScene or approvalInfo is null");
        }
        ApprovalInfoConfig approvalInfo = approvalInfoBO.getApprovalInfo();

        Map<String, String> result = new HashMap<>();
        if (Objects.nonNull(approvalInfoBO.getEntityId())) {
            Long crowdId = Objects.nonNull(approvalInfoBO.getCrowdId()) ? approvalInfoBO.getCrowdId() : approvalInfoBO.getEntityId();
            result.put("crowdId", String.valueOf(crowdId));
        }
        if (StringUtils.isNotBlank(approvalInfo.getGroupName())) {
            result.put("crowdName", approvalInfo.getGroupName());
        }
        if (StringUtils.isNotBlank(approvalInfo.getLabelName())) {
            result.put("labelName", approvalInfo.getLabelName());
        }
        if (StringUtils.isNotBlank(approvalInfo.getApprovalReason())) {
            result.put("approvalReason", approvalInfo.getApprovalReason());
        }

        String boxCode = ApprovalApplySceneEnum.getApplyReasonBoxCode(approvalInfoBO.getApplyScene());
        if (StringUtils.isNotBlank(boxCode)) {
            result.put(boxCode, approvalInfo.getApplyReason());
        }

        if (Objects.nonNull(approvalInfoBO.getApplyScene())) {
            result.put("applyScene", approvalInfoBO.getApplyScene().getCode());
        }
        if (Objects.nonNull(approvalInfo.getApprovalLevel())) {
            result.put("approvalLevel", String.valueOf(approvalInfo.getApprovalLevel()));
        }
        return result;
    }

    protected ProcessInstance mockProcessInstance(String instanceId) {
        ProcessInstance processInstance = new ProcessInstance();
        processInstance.setProcessInstanceId(String.valueOf(instanceId));

        if (StringUtils.isBlank(mock_bpms_approval_result)) {
            processInstance.setProcessInstanceStatus(ProcessInstanceStatus.ERROR);
            return processInstance;
        }

        switch (mock_bpms_approval_result) {
            case "SUCCESS":
                processInstance.setProcessInstanceStatus(ProcessInstanceStatus.COMPLETED);
                processInstance.setOutResult(BpmsApprovalStatusEnum.SUCCESS_OUT_RESULT);
                break;
            case "REJECTED":
                processInstance.setProcessInstanceStatus(ProcessInstanceStatus.COMPLETED);
                break;
            case "CANCELED":
                processInstance.setProcessInstanceStatus(ProcessInstanceStatus.CANCELED);
                break;
            case "ERROR":
            default:
                processInstance.setProcessInstanceStatus(ProcessInstanceStatus.ERROR);
            }
        return processInstance;
    }

}
