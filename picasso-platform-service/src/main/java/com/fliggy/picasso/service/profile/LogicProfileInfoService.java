package com.fliggy.picasso.service.profile;

import java.util.List;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
public interface LogicProfileInfoService {
    /**
     * 根据参数统计总数
     *
     * @param param
     */
    long count(LogicProfileInfoParameter param);

    /**
     * 根据参数查询
     *
     * @param param
     */
    LogicProfileInfoDTO find(LogicProfileInfoParameter param);

    /**
     * 列表查询
     *
     * @param param
     */
    List<LogicProfileInfoDTO> list(LogicProfileInfoParameter param);

    /**
     * 创建
     *
     * @param param
     */
    void create(LogicProfileInfoParameter param);

    /**
     * @param code
     * @param name
     * @param desc
     * @param primaryKey
     * @return
     */
    Integer create(String code, String name, String desc, String primaryKey);

    /**
     * 选择性修改
     *
     * @param dto
     * @param param
     */
    void updateSelective(LogicProfileInfoDTO dto, LogicProfileInfoParameter param);

}