package com.fliggy.picasso.service.profile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fliggy.picasso.common.enums.DeletedEnum;
import com.fliggy.picasso.dao.LogicProfileInfoDO;
import com.fliggy.picasso.dao.LogicProfileInfoParam;
import com.fliggy.picasso.dao.LogicProfileInfoParam.Criteria;
import com.fliggy.picasso.mapper.picasso.LogicProfileInfoDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class LogicProfileInfoServiceImpl implements LogicProfileInfoService {

    @Autowired
    private LogicProfileInfoDAO logicProfileInfoDAO;

    @Autowired
    private LogicProfileInfoConverter logicProfileInfoConverter;

    /**
     * 根据参数统计总数
     *
     * @param param
     */
    @Override
    public long count(LogicProfileInfoParameter param) {
        LogicProfileInfoParam logicProfileInfoParam = new LogicProfileInfoParam();
        Criteria criteria = logicProfileInfoParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        return logicProfileInfoDAO.countByParam(logicProfileInfoParam);
    }

    /**
     * 根据参数查询
     *
     * @param param
     */
    @Override
    public LogicProfileInfoDTO find(LogicProfileInfoParameter param) {
        LogicProfileInfoParam logicProfileInfoParam = new LogicProfileInfoParam();
        Criteria criteria = logicProfileInfoParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        List<LogicProfileInfoDO> list = logicProfileInfoDAO.selectByParam(logicProfileInfoParam);
        if (null == list || list.isEmpty()) {
            return null;
        }
        return logicProfileInfoConverter.convertFromDO(list.get(0));
    }

    /**
     * 列表查询
     *
     * @param param
     */
    @Override
    public List<LogicProfileInfoDTO> list(LogicProfileInfoParameter param) {
        LogicProfileInfoParam logicProfileInfoParam = new LogicProfileInfoParam();
        Criteria criteria = logicProfileInfoParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        List<LogicProfileInfoDO> list = logicProfileInfoDAO.selectByParam(logicProfileInfoParam);
        if (null == list || list.isEmpty()) {
            return null;
        }
        List<LogicProfileInfoDTO> result = new ArrayList<>();
        for (LogicProfileInfoDO record : list) {
            LogicProfileInfoDTO logicProfileInfoDTO = logicProfileInfoConverter.convertFromDO(record);
            result.add(logicProfileInfoDTO);
        }
        return result;
    }

    /**
     * 创建
     *
     * @param param
     */
    @Override
    public void create(LogicProfileInfoParameter param) {
        LogicProfileInfoDO record = new LogicProfileInfoDO();
        record.setId(param.getId());
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setCode(param.getCode());
        record.setName(param.getName());
        record.setDescription(param.getDescription());
        record.setPrimaryKey(param.getPrimaryKey());
        record.setCreator(param.getCreator());
        record.setModifier(param.getModifier());
        record.setDeleted(param.getDeleted());
        logicProfileInfoDAO.insert(record);
    }

    @Override
    public Integer create(String code, String name, String desc, String primaryKey) {
        LogicProfileInfoDO record = new LogicProfileInfoDO();
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setCode(code);
        record.setName(name);
        record.setDescription(desc);
        record.setPrimaryKey(primaryKey);
        record.setCreator("148483");
        record.setModifier("148483");
        record.setDeleted(DeletedEnum.fromBoolean(false));
        return logicProfileInfoDAO.insert(record);
    }

    /**
     * 修改
     *
     * @param dto
     * @param param
     */
    @Override
    public void updateSelective(LogicProfileInfoDTO dto, LogicProfileInfoParameter param) {
        LogicProfileInfoDO record = logicProfileInfoConverter.convertFromDTO(dto);
        record.setGmtModified(new Date());
        LogicProfileInfoParam logicProfileInfoParam = new LogicProfileInfoParam();
        Criteria criteria = logicProfileInfoParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        logicProfileInfoDAO.updateByParamSelective(record, logicProfileInfoParam);
    }
}