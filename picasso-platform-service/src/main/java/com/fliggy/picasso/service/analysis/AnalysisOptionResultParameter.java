package com.fliggy.picasso.service.analysis;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class AnalysisOptionResultParameter implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     *   分析ID
     */
    @Getter
    @Setter
    private Long analysisId;

    /**
     *   分析子项状态
     */
    @Getter
    @Setter
    private String analysisOptStatus;

    /**
     *   最近更新时间
     */
    @Getter
    @Setter
    private Date lastUpdateTime;

    /**
     *   分析执行时的各标签数据时间
     */
    @Getter
    @Setter
    private String labelDataTime;

    /**
     *   分析子项图表类型
     */
    @Getter
    @Setter
    private String analysisOptGraghType;

    /**
     *   分析子项配置
     */
    @Getter
    @Setter
    private String analysisOptConfig;

    /**
     *   分析子项结果
     */
    @Getter
    @Setter
    private String analysisOptResult;

    /**
     *   分析子项扩展信息
     */
    @Getter
    @Setter
    private String extInfo;
}