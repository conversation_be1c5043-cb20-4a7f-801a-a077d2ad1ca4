package com.fliggy.picasso.service.analysis;

import com.fliggy.crowd.service.TripCrowdCommonService;
import com.fliggy.picasso.analysis.AnalysisType;
import com.fliggy.picasso.common.enums.DeletedEnum;
import com.fliggy.picasso.dao.AnalysisMetaInfoDO;
import com.fliggy.picasso.dao.AnalysisMetaInfoParam;
import com.fliggy.picasso.dao.AnalysisMetaInfoParam.Criteria;
import com.fliggy.picasso.dao.AnalysisMetaInfoParam.OrderCondition;
import com.fliggy.picasso.dao.AnalysisMetaInfoParam.SortType;
import com.fliggy.picasso.employee.UserContext;
import com.fliggy.picasso.employee.UserContextDTO;
import com.fliggy.picasso.mapper.picasso.AnalysisMetaInfoDAO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class AnalysisMetaInfoServiceImpl implements AnalysisMetaInfoService {

    @Autowired
    private AnalysisMetaInfoDAO analysisMetaInfoDAO;

    @Autowired
    private AnalysisMetaInfoConverter analysisMetaInfoConverter;

    @Autowired
    private TripCrowdCommonService tripCrowdCommonService;

    @Autowired
    @Lazy
    private AnalysisOptionResultService analysisOptionResultService;

    /**
     * 列表查询
     *
     * @param param
     */
    @Override
    public List<AnalysisMetaInfoDTO> list(AnalysisMetaInfoParameter param) {
        AnalysisMetaInfoParam analysisMetaInfoParam = new AnalysisMetaInfoParam();
        Criteria criteria = analysisMetaInfoParam.createCriteria();

        if (StringUtils.isNotEmpty(param.getAnalysisName())) {
            criteria.andAnalysisNameLike("%" + param.getAnalysisName() + "%");
        }
        if (StringUtils.isNotEmpty(param.getAnalysisType())) {
            criteria.andAnalysisTypeEqualTo(param.getAnalysisType());
        }
        if (StringUtils.isNotEmpty(param.getCreator())) {
            criteria.andCreatorEqualTo(param.getCreator());
        }
        if (StringUtils.isNotEmpty(param.getStatus())) {
            criteria.andStatusEqualTo(param.getStatus());
        }

        // 过滤删除
        criteria.andIsDeletedEqualTo(DeletedEnum.fromBoolean(false));

        // 按修改时间倒序
        analysisMetaInfoParam.appendOrderByClause(OrderCondition.GMTMODIFIED, SortType.DESC);

        List<AnalysisMetaInfoDTO> result = Lists.newArrayList();
        List<AnalysisMetaInfoDO> list = analysisMetaInfoDAO.selectByParamWithBLOBs(analysisMetaInfoParam);
        if (null == list || list.isEmpty()) {
            return result;
        }

        for (AnalysisMetaInfoDO record : list) {
            AnalysisMetaInfoDTO analysisMetaInfoDTO = analysisMetaInfoConverter.convertFromDO(record);
            result.add(analysisMetaInfoDTO);
        }
        return result;
    }

    /**
     * 新增画像分析/指标分析
     *
     * @param analysisMetaInfoDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(AnalysisMetaInfoDTO analysisMetaInfoDTO) {

        // 创建分析
        create(analysisMetaInfoDTO);

        // 如果是指标分析，同时创建分析子项
        if (AnalysisType.MEASURE == analysisMetaInfoDTO.getAnalysisType()) {
            AnalysisMeasureOptAddParam analysisMeasureOptAddParam = new AnalysisMeasureOptAddParam();
            analysisMeasureOptAddParam.setAnalysisId(analysisMetaInfoDTO.getId());
            analysisOptionResultService.addMeasureAnalysisOpt(analysisMeasureOptAddParam, analysisMetaInfoDTO);
        }
    }

    /**
     * 创建
     *
     * @param analysisMetaInfoDTO
     */
    @Override
    public void create(AnalysisMetaInfoDTO analysisMetaInfoDTO) {

        // 获取用户登陆态
        Optional<UserContextDTO> loginUserOpt = UserContext.getBucUser();
        if (!loginUserOpt.isPresent()) {
            throw new RuntimeException("未获取到登陆用户!");
        }

        Date now = new Date();
        analysisMetaInfoDTO.setGmtCreate(now);
        analysisMetaInfoDTO.setGmtModified(now);

        analysisMetaInfoDTO.setStatus(AnalysisStatus.INIT);
        analysisMetaInfoDTO.setIsDeleted(DeletedEnum.fromBoolean(false));
        analysisMetaInfoDTO.setCreator(StringUtils.isBlank(loginUserOpt.get().getNickNameCN()) ? loginUserOpt.get().getLastName() : loginUserOpt.get().getNickNameCN());
        analysisMetaInfoDTO.setOperator(Lists.newArrayList(StringUtils.isBlank(loginUserOpt.get().getNickNameCN()) ? loginUserOpt.get().getLastName() : loginUserOpt.get().getNickNameCN()));

        AnalysisMetaInfoDO record = analysisMetaInfoConverter.convertFromDTO(analysisMetaInfoDTO);
        analysisMetaInfoDAO.insert(record);

        analysisMetaInfoDTO.setId(record.getId());
    }

    /**
     * 修改
     *
     * @param analysisMetaInfoDTO
     */
    @Override
    public void updateSelectiveById(AnalysisMetaInfoDTO analysisMetaInfoDTO) {
        AnalysisMetaInfoDO record = analysisMetaInfoConverter.convertFromDTO(analysisMetaInfoDTO);
        record.setGmtModified(new Date());
        AnalysisMetaInfoParam analysisMetaInfoParam = new AnalysisMetaInfoParam();
        Criteria criteria = analysisMetaInfoParam.createCriteria();
        criteria.andIdEqualTo(analysisMetaInfoDTO.getId());
        analysisMetaInfoDAO.updateByParamSelective(record, analysisMetaInfoParam);
    }

    @Override
    public void delete(Long id) {
        AnalysisMetaInfoDTO analysisMetaInfoDTO = new AnalysisMetaInfoDTO();
        analysisMetaInfoDTO.setId(id);
        analysisMetaInfoDTO.setIsDeleted(DeletedEnum.fromBoolean(true));
        analysisMetaInfoDTO.setGmtModified(new Date());
        updateSelectiveById(analysisMetaInfoDTO);
    }

    @Override
    public PageInfo<AnalysisMetaInfoDTO> pageQuery(AnalysisMetaInfoParameter param, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<AnalysisMetaInfoDTO> res = list(param);
        return PageInfo.of(res);
    }

    @Override
    public AnalysisMetaInfoDTO findById(Long id) {
        AnalysisMetaInfoParam analysisMetaInfoParam = new AnalysisMetaInfoParam();
        Criteria criteria = analysisMetaInfoParam.createCriteria();
        criteria.andIdEqualTo(id);

        List<AnalysisMetaInfoDO> list = analysisMetaInfoDAO.selectByParamWithBLOBs(
                analysisMetaInfoParam);

        if (CollectionUtils.isNotEmpty(list)) {
            return analysisMetaInfoConverter.convertFromDO(list.get(0));
        }

        return null;
    }

    @Override
    public AnalysisMetaInfoVo queryAnalysisMetaInfo(Long id) {
        return null;
    }
}