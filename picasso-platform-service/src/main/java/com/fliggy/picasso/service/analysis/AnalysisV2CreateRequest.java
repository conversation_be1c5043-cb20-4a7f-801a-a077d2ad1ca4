package com.fliggy.picasso.service.analysis;

import com.fliggy.picasso.analysis.AnalysisV2Meta;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Created on 2022/6/6 下午7:49
 */
@Data
public class AnalysisV2CreateRequest implements Serializable {

    /**
     * 分析ID
     */
    private Long id;

    /**
     *   分析名称
     */
    private String name;

    /**
     *   人群名称
     */
    private String crowdName;

    /**
     *   分析描述
     */
    private String description;

    /**
     *   创建人
     */
    private String creator;

    /**
     *   人群id
     */
    private Long crowdId;

    /**
     *   分析元数据
     *   【必传】
     *   创建父分析时，将选择的标签code传过来
     *   创建子分析时，将柱状图的code 和 x坐标值传过来
     */
    private AnalysisV2Meta meta;

    /**
     *   父id
     *   【非必传】
     *   下钻场景必传，将当前分析id传过来
     */
    private Long parentId = 0L;

    /**
     *   父分析元数据
     *   【非必传】
     *   下钻场景必传，这里传柱状图返回的 parentTagInfoList
     */
    private AnalysisV2Meta parentMetaAndValue;

}
