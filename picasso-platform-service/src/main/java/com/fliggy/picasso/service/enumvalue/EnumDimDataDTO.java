package com.fliggy.picasso.service.enumvalue;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class EnumDimDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     *   创建时间
     */
    @Getter
    @Setter
    private Date gmtCreate;

    /**
     *   修改时间
     */
    @Getter
    @Setter
    private Date gmtModified;

    /**
     *   枚举id。具有实际的意义，存量的枚举，需要使用原有id。不具有实际意义的枚举该值为-1。
     */
    @Getter
    @Setter
    private Long enumId;

    /**
     *   外键，维度id。
     */
    @Getter
    @Setter
    private Long dimMetaId;

    /**
     *   枚举代码（转换前）
     */
    @Getter
    @Setter
    private String enumCode;

    /**
     *   枚举描述（转换后）
     */
    @Getter
    @Setter
    private String enumDesc;

    /**
     *   父节点id（0代表根节点）
     */
    @Getter
    @Setter
    private Long parentId;

    /**
     *   是否叶子节点
     */
    @Getter
    @Setter
    private Byte leaf;

    /**
     *   节点层级
     */
    @Getter
    @Setter
    private String level;

    /**
     *   是否逻辑删除（0:否，1:是）
     */
    @Getter
    @Setter
    private Byte deleted;

    /**
     *   数据业务日期，格式YYYYMMDD
     */
    @Getter
    @Setter
    private String ds;
}