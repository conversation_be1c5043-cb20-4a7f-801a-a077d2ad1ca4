package com.fliggy.picasso.service.enumvalue;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.common.domain.label.enumvalue.EnumDimMetaInfoDO;
import com.fliggy.picasso.common.domain.label.enumvalue.EnumDimMetaInfoParameter;
import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import com.fliggy.picasso.dao.EnumDimMetaInfoParam;
import com.fliggy.picasso.dao.EnumDimMetaInfoParam.Criteria;
import com.fliggy.picasso.mapper.picasso.EnumDimMetaInfoDAO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class EnumDimMetaInfoServiceImpl implements EnumDimMetaInfoService {

    @Autowired
    private EnumDimMetaInfoDAO enumDimMetaInfoDAO;

    @Autowired
    private EnumDimMetaInfoConverter enumDimMetaInfoConverter;

    /**
     * 根据参数统计总数
     *
     * @param param
     */
    @Override
    public long count(EnumDimMetaInfoParameter param) {
        EnumDimMetaInfoParam enumDimMetaInfoParam = new EnumDimMetaInfoParam();
        Criteria criteria = enumDimMetaInfoParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        return enumDimMetaInfoDAO.countByParam(enumDimMetaInfoParam);
    }

    /**
     * 根据参数查询
     *
     * @param param
     */
    @Override
    public EnumDimMetaInfoDTO find(EnumDimMetaInfoParameter param) {
        EnumDimMetaInfoParam enumDimMetaInfoParam = new EnumDimMetaInfoParam();
        Criteria criteria = enumDimMetaInfoParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        List<EnumDimMetaInfoDO> list = enumDimMetaInfoDAO.selectByParam(enumDimMetaInfoParam);
        if (null == list || list.isEmpty()) {
            return null;
        }
        return enumDimMetaInfoConverter.convertFromDO(list.get(0));
    }

    /**
     * 根据参数查询
     *
     * @param enumCode
     */
    @Override
    public EnumDimMetaInfoDO findByCode(String enumCode) {
        if (StringUtils.isEmpty(enumCode)) {
            throw new RuntimeException("LabelCode Can't Be NULL!");
        }

        EnumDimMetaInfoParam enumDimMetaInfoParam = new EnumDimMetaInfoParam();
        Criteria criteria = enumDimMetaInfoParam.createCriteria();

        criteria.andCodeEqualTo(enumCode);

        List<EnumDimMetaInfoDO> list = enumDimMetaInfoDAO.selectByParam(enumDimMetaInfoParam);
        if (null == list || list.isEmpty()) {
            return null;
        }

        if (list.size() > 1) {
            throw new RuntimeException("Found More Than One EnumDimMeta By EnumCode:" + enumCode);
        }

        return list.get(0);
    }

    /**
     * 根据参数查询
     *
     * @param id
     */
    @Override
    public EnumDimMetaInfoDTO findById(Long id) {
        if (id == null) {
            throw new RuntimeException("LabelCode Can't Be NULL!");
        }

        EnumDimMetaInfoDO enumDimMetaInfoDO = enumDimMetaInfoDAO.selectByPrimaryKey(id);

        return enumDimMetaInfoConverter.convertFromDO(enumDimMetaInfoDO);
    }

    @Override
    public EnumDimMetaInfoDO queryById(Long id) {
        if (id == null) {
            throw new RuntimeException("LabelCode Can't Be NULL!");
        }

        return enumDimMetaInfoDAO.selectByPrimaryKey(id);
    }

    /**
     * 列表查询
     *
     * @param param
     */
    @Override
    public List<EnumDimMetaInfoDTO> list(EnumDimMetaInfoParameter param) {
        EnumDimMetaInfoParam enumDimMetaInfoParam = new EnumDimMetaInfoParam();
        enumDimMetaInfoParam.createCriteria().andDeletedEqualTo(param.getDeleted());
        if (CollectionUtils.isNotEmpty(param.getIds())) {
            enumDimMetaInfoParam.createCriteria().andIdIn(param.getIds());
        }
        List<EnumDimMetaInfoDO> list = enumDimMetaInfoDAO.selectByParamWithBLOBs(enumDimMetaInfoParam);
        if (null == list || list.isEmpty()) {
            return null;
        }
        List<EnumDimMetaInfoDTO> result = new ArrayList<>();
        for (EnumDimMetaInfoDO record : list) {
            EnumDimMetaInfoDTO enumDimMetaInfoDTO = enumDimMetaInfoConverter.convertFromDO(record);
            result.add(enumDimMetaInfoDTO);
        }
        return result;
    }

    @Override
    public List<EnumDimMetaInfoDO> pageQuery(EnumDimMetaInfoParameter param, Integer offset, Integer pageSize) {
        EnumDimMetaInfoParam query = new EnumDimMetaInfoParam();
        Criteria criteria = query.createCriteria();
        query.setPageStart(offset);
        query.setPageSize(pageSize);

        if (param.getId() != null) {
            criteria.andIdEqualTo(param.getId());
        }

        if (param.getCode() != null) {
            criteria.andCodeLike("%" + param.getCode() + "%");
        }

        if (param.getName() != null) {
            criteria.andNameLike("%" + param.getName() + "%");
        }

        if (param.getType() != null) {
            criteria.andTypeEqualTo(param.getType());
        }

        if (param.getCreator() != null) {
            criteria.andCreatorLike("%" + param.getCreator().getEmpId() + "%");
        }

        if (param.getDeleted() != null) {
            criteria.andDeletedEqualTo(param.getDeleted());
        }

        return enumDimMetaInfoDAO.selectByParamWithBLOBs(query);
    }

    /**
     * 创建
     *
     * @param param
     */
    @Override
    public long create(EnumDimMetaInfoParameter param) {
        EnumDimMetaInfoDO record = new EnumDimMetaInfoDO();
        record.setId(param.getId());
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setName(param.getName());
        record.setType(param.getType());
        record.setCode(param.getCode());
        record.setDeleted(param.getDeleted());
        record.setCreator(JSON.toJSONString(param.getCreator()));
        record.setOperator(JSON.toJSONString(param.getOperator()));
        record.setExtInfo(param.getExtInfo());
        return (long)enumDimMetaInfoDAO.insert(record);
    }

    @Override
    public long create(EnumDimMetaInfoDTO enumDimMetaInfoDTO) {
        Date now = new Date();
        enumDimMetaInfoDTO.setGmtCreate(now);
        enumDimMetaInfoDTO.setGmtModified(now);
        EnumDimMetaInfoDO enumDimMetaInfoDO = enumDimMetaInfoConverter.convertFromDTO(enumDimMetaInfoDTO);
        enumDimMetaInfoDAO.insert(enumDimMetaInfoDO);
        enumDimMetaInfoDTO.setId(enumDimMetaInfoDO.getId());
        return enumDimMetaInfoDO.getId();
    }

    /**
     * 修改
     *
     * @param dto
     * @param param
     */
    @Override
    public boolean updateSelective(EnumDimMetaInfoDTO dto, EnumDimMetaInfoParameter param) {
        EnumDimMetaInfoDO record = enumDimMetaInfoConverter.convertFromDTO(dto);
        record.setGmtModified(new Date());

        EnumDimMetaInfoParam enumDimMetaInfoParam = new EnumDimMetaInfoParam();
        Criteria criteria = enumDimMetaInfoParam.createCriteria();
        criteria.andIdEqualTo(dto.getId());
        return enumDimMetaInfoDAO.updateByParamSelective(record, enumDimMetaInfoParam) > 0;
    }

    /**
     * @param labelDataType
     * @return
     */
    @Override
    public boolean hasEnumValue(LabelBizDataTypeEnum labelDataType) {
        return Lists.newArrayList(LabelBizDataTypeEnum.ENUM, LabelBizDataTypeEnum.MULTI_VALUE, LabelBizDataTypeEnum.KV).contains(labelDataType);
    }
}