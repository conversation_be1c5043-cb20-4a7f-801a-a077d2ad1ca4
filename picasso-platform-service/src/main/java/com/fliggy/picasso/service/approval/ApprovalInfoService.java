package com.fliggy.picasso.service.approval;

import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoBO;
import com.fliggy.picasso.common.enums.bpms.ApprovalApplySceneEnum;

import java.util.List;

public interface ApprovalInfoService {

    /**
     * 插入审批信息
     * @param approvalInfoBO
     * @return
     */
    int insert(ApprovalInfoBO approvalInfoBO);

    /**
     * 更新审批信息
     * @param approvalInfoBO
     * @return
     */
    int updateStatus(ApprovalInfoBO approvalInfoBO);

    /**
     * 更新审批信息，当审批结果发生变化时更新
     * @param approvalInfoBO
     * @return
     */
    Boolean updateStatusWhileDiff(ApprovalInfoBO approvalInfoBO);

    /**
     * 根据approvalId查询审批信息
     * @param approvalId
     * @return
     */
    ApprovalInfoBO queryByApprovalId(String approvalId);

    /**
     * 根据approvalId查询审批信息
     * @param approvalIds
     * @return
     */
    List<ApprovalInfoBO> queryByApprovalIds(List<String> approvalIds);

    /**
     * 根据entityId和applyScene查询审批信息
     * @param entityId
     * @param applyScene
     * @return
     */
    List<ApprovalInfoBO> queryByEntityIdAndApplyScene(Long entityId, ApprovalApplySceneEnum applyScene);

    /**
     * 根据entityId和applyScene查询最新审批信息
     * @param entityId
     * @param applyScene
     * @return
     */
    ApprovalInfoBO queryLatestByEntityIdAndApplyScene(Long entityId, ApprovalApplySceneEnum applyScene);

    /**
     * 根据entityId查询审批信息
     * @param entityId
     * @return
     */
    List<ApprovalInfoBO> queryByEntityId(Long entityId);
}
