package com.fliggy.picasso.service.handler.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.label.LabelDataConfig;
import com.fliggy.picasso.common.domain.score.PageViewInfoVO;
import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import com.fliggy.picasso.common.enums.label.LabelClassifyTabEnum;
import com.fliggy.picasso.common.enums.label.LabelDataDescEnum;
import com.fliggy.picasso.common.enums.label.LabelOpenScopeEnum;
import com.fliggy.picasso.common.enums.score.PvEntityTypeEnum;
import com.fliggy.picasso.common.enums.score.ScoreTypeEnum;
import com.fliggy.picasso.domain.LabelInfoHoloDO;
import com.fliggy.picasso.employee.Amdp3256EmployeeServiceImpl;
import com.fliggy.picasso.entity.bo.LabelInfoFrontBO;
import com.fliggy.picasso.entity.convertor.LabelInfoHoloConvertor;
import com.fliggy.picasso.entity.param.LabelMarketSearchParam;
import com.fliggy.picasso.entity.vo.CollectInfoVO;
import com.fliggy.picasso.service.enumvalue.EnumDimDataDTO;
import com.fliggy.picasso.service.enumvalue.EnumDimDataService;
import com.fliggy.picasso.service.handler.LabelMarketSearchHandler;
import com.fliggy.picasso.service.label.LabelInfoHoloService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.label.auth.LabelAuthFacade;
import com.fliggy.picasso.service.permission.PermissionService;
import com.fliggy.picasso.service.score.CollectInfoService;
import com.fliggy.picasso.service.score.PageViewInfoService;
import com.fliggy.picasso.service.score.ScoreInfoService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractLabelMarketSearchHandler implements LabelMarketSearchHandler {

    @Resource
    protected LabelInfoService labelInfoService;
    @Resource
    protected LabelInfoHoloService labelInfoHoloService;
    @Resource
    private LabelAuthFacade labelAuthFacade;
    @Resource
    private CollectInfoService collectInfoService;
    @Resource
    private PageViewInfoService pageViewInfoService;
    @Resource
    private EnumDimDataService enumDimDataService;
    @Resource
    private ScoreInfoService scoreInfoService;
    @Autowired
    private Amdp3256EmployeeServiceImpl amdp3256EmployeeService;
    @Autowired
    protected PermissionService permissionService;

    private static final ExecutorService LABEL_PROCESS_EXECUTOR = new ThreadPoolExecutor(
            0,
            20,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    /**
     * 根据搜索项查询标签列表
     * 按创建时间查最新2000个标签
     * @param param
     * @return
     */
    protected List<LabelInfoHoloDO> queryLabelList(LabelMarketSearchParam param) {
        if (Objects.isNull(param)) {
            return Lists.newArrayList();
        }

        return labelInfoHoloService.searchLabelList(param);
    }

    /**
     * 分页
     * @param labels
     * @param pageNo
     * @param pageSize
     * @return
     */
    protected List<LabelInfoHoloDO> pageLabels(List<LabelInfoHoloDO> labels, Integer pageNo, Integer pageSize) {
        if (CollectionUtils.isEmpty(labels)) {
            return Lists.newArrayList();
        }
        if (Objects.isNull(pageNo) || pageNo <= 0) {
            pageNo = 1;
        }
        if (Objects.isNull(pageSize) || pageSize <= 0) {
            pageSize = 10;
        }
        return labels.stream().skip((pageNo - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
    }

    /**
     * 转换为BO
     * @param labels
     * @return
     */
    protected List<LabelInfoFrontBO> convertToBO(List<LabelInfoHoloDO> labels) {
        return labels.stream().map(LabelInfoHoloConvertor::convertToBO).collect(Collectors.toList());
    }

    /**
     * 获取标签tab统计
     * @param labels
     * @return
     */
    protected Map<LabelClassifyTabEnum, Long> getTabCntMap(List<LabelInfoHoloDO> labels, Map<String, Boolean> labelPermissions,
                                                           Map<String, Integer> labelCollectInfo, String empId) {
        Map<LabelClassifyTabEnum, Long> tabCntMap = Maps.newHashMap();
        tabCntMap.put(LabelClassifyTabEnum.ALL, (long) labels.size());

        Long publicCnt = 0L;
        List<LabelInfoHoloDO> publicLabels = labels.stream().filter(label -> Objects.isNull(label.getOpenScope())
                || Objects.equals(label.getOpenScope(), LabelOpenScopeEnum.PUBLIC.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(publicLabels)) {
            long nonAuthorizedCnt = publicLabels.stream()
                    .filter(label -> Objects.nonNull(label.getSecurityLevel()) && label.getSecurityLevel() == 1).count();
            long authorizedCnt = publicLabels.stream()
                    .filter(label -> Objects.nonNull(label.getSecurityLevel()) && label.getSecurityLevel() > 1)
                    .filter(label -> labelPermissions.containsKey(label.getCode()) && labelPermissions.get(label.getCode())).count();
            publicCnt = nonAuthorizedCnt + authorizedCnt;
        }

        Long privateCnt = 0L;
        List<LabelInfoHoloDO> privateLabels = labels.stream().filter(label -> Objects.equals(label.getOpenScope(), LabelOpenScopeEnum.PRIVATE.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(privateLabels)) {
            privateCnt = privateLabels.stream().filter(label -> {
                if (Objects.nonNull(label.getDataOwner()) && label.getDataOwner().contains(empId)) {
                    return true;
                }
                if (Objects.nonNull(label.getManagers()) && label.getManagers().contains(empId)) {
                    return true;
                }
                if (Objects.nonNull(label.getSharers()) && label.getSharers().contains(empId)) {
                    return true;
                }
                return false;
            }).count();
        }
        tabCntMap.put(LabelClassifyTabEnum.AUTHORIZED, publicCnt + privateCnt);

        long collectedCnt = labels.stream().filter(label -> labelCollectInfo.containsKey(label.getCode())
                && labelCollectInfo.get(label.getCode()) == 1).count();
        tabCntMap.put(LabelClassifyTabEnum.COLLECTED, collectedCnt);
        return tabCntMap;
    }

    /**
     * 标签鉴权
     * @param labels
     * @param bucId
     * @return
     */
    protected Map<String, Boolean> checkLabelPermission(List<LabelInfoHoloDO> labels, Integer bucId) {
        List<String> labelCodes = labels.stream()
                .filter(labelInfoHoloDO -> Objects.nonNull(labelInfoHoloDO.getSecurityLevel()) && labelInfoHoloDO.getSecurityLevel()>1)
                .map(LabelInfoHoloDO::getCode).collect(Collectors.toList());
        return labelAuthFacade.checkPermissions(bucId, labelCodes);
    }

    protected Map<String, Integer> checkLabelCollected(List<LabelInfoHoloDO> labels, String empId) {
        List<String> labelCodes = labels.stream().map(LabelInfoHoloDO::getCode).collect(Collectors.toList());
        return isLabelCollected(labelCodes, empId);
    }

    /**
     * 添加额外信息
     * @param labelInfoFrontBOList
     * @param labelPermissions
     */
    protected void addUpExtraInfo(List<LabelInfoFrontBO> labelInfoFrontBOList, Map<String, Boolean> labelPermissions,
                                  Map<String, Integer> labelCollectInfo) {
        List<String> labelCodes = labelInfoFrontBOList.stream().map(LabelInfoFrontBO::getCode).collect(Collectors.toList());
        Map<String, BigDecimal> healthScoreMap = scoreInfoService.batchQueryLabelScoreByScoreType(labelCodes, ScoreTypeEnum.HEALTH_SCORE);
        Map<String, Long> labelPvCnt = getLabelPvCnt(labelInfoFrontBOList);

        // 设置基本信息（内存操作，直接执行）
        labelInfoFrontBOList.forEach(labelInfoFrontBO -> {
            String code = labelInfoFrontBO.getCode();

            // 设置权限
            if (labelPermissions.containsKey(code)) {
                labelInfoFrontBO.setIsAuth(labelPermissions.get(code));
            }

            // 设置健康分数
            labelInfoFrontBO.setHealthScore(Objects.isNull(healthScoreMap.get(code)) ? null : String.valueOf(healthScoreMap.get(code)));

            // 设置收藏状态
            labelInfoFrontBO.setIsCollect(labelCollectInfo.containsKey(code) && labelCollectInfo.get(code) == 1);

            // 设置PV数量
            if (labelPvCnt.containsKey(code)) {
                labelInfoFrontBO.setPvCnt(labelPvCnt.get(code));
            }
        });

        // 并行处理收藏数量查询
        CompletableFuture<Void> collectAmountFuture = CompletableFuture.allOf(labelInfoFrontBOList.stream()
            .map(labelInfoFrontBO -> CompletableFuture.runAsync(() -> {
                String code = labelInfoFrontBO.getCode();
                // 设置收藏数量
                Long collectAmount = collectInfoService.countLabelCollect(code);
                labelInfoFrontBO.setCollectAmount(Objects.isNull(collectAmount) ? 0L : collectAmount);
            }, LABEL_PROCESS_EXECUTOR)).toArray(CompletableFuture[]::new));

        // 并行处理预览数据查询
        CompletableFuture<Void> previewDataFuture = CompletableFuture.allOf(labelInfoFrontBOList.stream()
            .filter(labelInfoFrontBO -> LabelBizDataTypeEnum.isEnum(labelInfoFrontBO.getDataType()))
            .map(labelInfoFrontBO -> CompletableFuture.runAsync(() -> {
                // 设置预览数据
                labelInfoFrontBO.setPreviewList(getLabelDataPreview(labelInfoFrontBO));
            }, LABEL_PROCESS_EXECUTOR)).toArray(CompletableFuture[]::new));

        // 并行处理需要外部调用的操作
        CompletableFuture<Void> externalCallFuture = CompletableFuture.allOf(labelInfoFrontBOList.stream()
            .map(labelInfoFrontBO -> CompletableFuture.runAsync(() -> {
                // 添加钉钉ID
                addUpDingTalkId(labelInfoFrontBO);
            }, LABEL_PROCESS_EXECUTOR)).toArray(CompletableFuture[]::new));

        // 等待所有异步任务完成
        CompletableFuture.allOf(collectAmountFuture, previewDataFuture, externalCallFuture).join();
    }

    /**
     * 补充阿里钉号
     * @param labelInfoFrontBO
     */
    private void addUpDingTalkId(LabelInfoFrontBO labelInfoFrontBO) {
        if (Objects.isNull(labelInfoFrontBO)) {
            return;
        }

        if (Objects.nonNull(labelInfoFrontBO.getBizOwner())) {
            Employee bizOwner = labelInfoFrontBO.getBizOwner();
            String dingTalkId = amdp3256EmployeeService.getDingTalkIdByEmpId(bizOwner.getEmpId());
            if (StringUtils.isNotBlank(dingTalkId)) {
                bizOwner.setDingTalkId(dingTalkId);
            }
        }

        if (Objects.nonNull(labelInfoFrontBO.getDataOwner())) {
            Employee dataOwner = labelInfoFrontBO.getDataOwner();
            String dingTalkId = amdp3256EmployeeService.getDingTalkIdByEmpId(dataOwner.getEmpId());
            if (StringUtils.isNotBlank(dingTalkId)) {
                dataOwner.setDingTalkId(dingTalkId);
            }
        }
    }

    /**
     * 获取标签是否被收藏
     * @param labelCodes
     * @param empId
     * @return
     */
    private Map<String, Integer> isLabelCollected(List<String> labelCodes, String empId) {
        List<CollectInfoVO> collectInfoVOList = collectInfoService.queryLabelCollectList(labelCodes, empId);
        return collectInfoVOList.stream().collect(Collectors.toMap(CollectInfoVO::getEntity, CollectInfoVO::getCollect));
    }

    /**
     * 获取标签PV数
     * @param labelInfoFrontBOList
     * @return
     */
    private Map<String, Long> getLabelPvCnt(List<LabelInfoFrontBO> labelInfoFrontBOList) {
        List<String> labelCodes = labelInfoFrontBOList.stream().map(LabelInfoFrontBO::getCode).collect(Collectors.toList());
        List<PageViewInfoVO> pageViewInfoVOList = pageViewInfoService.queryPageViewList(labelCodes, PvEntityTypeEnum.LABEL);
        return pageViewInfoVOList.stream().collect(Collectors.toMap(PageViewInfoVO::getEntity, PageViewInfoVO::getPvCnt));
    }

    /**
     * 获取标签数据预览
     * @param labelInfoFrontBO
     * @return
     */
    private List<String> getLabelDataPreview(LabelInfoFrontBO labelInfoFrontBO) {
        if (Objects.isNull(labelInfoFrontBO) || Objects.isNull(labelInfoFrontBO.getDataConfig())) {
            return Lists.newArrayList();
        }
        LabelDataConfig dataConfig = labelInfoFrontBO.getDataConfig();
        if (Objects.equals(dataConfig.getDataDesc(), LabelDataDescEnum.COMMON_ENUM)) {
            List<EnumDimDataDTO> enumDimDataDTOList = enumDimDataService.queryPreviewData(labelInfoFrontBO.getDimEnumId());
            return enumDimDataDTOList.stream().map(EnumDimDataDTO::getEnumDesc).collect(Collectors.toList());
        }

        Map<String, String> dataValue = dataConfig.getDataValue();
        if (MapUtils.isEmpty(dataValue)) {
            return Lists.newArrayList();
        }
        return new ArrayList<>(dataValue.values());
    }

}
