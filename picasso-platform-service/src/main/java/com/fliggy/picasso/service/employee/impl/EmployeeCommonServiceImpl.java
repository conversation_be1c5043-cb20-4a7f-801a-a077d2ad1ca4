package com.fliggy.picasso.service.employee.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.ihr.amdplatform.service.api.AmdpDataQueryService;
import com.alibaba.ihr.amdplatform.service.dto.DataFieldDTO;
import com.alibaba.ihr.amdplatform.service.dto.DataRowDTO;
import com.alibaba.ihr.amdplatform.service.dto.QueryResultDTO;
import com.alibaba.ihr.amdplatform.service.dto.ResultDTO;
import com.alibaba.ihr.amdplatform.service.param.AuthParam;
import com.alibaba.ihr.amdplatform.service.param.DataQueryParam;
import com.alibaba.ihr.amdplatform.service.param.FilterField;

import com.fliggy.picasso.service.employee.EmployeeCommonService;
import com.taobao.csp.courier.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.fliggy.picasso.common.Constant.*;
import static com.fliggy.picasso.common.Constant.AMDP_AGGREGATE_ID;
import static com.fliggy.picasso.common.Constant.AMDP_APP_SECRET;
import static com.fliggy.picasso.common.Constant.APP_NAME;

/**
 * AMDP: Alibaba Master Data Platform，阿里巴巴企业主数据管理与对接平台
 * @link https://yuque.antfin-inc.com/tx6u83/qd423y/cbavcz
 *
 * <AUTHOR>
 * @date 2021/2/3
 */
@Component
public class EmployeeCommonServiceImpl implements EmployeeCommonService {

    private final AmdpDataQueryService amdpDataQueryService;

    @Autowired
    public EmployeeCommonServiceImpl(AmdpDataQueryService amdpDataQueryService) {
        this.amdpDataQueryService = amdpDataQueryService;
    }

    @Override
    public String getUserMailAddress(String empId) {
        AuthParam authParam = new AuthParam(APP_NAME, AMDP_APP_SECRET);
        DataQueryParam dataQueryParam = new DataQueryParam();
        dataQueryParam.setCombineId(AMDP_AGGREGATE_ID);

        List<FilterField> filterFieldList = new ArrayList<>();
        List<String> workNoList = Stream.of(empId).collect(Collectors.toList());
        FilterField workNoListField = new FilterField("workNoList", workNoList);
        filterFieldList.add(workNoListField);
        dataQueryParam.setFilterFieldList(filterFieldList);

        ResultDTO<QueryResultDTO> result = amdpDataQueryService.queryDataSet(authParam, dataQueryParam);
        if (result != null && result.isSuccess() && result.getData() != null) {
            QueryResultDTO data = result.getData();
            DataRowDTO dataRow = data.getDataRows().get(0);

            for (DataFieldDTO dataFieldDTO : dataRow.getDataFields()) {
                if ("buMail".equals(dataFieldDTO.getName())) {
                    return dataFieldDTO.getValue().toString();
                }
            }
        }
        return StringUtils.EMPTY;
    }
}
