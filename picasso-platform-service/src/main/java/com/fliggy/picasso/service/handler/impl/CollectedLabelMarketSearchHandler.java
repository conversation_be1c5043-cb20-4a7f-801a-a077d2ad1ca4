package com.fliggy.picasso.service.handler.impl;

import com.fliggy.picasso.common.enums.label.LabelClassifyTabEnum;
import com.fliggy.picasso.domain.LabelInfoHoloDO;
import com.fliggy.picasso.entity.bo.LabelInfoFrontBO;
import com.fliggy.picasso.entity.bo.LabelMarketSearchInfoBO;
import com.fliggy.picasso.entity.param.LabelMarketSearchParam;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class CollectedLabelMarketSearchHandler extends AbstractLabelMarketSearchHandler {
    @Override
    public LabelClassifyTabEnum getLabelClassifyTab() {
        return LabelClassifyTabEnum.COLLECTED;
    }

    @Override
    public LabelMarketSearchInfoBO process(LabelMarketSearchParam param) {
        if (Objects.isNull(param)) {
            return null;
        }

        // 搜索排序
        List<LabelInfoHoloDO> labels = queryLabelList(param);

        // 计数
        LabelMarketSearchInfoBO result = new LabelMarketSearchInfoBO();
        Map<String, Boolean> labelPermissions = checkLabelPermission(labels, param.getBucId());
        Map<String, Integer> labelCollectInfo = checkLabelCollected(labels, param.getEmpId());
        Map<LabelClassifyTabEnum, Long> tabCntMap = getTabCntMap(labels, labelPermissions, labelCollectInfo, param.getEmpId());
        result.setTabCntMap(tabCntMap);

        // 收藏过滤
        List<LabelInfoHoloDO> collectedLabels = labels.stream().filter(label -> labelCollectInfo.containsKey(label.getCode())
                && labelCollectInfo.get(label.getCode()) == 1).collect(Collectors.toList());

        // 分页，补数据
        List<LabelInfoHoloDO> pageLabels = pageLabels(collectedLabels, param.getPageNo(), param.getPageSize());
        List<LabelInfoFrontBO> labelInfoFrontBOList = convertToBO(pageLabels);
        addUpExtraInfo(labelInfoFrontBOList, labelPermissions, labelCollectInfo);
        result.setLabelInfoList(labelInfoFrontBOList);
        return result;
    }
}
