package com.fliggy.picasso.service.approval.impl;

import java.util.Objects;

import com.alibaba.alipmc.api.model.bpm.ProcessInstance;
import com.alibaba.fastjson.JSON;

import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoBO;
import com.fliggy.picasso.common.enums.ChangeRecordSourceTypeEnum;
import com.fliggy.picasso.common.enums.bpms.ApprovalApplySceneEnum;
import com.fliggy.picasso.common.enums.crowd.CrowdTypeEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.entity.bo.CrowdInfoBO;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class CrowdExportApprovalServiceImpl extends AbstractApprovalServiceImpl {

    @Switch(description = "跳过bpms审批流程")
    public Boolean CROWD_EXPORT_SKIP_BPMS_APPROVAL = false;

    @Override
    public ApprovalApplySceneEnum getApplyScene() {
        return ApprovalApplySceneEnum.CROWD_EXPORT;
    }

    @Override
    public Boolean approvalJudge(CrowdInfoBO crowdInfoBO) {
        if (Objects.isNull(crowdInfoBO)) {
            log.error("CrowdExportApprovalServiceImpl approvalJudge exception: crowdMetaInfoVO is null");
            throw new ParamErrorException("crowdMetaInfoVO is null");
        }

        if (Objects.isNull(crowdInfoBO.getCrowdType()) || !Objects.equals(crowdInfoBO.getCrowdType(), CrowdTypeEnum.LABEL_CROWD)) {
            return false;
        }

        // 已有流程在审批中
        if (Objects.nonNull(crowdInfoBO.getId()) && isApprovalRunning(crowdInfoBO.getId(), ApprovalApplySceneEnum.CROWD_EXPORT)) {
            return false;
        }

        Boolean judgeResult = useNeedApprovalAdexLabel(crowdInfoBO);
        if (print_approval_debug_log) {
            log.info("CrowdExportApprovalServiceImpl approvalJudge crowdId:{}, result:{}", crowdInfoBO.getId(), judgeResult);
        }
        return judgeResult;
    }

    @Override
    public void approvalHandle(ApprovalInfoBO approvalInfo) {
        // 已有流程在审批中
        if (isApprovalRunning(approvalInfo.getEntityId(), approvalInfo.getApplyScene())) {
            return;
        }

        // 工号不足6位补零
        if (Objects.isNull(approvalInfo.getApprovalCreator()) || StringUtils.isEmpty(approvalInfo.getApprovalCreator().getEmpId())) {
            log.error("CrowdExportApprovalServiceImpl approvalJudge exception: empId is empty, entityId:{}", approvalInfo.getEntityId());
            throw new ParamErrorException("empId is empty");
        }
        String empId = empIdAddUpZero(approvalInfo.getApprovalCreator().getEmpId());

        // 发起审批
        ProcessInstance result = bpmsCommonService.startProcessInstance(
                ADEX_LABEL_USER_BPMS_CODE,
                "流通中心标签使用审批",
                empId,
                getInitData(approvalInfo),
                BPMS_AUTH_KEY);
        int insertApprovalInfo = approvalInfoService.insert(resolveProcessInstanceResult(approvalInfo, result));
        if (insertApprovalInfo <= 0) {
            log.error("CrowdExportApprovalServiceImpl approvalHandle exception: 创建审批流失败, entityId:{}", approvalInfo.getEntityId());
            throw new RuntimeException("创建审批流失败");
        }

        // 记录审批信息
        int updateRecord = picassoRecordService.updateApprovalInfo(String.valueOf(approvalInfo.getEntityId()), ChangeRecordSourceTypeEnum.CROWD_EXPORT
                , approvalInfo.getApplyScene(), result.getProcessInstanceId());
        if (updateRecord <= 0) {
            log.error("CrowdExportApprovalServiceImpl approvalHandle exception: 更新记录失败, entityId:{}", approvalInfo.getEntityId());
            throw new RuntimeException("更新记录失败");
        }
        if (print_approval_debug_log) {
            log.info("CrowdExportApprovalServiceImpl approvalHandle success: entityId:{}, result:{}", approvalInfo.getEntityId(), JSON.toJSONString(result));
        }
    }

    @Override
    public void approvalCallBack(String approvalId) {
        ApprovalInfoBO approvalInfoBO = approvalInfoService.queryByApprovalId(approvalId);
        if (Objects.isNull(approvalInfoBO)) {
            log.error("CrowdExportApprovalServiceImpl approvalCallBack exception: approvalId is not exist, approvalId:{}", approvalId);
            return;
        }

        ProcessInstance processInstance = null;
        if (CROWD_EXPORT_SKIP_BPMS_APPROVAL) {
            processInstance = mockProcessInstance(approvalId);
        } else {
            processInstance = bpmsCommonService.getProcessInstance(approvalId, BPMS_AUTH_KEY);
        }
        approvalInfoBO.setApprovalStatus(resolveApprovalStatus(processInstance));
        Boolean updateApproval = approvalInfoService.updateStatusWhileDiff(approvalInfoBO);
        if (!updateApproval) {
            log.error("CrowdExportApprovalServiceImpl approvalCallBack exception: update approvalInfo fail, approvalId:{}", approvalId);
        }
        if (print_approval_debug_log) {
            log.info("CrowdExportApprovalServiceImpl approvalCallBack success: approvalId:{}, result:{}", approvalId, JSON.toJSONString(processInstance));
        }
    }
}
