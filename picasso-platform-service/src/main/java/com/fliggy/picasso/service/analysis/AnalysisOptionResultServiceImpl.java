package com.fliggy.picasso.service.analysis;

import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.fliggy.olap.client.domain.ConditionParam;
import com.fliggy.olap.client.domain.OlapQueryResult;
import com.fliggy.picasso.analysis.AnalysisExecutor;
import com.fliggy.picasso.analysis.AnalysisMeasureQueryHandler;
import com.fliggy.picasso.analysis.AnalysisProfileQueryHandler;
import com.fliggy.picasso.analysis.AnalysisProfileQueryWebFormatHandler;
import com.fliggy.picasso.analysis.AnalysisProfileQueryWebFormatResult;
import com.fliggy.picasso.analysis.AnalysisQueryEngine;
import com.fliggy.picasso.analysis.AnalysisQueryHandler;
import com.fliggy.picasso.analysis.AnalysisQueryParam;
import com.fliggy.picasso.analysis.AnalysisQueryParam.AnalysisQueryParamBuilder;
import com.fliggy.picasso.analysis.AnalysisQueryParam.Filter;
import com.fliggy.picasso.analysis.AnalysisQueryParam.Operator;
import com.fliggy.picasso.client.entity.CrowdMetaInfoSummary;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.dao.AnalysisOptionResultDO;
import com.fliggy.picasso.dao.AnalysisOptionResultParam;
import com.fliggy.picasso.dao.AnalysisOptionResultParam.Criteria;
import com.fliggy.picasso.mapper.picasso.AnalysisOptionResultDAO;
import com.google.common.collect.Lists;
import groovy.swing.factory.ActionFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fliggy.picasso.analysis.AnalysisConstants.OLAP_MEASURE_PICASSO_GROUP_USER_CNT;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class AnalysisOptionResultServiceImpl implements AnalysisOptionResultService {

    private static final Logger LOG = LoggerFactory.getLogger(AnalysisOptionResultServiceImpl.class);

    @Autowired
    private AnalysisOptionResultDAO analysisOptionResultDAO;

    @Autowired
    private AnalysisOptionResultConverter analysisOptionResultConverter;

    @Autowired
    private AnalysisMetaInfoService analysisMetaInfoService;

    @Override
    public List<AnalysisOptionResultDTO> listByAnalysisId(Long id) {
        AnalysisOptionResultParam analysisOptionResultParam = new AnalysisOptionResultParam();
        Criteria criteria = analysisOptionResultParam.createCriteria();
        criteria.andAnalysisIdEqualTo(id);

        List<AnalysisOptionResultDTO> result = Lists.newArrayList();
        List<AnalysisOptionResultDO> list = analysisOptionResultDAO.selectByParamWithBLOBs(
            analysisOptionResultParam);
        if (null == list || list.isEmpty()) {
            return result;
        }

        for (AnalysisOptionResultDO record : list) {
            AnalysisOptionResultDTO analysisMetaInfoDTO = analysisOptionResultConverter.convertFromDO(record);
            result.add(analysisMetaInfoDTO);
        }
        return result;
    }

    /**
     * 创建
     *
     * @param analysisOptionResultDTO analysisOptionResultDTO
     */
    @Override
    public void create(AnalysisOptionResultDTO analysisOptionResultDTO) {

        if (analysisOptionResultDTO.getAnalysisId() == null) {
            throw new ParamErrorException("analysisId can't be null!");
        }

        if (analysisOptionResultDTO.getAnalysisOptConfig() == null) {
            throw new ParamErrorException("analysisOptConfig can't be null!");
        }

        if (analysisOptionResultDTO.getAnalysisOptStatus() == null) {
            throw new ParamErrorException("analysisOptStatus can't be null!");
        }

        Date now = new Date();
        analysisOptionResultDTO.setGmtCreate(now);
        analysisOptionResultDTO.setGmtModified(now);

        AnalysisOptionResultDO analysisOptionResultDO = analysisOptionResultConverter.convertFromDTO(
            analysisOptionResultDTO);

        analysisOptionResultDAO.insert(analysisOptionResultDO);

        //返回ID
        analysisOptionResultDTO.setId(analysisOptionResultDO.getId());
    }

    @Override
    public AnalysisOptionResultDTO findById(Long id) {
        AnalysisOptionResultParam analysisOptionResultParam = new AnalysisOptionResultParam();
        Criteria criteria = analysisOptionResultParam.createCriteria();
        criteria.andIdEqualTo(id);

        List<AnalysisOptionResultDO> list = analysisOptionResultDAO.selectByParamWithBLOBs(
            analysisOptionResultParam);

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
            return analysisOptionResultConverter.convertFromDO(list.get(0));
        }

        return null;
    }

    @Override
    public void updateSelective(AnalysisOptionResultDTO analysisOptionResultDTO) {
        AnalysisOptionResultDO record = analysisOptionResultConverter.convertFromDTO(
            analysisOptionResultDTO);

        analysisOptionResultDAO.updateByPrimaryKeySelective(record);
    }

    @Override
    public void update(AnalysisOptionResultDTO analysisOptionResultDTO) {
        AnalysisOptionResultDO record = analysisOptionResultConverter.convertFromDTO(
            analysisOptionResultDTO);

        analysisOptionResultDAO.updateByPrimaryKeyWithBLOBs(record);
    }

    @Override
    public void delete(Long id) {
        analysisOptionResultDAO.deleteByPrimaryKey(id);
    }

    @Override
    public List<AnalysisOptionResultDTO> previewAndSaveProfileAnalysisOpts(AnalysisProfileOptParam param) {

        // 查询分析元数据信息
        AnalysisMetaInfoDTO analysisMetaInfo = analysisMetaInfoService.findById(param.getAnalysisId());

        // 初始化分析子项Action
        List<AnalysisOptAction<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult>>
            analysisOptionAddActions
            = buildProfileAddAction(param, analysisMetaInfo);

        // 执行分析子项Action
        return doAnalysisOptionQueryAction(analysisOptionAddActions);
    }

    @Override
    public List<AnalysisOptionResultDTO> previewProfileAnalysisOpts(AnalysisProfileOptParam param) {
        // 查询分析元数据信息
        AnalysisMetaInfoDTO analysisMetaInfo = analysisMetaInfoService.findById(param.getAnalysisId());

        // 初始化分析子项Action
        List<AnalysisOptAction<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult>> analysisOptionAddActions
            = buildProfilePreviewAction(param, analysisMetaInfo);

        // 执行分析子项Action
        return doAnalysisOptionQueryAction(analysisOptionAddActions);
    }

    @Override
    public AnalysisOptionResultDTO addMeasureAnalysisOpt(AnalysisMeasureOptAddParam param,
                                                         AnalysisMetaInfoDTO analysisMetaInfoDTO) {

        // 构造 AnalysisQueryParam
        AnalysisQueryParam queryParam = buildAnalysisMeasureQueryParam(param, analysisMetaInfoDTO);

        AnalysisOptionResultDTO analysisOptionResultDTO = new AnalysisOptionResultDTO();
        analysisOptionResultDTO.setAnalysisId(analysisMetaInfoDTO.getId());
        analysisOptionResultDTO.setAnalysisName(analysisMetaInfoDTO.getAnalysisName());
        analysisOptionResultDTO.setAnalysisType(analysisMetaInfoDTO.getAnalysisType());

        // 初始化
        analysisOptionResultDTO.setAnalysisOptStatus(AnalysisOptStatus.INIT);
        // 保存请求参数
        analysisOptionResultDTO.setAnalysisOptConfig((JSONObject)JSON.toJSON(queryParam));

        create(analysisOptionResultDTO);

        return analysisOptionResultDTO;
    }

    @Override
    public List<AnalysisOptionResultDTO> previewAndSaveMeasureAnalysisOpt(AnalysisMeasureOptAddParam param) {

        // 查询分析元数据信息
        AnalysisMetaInfoDTO analysisMetaInfo = analysisMetaInfoService.findById(param.getAnalysisId());

        // 初始化分析子项创建Action
        List<AnalysisOptExecuteAndUpdateAction<AnalysisQueryParam, OlapQueryResult>>
            analysisOptionAddActions
            = buildMeasureUpdateActions(param, analysisMetaInfo);

        return doAnalysisOptionQueryAction(analysisOptionAddActions);
    }

    @Override
    public List<AnalysisOptionResultDTO> reload(Long id) {

        // 查询分析元数据信息
        AnalysisMetaInfoDTO analysisMetaInfo = analysisMetaInfoService.findById(id);

        // 初始化分析子项创建Action
        List<AnalysisOptExecuteAndUpdateAction<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult>>
            analysisOptionAddActions
            = buildProfileUpsertAction(analysisMetaInfo);

        return doAnalysisOptionQueryAction(analysisOptionAddActions);
    }

    @Autowired
    private AnalysisExecutor analysisExecutor;

    @Autowired
    private AnalysisProfileQueryHandler analysisProfileQueryHandler;

    @Autowired
    private AnalysisMeasureQueryHandler analysisMeasureQueryHandler;

    @Autowired
    private AnalysisProfileQueryWebFormatHandler analysisProfileQueryWebFormatHandler;

    private ActionFactory actionFactory = new ActionFactory();

    /**
     * 构造画像分析Actions
     *
     * @param param            AnalysisProfileOptParam
     * @param analysisMetaInfo AnalysisMetaInfoDTO
     * @return AnalysisOptExecuteAndSaveAction集合
     */
    private List<AnalysisOptAction<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult>> buildProfileAddAction(
        AnalysisProfileOptParam param,
        AnalysisMetaInfoDTO analysisMetaInfo) {
        return buildProfileAction(param, analysisMetaInfo, new AnalysisOptExecuteAndSaveActionBuilder());
    }

    /**
     * 构造画像分析Actions
     *
     * @param param            AnalysisProfileOptParam
     * @param analysisMetaInfo AnalysisMetaInfoDTO
     * @return AnalysisOptExecuteAndSaveAction集合
     */
    private List<AnalysisOptAction<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult>> buildProfilePreviewAction(
        AnalysisProfileOptParam param,
        AnalysisMetaInfoDTO analysisMetaInfo) {
        return buildProfileAction(param, analysisMetaInfo, new AnalysisOptExecuteActionBuilder());
    }

    /**
     * @param analysisMetaInfo
     * @return
     */
    private List<AnalysisOptExecuteAndUpdateAction<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult>> buildProfileUpsertAction(
        AnalysisMetaInfoDTO analysisMetaInfo) {

        List<AnalysisOptionResultDTO> opts = listByAnalysisId(analysisMetaInfo.getId());

        List<AnalysisOptExecuteAndUpdateAction<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult>> actions = Lists
            .newArrayList();
        if (CollectionUtils.isNotEmpty(opts)) {

            opts.forEach(
                analysisOptionResultDTO -> {

                    AnalysisQueryParam queryParam = JSON.toJavaObject(analysisOptionResultDTO.getAnalysisOptConfig(),
                        AnalysisQueryParam.class);

                    // 构造 AnalysisOptionQueryAction
                    AnalysisOptExecuteAndUpdateAction<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult> action
                        = new AnalysisOptExecuteAndUpdateAction<>(analysisOptionResultDTO,
                        new AnalysisQueryEngine<>(analysisProfileQueryWebFormatHandler, analysisExecutor),
                        queryParam
                    );

                    actions.add(action);
                }
            );
        }

        return actions;
    }

    /**
     * 构造画像分析Actions
     *
     * @param param            AnalysisProfileOptParam
     * @param analysisMetaInfo AnalysisMetaInfoDTO
     * @return AnalysisOptExecuteAndSaveAction集合
     */
    private List<AnalysisOptAction<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult>> buildProfileAction(
        AnalysisProfileOptParam param,
        AnalysisMetaInfoDTO analysisMetaInfo,
        AbstractActionBuilder abstractActionBuilder) {

        List<AnalysisOptAction<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult>> actions = Lists
            .newArrayList();

        // 每个维度组是一个构建一个分析子项（如添加单维分析中添加多个）
        for (List<String> dimensionGroup : param.getDimensionGroups()) {

            if (CollectionUtils.isEmpty(dimensionGroup)) {
                continue;
            }

            List<CrowdMetaInfoSummary> groups = analysisMetaInfo.getAnalysisConfig().getGroups();

            // 单维分析，支持多人群
            if (dimensionGroup.size() == 1) {
                // 组装 AnalysisOptionResultDTO
                AnalysisOptionResultDTO analysisOptionResultDTO = new AnalysisOptionResultDTO();
                analysisOptionResultDTO.setAnalysisId(analysisMetaInfo.getId());
                analysisOptionResultDTO.setAnalysisName(analysisMetaInfo.getAnalysisName());
                analysisOptionResultDTO.setAnalysisType(analysisMetaInfo.getAnalysisType());

                // 构造 AnalysisQueryParam
                AnalysisQueryParam queryParam = buildAnalysisProfileQueryParam(dimensionGroup, param.getFilters(),
                    groups);

                // 构造 AnalysisOptionQueryAction
                AnalysisOptAction<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult> action =
                    abstractActionBuilder.newInstance(analysisOptionResultDTO, queryParam,
                        analysisProfileQueryWebFormatHandler);

                actions.add(action);
            } else {
                // 多维分析，人群需要拆解多个子项
                groups.forEach(
                    group -> {
                        // 组装 AnalysisOptionResultDTO
                        AnalysisOptionResultDTO analysisOptionResultDTO = new AnalysisOptionResultDTO();
                        analysisOptionResultDTO.setAnalysisId(analysisMetaInfo.getId());
                        analysisOptionResultDTO.setAnalysisName(analysisMetaInfo.getAnalysisName());
                        analysisOptionResultDTO.setAnalysisType(analysisMetaInfo.getAnalysisType());

                        // 构造 AnalysisQueryParam
                        AnalysisQueryParam queryParam = buildAnalysisProfileQueryParam(dimensionGroup,
                            param.getFilters(),
                            Lists.newArrayList(group));

                        // 构造 AnalysisOptionQueryAction
                        AnalysisOptAction<AnalysisQueryParam, AnalysisProfileQueryWebFormatResult> action =
                            abstractActionBuilder.newInstance(analysisOptionResultDTO, queryParam,
                                analysisProfileQueryWebFormatHandler);

                        actions.add(action);
                    }
                );
            }
        }

        return actions;
    }

    private List<AnalysisOptExecuteAndUpdateAction<AnalysisQueryParam, OlapQueryResult>> buildMeasureUpdateActions(
        AnalysisMeasureOptAddParam param,
        AnalysisMetaInfoDTO analysisMetaInfo) {

        // 构造 AnalysisQueryParam
        AnalysisQueryParam queryParam = buildAnalysisMeasureQueryParam(param, analysisMetaInfo);

        List<AnalysisOptionResultDTO> opts = listByAnalysisId(analysisMetaInfo.getId());
        if (opts.size() != 1) {
            throw new RuntimeException("Found More Than One AnalysisOpt! AnalysisID:" + analysisMetaInfo.getId());
        }

        AnalysisOptionResultDTO analysisOptionResultDTO = opts.get(0);
        List<AnalysisOptExecuteAndUpdateAction<AnalysisQueryParam, OlapQueryResult>> actions = Lists
            .newArrayList();

        // 构造 AnalysisOptionQueryAction
        AnalysisOptExecuteAndUpdateAction<AnalysisQueryParam, OlapQueryResult> action
            = new AnalysisOptExecuteAndUpdateAction<>(analysisOptionResultDTO,
            new AnalysisQueryEngine<>(analysisMeasureQueryHandler, analysisExecutor),
            queryParam
        );

        actions.add(action);

        return actions;
    }

    /**
     * @param dimensionGroup 维度分组
     * @param filters        过滤项
     * @param groups         人群集合
     * @return AnalysisQueryParam
     */
    private AnalysisQueryParam buildAnalysisProfileQueryParam(List<String> dimensionGroup,
                                                              List<Filter> filters,
                                                              List<CrowdMetaInfoSummary> groups) {
        AnalysisQueryParamBuilder builder = AnalysisQueryParamBuilder.anAnalysisQueryParam();
        builder.addMeasure(OLAP_MEASURE_PICASSO_GROUP_USER_CNT);

        if (CollectionUtils.isNotEmpty(dimensionGroup)) {
            dimensionGroup.forEach(builder::addDimension);
        }
        if (CollectionUtils.isNotEmpty(filters)) {
            filters.forEach(builder::addFilter);
        }

        if (CollectionUtils.isNotEmpty(groups)) {
            groups.forEach(builder::addGroup);
        }

        return builder.build();
    }

    /**
     * 构造指标分析参数
     *
     * @param param
     * @param analysisMetaInfo
     * @return
     */
    private AnalysisQueryParam buildAnalysisMeasureQueryParam(AnalysisMeasureOptAddParam param,
                                                              AnalysisMetaInfoDTO analysisMetaInfo) {
        AnalysisQueryParam analysisQueryParam = new AnalysisQueryParam();
        List<CrowdMetaInfoSummary> groups = analysisMetaInfo.getAnalysisConfig().getGroups();
        if (CollectionUtils.isEmpty(groups)) {
            throw new RuntimeException("Groups In AnalysisMetaInfo Can't Be Empty!");
        }

        analysisQueryParam.setGroups(groups);
        analysisQueryParam.setDimensions(param.getDimensions());
        analysisQueryParam.setMeasures(param.getMeasures());
        analysisQueryParam.setLimit(param.getLimit());

        if (param.getFilter() != null) {
            ConditionParam condition = param.getFilter();

            String concat = condition.getConcat();
            if (!"AND".equals(concat)) {
                throw new RuntimeException("Not Support Concat Operator! Concat:" + concat);
            }

            List<Filter> filters = Lists.newArrayList();
            for (ConditionParam conditionParam : condition.getChildren()) {

                Operator operator = Operator.fromCode(conditionParam.getOperator());
                if (operator == null) {
                    throw new RuntimeException("Not Support Operator! Operator:" + operator);
                }
                filters.add(new Filter(conditionParam.getKey(), operator, conditionParam.getValue()));
            }
            analysisQueryParam.setFilters(filters);
        }

        analysisQueryParam.setStart(param.getStart());
        analysisQueryParam.setEnd(param.getEnd());

        return analysisQueryParam;
    }

    /**
     * @param actions actions
     * @return List<AnalysisOptionResultDTO>
     */
    private <A extends AnalysisOptAction> List<AnalysisOptionResultDTO> doAnalysisOptionQueryAction(
        List<A> actions) {

        //TODO 并行改造
        List<AnalysisOptionResultDTO> res = Lists.newArrayList();
        for (AnalysisOptAction action : actions) {
            AnalysisOptionResultDTO analysisOptionResultDTO = action.execute();
            res.add(analysisOptionResultDTO);
        }

        return res;
    }

    /**
     * 分析子项Action封装类
     *
     * @param <T> 查询参数类型
     * @param <K> 查询结果类型
     */
    abstract static class AnalysisOptAction<T, K> {

        final AnalysisOptionResultDTO analysisOptionResultDTO;
        final AnalysisQueryEngine<T, K> analysisQueryEngine;

        final T queryParam;
        K queryResult;

        public AnalysisOptAction(AnalysisOptionResultDTO analysisOptionResultDTO,
                                 AnalysisQueryEngine<T, K> analysisQueryEngine,
                                 T queryParam) {
            this.analysisOptionResultDTO = analysisOptionResultDTO;
            this.analysisQueryEngine = analysisQueryEngine;
            this.queryParam = queryParam;
        }

        abstract AnalysisOptionResultDTO execute();
    }

    /**
     * 封装分析子项执行&创建的动作
     */
    final class AnalysisOptExecuteAndSaveAction<T, K> extends AnalysisOptAction<T, K> {

        public AnalysisOptExecuteAndSaveAction(AnalysisOptionResultDTO analysisOptionResultDTO,
                                               AnalysisQueryEngine<T, K> analysisQueryEngine, T queryParam) {
            super(analysisOptionResultDTO, analysisQueryEngine, queryParam);
        }

        @Override
        AnalysisOptionResultDTO execute() {

            // 初始化
            initAnalysisOptionResult();

            // 执行查询
            try {
                queryResult = executeQuery();
                analysisOptionResultDTO.setAnalysisOptResult((JSONObject)JSON.toJSON(queryResult));
            } catch (Exception e) {
                // 记录失败结果
                String msg = "Execute Olap Query Error! Message:" + e.getMessage();
                LOG.error(msg, e);
                recordFailedResult(e.getMessage(), e);
                return super.analysisOptionResultDTO;
            }

            // 记录成功结果
            recordSuccessResult();

            return super.analysisOptionResultDTO;
        }

        /**
         *
         */
        private void recordSuccessResult() {
            analysisOptionResultDTO.setAnalysisOptStatus(AnalysisOptStatus.SUCCESS);
            analysisOptionResultDTO.setLastUpdateTime(new Date());

            update(this.analysisOptionResultDTO);
        }

        private void recordFailedResult(String message, Exception e) {
            analysisOptionResultDTO.setAnalysisOptStatus(AnalysisOptStatus.ERROR);
            analysisOptionResultDTO.setLastUpdateTime(new Date());
            analysisOptionResultDTO.setExtInfo(message);

            update(this.analysisOptionResultDTO);
        }

        private K executeQuery() {
            K res = analysisQueryEngine.query(this.queryParam);
            return res;
        }

        private void initAnalysisOptionResult() {
            // 初始化
            analysisOptionResultDTO.setAnalysisOptStatus(AnalysisOptStatus.RUNNING);

            // 保存请求参数
            analysisOptionResultDTO.setAnalysisOptConfig((JSONObject)JSON.toJSON(this.queryParam));

            create(analysisOptionResultDTO);
        }
    }

    /**
     * 封装分析子项执行&创建的动作
     */
    final class AnalysisOptExecuteAndUpdateAction<T, K> extends AnalysisOptAction<T, K> {

        public AnalysisOptExecuteAndUpdateAction(AnalysisOptionResultDTO analysisOptionResultDTO,
                                                 AnalysisQueryEngine<T, K> analysisQueryEngine, T queryParam) {
            super(analysisOptionResultDTO, analysisQueryEngine, queryParam);
        }

        @Override
        AnalysisOptionResultDTO execute() {

            // 初始化
            updateAnalysisOptionResult();

            // 执行查询
            try {
                queryResult = executeQuery();
                analysisOptionResultDTO.setAnalysisOptResult((JSONObject)JSON.toJSON(queryResult));
            } catch (Exception e) {
                // 记录失败结果
                String msg = "Execute Olap Query Error! Message:" + e.getMessage();
                LOG.error(msg, e);
                recordFailedResult(e.getMessage(), e);
                return super.analysisOptionResultDTO;
            }

            // 记录成功结果
            recordSuccessResult();

            return super.analysisOptionResultDTO;
        }

        /**
         *
         */
        private void recordSuccessResult() {
            analysisOptionResultDTO.setAnalysisOptStatus(AnalysisOptStatus.SUCCESS);
            analysisOptionResultDTO.setLastUpdateTime(new Date());

            update(this.analysisOptionResultDTO);
        }

        private void recordFailedResult(String message, Exception e) {
            analysisOptionResultDTO.setAnalysisOptStatus(AnalysisOptStatus.ERROR);
            analysisOptionResultDTO.setLastUpdateTime(new Date());

            analysisOptionResultDTO.setExtInfo(message);

            update(this.analysisOptionResultDTO);
        }

        private K executeQuery() {
            return analysisQueryEngine.query(this.queryParam);
        }

        private void updateAnalysisOptionResult() {
            // 初始化
            analysisOptionResultDTO.setAnalysisOptStatus(AnalysisOptStatus.RUNNING);
            // 保存请求参数
            analysisOptionResultDTO.setAnalysisOptConfig((JSONObject)JSON.toJSON(this.queryParam));
            update(this.analysisOptionResultDTO);
        }
    }

    /**
     * 封装分析子项执行的动作
     */
    static final class AnalysisOptExecuteAction<T, K> extends AnalysisOptAction<T, K> {

        public AnalysisOptExecuteAction(AnalysisOptionResultDTO analysisOptionResultDTO,
                                        AnalysisQueryEngine<T, K> analysisQueryEngine, T queryParam) {
            super(analysisOptionResultDTO, analysisQueryEngine, queryParam);
        }

        @Override
        AnalysisOptionResultDTO execute() {
            try {
                queryResult = executeQuery();
                analysisOptionResultDTO.setAnalysisOptStatus(AnalysisOptStatus.SUCCESS);
                analysisOptionResultDTO.setLastUpdateTime(new Date());
                analysisOptionResultDTO.setAnalysisOptConfig((JSONObject)JSON.toJSON(this.queryParam));
                analysisOptionResultDTO.setAnalysisOptResult((JSONObject)JSON.toJSON(queryResult));
            } catch (Exception e) {
                throw new RuntimeException("Execute OLAP Query Error!", e);
            }

            return this.analysisOptionResultDTO;
        }

        private K executeQuery() {
            return analysisQueryEngine.query(this.queryParam);
        }
    }

    abstract class AbstractActionBuilder {
        abstract <T, K> AnalysisOptAction<T, K> newInstance(AnalysisOptionResultDTO analysisOptionResultDTO,
                                                            T queryParam,
                                                            AnalysisQueryHandler<T, K> handler);
    }

    final class AnalysisOptExecuteAndSaveActionBuilder extends AbstractActionBuilder {

        @Override
        public <T, K> AnalysisOptExecuteAndSaveAction<T, K> newInstance(
            AnalysisOptionResultDTO analysisOptionResultDTO,
            T queryParam,
            AnalysisQueryHandler<T, K> handler) {
            // 构造 AnalysisOptionQueryAction
            AnalysisOptExecuteAndSaveAction<T, K> action
                = new AnalysisOptExecuteAndSaveAction<T, K>(analysisOptionResultDTO,
                new AnalysisQueryEngine<>(handler, analysisExecutor),
                queryParam
            );
            return action;
        }

    }

    final class AnalysisOptExecuteActionBuilder extends AbstractActionBuilder {

        @Override
        public <T, K> AnalysisOptExecuteAction<T, K> newInstance(
            AnalysisOptionResultDTO analysisOptionResultDTO,
            T queryParam,
            AnalysisQueryHandler<T, K> handler) {
            // 构造 AnalysisOptionQueryAction
            AnalysisOptExecuteAction<T, K> action
                = new AnalysisOptExecuteAction<T, K>(analysisOptionResultDTO,
                new AnalysisQueryEngine<>(handler, analysisExecutor),
                queryParam
            );
            return action;
        }

    }
}


