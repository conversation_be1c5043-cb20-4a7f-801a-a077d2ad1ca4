package com.fliggy.picasso.service.buildsql.parseCondition.label;

import com.fliggy.picasso.common.enums.label.LabelOperatorEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 生成标签对应的表达式父类
 */
public abstract class BaseLabelExpressionGenerator implements InitializingBean {

    ExpressionGeneratorResolver expressionGeneratorResolver;

    public BaseLabelExpressionGenerator(ExpressionGeneratorResolver expressionGeneratorResolver) {
        this.expressionGeneratorResolver = expressionGeneratorResolver;
    }

    /**
     * 生成标签对应的表达式
     *
     * @param labelName
     * @param enumValues
     * @param operatorType
     * @return
     */
    public Map<String, String> generateExpression(String labelName, List<String> enumValues, LabelOperatorEnum operatorType) {
        if (CollectionUtils.isEmpty(enumValues) || StringUtils.isBlank(labelName) || operatorType == null) {
            throw new ParamErrorException("解析标签圈人逻辑表达式异常，标签类型或筛选条件为空。");
        }
        Map<String, String> labelExpression = new HashMap<>();
        String key = labelName.split("\\.")[1];
        String fieldName = labelName.split(" ")[0];
        String expression = buildExpression(fieldName, enumValues, operatorType);
        labelExpression.put(key, expression);
        return labelExpression;
    }

    /**
     * 构建表达式
     *
     * @param labelName
     * @param enumValues
     * @param operatorType
     * @return
     */
    abstract String buildExpression(String labelName, List<String> enumValues, LabelOperatorEnum operatorType);
}
