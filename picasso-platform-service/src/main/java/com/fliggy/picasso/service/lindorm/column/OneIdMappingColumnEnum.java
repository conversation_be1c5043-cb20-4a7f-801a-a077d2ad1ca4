package com.fliggy.picasso.service.lindorm.column;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum OneIdMappingColumnEnum {
    /**
     * one id mapping 列名
     */
    ONE_ID_REVERSE("scrm_one_id_reverse", "scrm_one_id_reverse id"),
    DECIDE_ONE_ID_REVERSE("device_one_id_reverse", "device_one_id_reverse id"),
    INDEX("index", "序号"),
    KEY_TYPE("key_type", "id类型"),
    KEY_VALUE("key_value", "id值"),
    ;

    private final String code;
    private final String desc;

    OneIdMappingColumnEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
