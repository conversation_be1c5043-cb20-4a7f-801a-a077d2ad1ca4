package com.fliggy.picasso.service.approval;

import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoBO;
import com.fliggy.picasso.common.enums.bpms.ApprovalApplySceneEnum;
import com.fliggy.picasso.entity.bo.CrowdInfoBO;

public interface ApprovalService {

    /**
     * 获取审批场景
     * @return
     */
    ApprovalApplySceneEnum getApplyScene();

    /**
     * 审批判断
     * @param crowdInfoBO
     * @return
     */
    Boolean approvalJudge(CrowdInfoBO crowdInfoBO);

    /**
     * 审批处理
     * @param approvalInfo
     * @return
     */
    void approvalHandle(ApprovalInfoBO approvalInfo);

    /**
     * 审批回调
     * @param approvalId
     * @return
     */
    void approvalCallBack(String approvalId);

    /**
     * 是否使用流通中心标签
     * @param crowdId
     * @return
     */
    Boolean useAdexLabel(Long crowdId);

    /**
     * 是否使用需要审批的流通中心标签
     * @param crowdInfoBO
     * @return
     */
    Boolean useNeedApprovalAdexLabel(CrowdInfoBO crowdInfoBO);
}
