package com.fliggy.picasso.service.analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;

import com.fliggy.picasso.analysis.AnalysisType;
import com.fliggy.picasso.dao.AnalysisOptionResultDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class AnalysisOptionResultConverter {

    /**
     * DTO模型转换成DO模型
     *
     * @param analysisOptionResultDTO
     */
    public AnalysisOptionResultDO convertFromDTO(AnalysisOptionResultDTO analysisOptionResultDTO) {
        AnalysisOptionResultDO analysisOptionResultDO = new AnalysisOptionResultDO();
        BeanUtils.copyProperties(analysisOptionResultDTO, analysisOptionResultDO);

        if (analysisOptionResultDTO.getAnalysisOptStatus() != null) {
            analysisOptionResultDO.setAnalysisOptStatus(analysisOptionResultDTO.getAnalysisOptStatus().name());
        }
        if (analysisOptionResultDTO.getAnalysisType() != null) {
            analysisOptionResultDO.setAnalysisType(analysisOptionResultDTO.getAnalysisType().getName());
        }
        if (analysisOptionResultDTO.getAnalysisOptConfig() != null) {
            analysisOptionResultDO.setAnalysisOptConfig(
                analysisOptionResultDTO.getAnalysisOptConfig().toString(SerializerFeature.WriteMapNullValue));
        }
        if (analysisOptionResultDTO.getAnalysisOptResult() != null) {
            analysisOptionResultDO.setAnalysisOptResult(
                analysisOptionResultDTO.getAnalysisOptResult().toString(SerializerFeature.WriteMapNullValue));
        }

        return analysisOptionResultDO;
    }

    /**
     * DO模型转换成DTO模型
     *
     * @param analysisOptionResultDO
     */
    public AnalysisOptionResultDTO convertFromDO(AnalysisOptionResultDO analysisOptionResultDO) {
        AnalysisOptionResultDTO analysisOptionResultDTO = new AnalysisOptionResultDTO();
        BeanUtils.copyProperties(analysisOptionResultDO, analysisOptionResultDTO);

        if (analysisOptionResultDO.getAnalysisOptStatus() != null) {
            analysisOptionResultDTO.setAnalysisOptStatus(
                AnalysisOptStatus.valueOf(analysisOptionResultDO.getAnalysisOptStatus()));
        }
        if (analysisOptionResultDO.getAnalysisType() != null) {
            analysisOptionResultDTO.setAnalysisType(AnalysisType.fromCode(analysisOptionResultDO.getAnalysisType()));
        }
        if (analysisOptionResultDO.getAnalysisOptConfig() != null) {
            analysisOptionResultDTO.setAnalysisOptConfig(
                JSON.parseObject(analysisOptionResultDO.getAnalysisOptConfig()));
        }
        if (analysisOptionResultDO.getAnalysisOptResult() != null) {
            analysisOptionResultDTO.setAnalysisOptResult(
                JSON.parseObject(analysisOptionResultDO.getAnalysisOptResult()));
        }

        return analysisOptionResultDTO;
    }
}