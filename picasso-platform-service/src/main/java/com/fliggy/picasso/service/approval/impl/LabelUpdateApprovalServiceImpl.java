package com.fliggy.picasso.service.approval.impl;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.alipmc.api.model.bpm.ProcessInstance;
import com.alibaba.fastjson.JSON;

import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoBO;
import com.fliggy.picasso.common.domain.crowd.record.PicassoChangeRecordBO;
import com.fliggy.picasso.common.domain.label.LabelDataConfig;
import com.fliggy.picasso.common.domain.label.LabelInfoDO;
import com.fliggy.picasso.common.domain.label.LabelOdpsSourceConfig;
import com.fliggy.picasso.common.enums.ChangeRecordSourceTypeEnum;
import com.fliggy.picasso.common.enums.LabelScope;
import com.fliggy.picasso.common.enums.bpms.AdexFluxSceneEnum;
import com.fliggy.picasso.common.enums.bpms.ApprovalApplySceneEnum;
import com.fliggy.picasso.common.enums.bpms.BpmsApprovalStatusEnum;
import com.fliggy.picasso.common.enums.label.BizRegionEnum;
import com.fliggy.picasso.common.enums.label.LabelDataDescEnum;
import com.fliggy.picasso.common.enums.label.LabelStatusEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.entity.bo.LabelInfoBackgroundBO;
import com.fliggy.picasso.entity.convertor.LabelBackgroundBoConvertor;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class LabelUpdateApprovalServiceImpl extends AbstractApprovalServiceImpl {

    @Switch(description = "跳过bpms审批流程")
    public Boolean LABEL_UPDATE_SKIP_BPMS_APPROVAL = false;

    @Override
    public ApprovalApplySceneEnum getApplyScene() {
        return ApprovalApplySceneEnum.LABEL_UPDATE;
    }

    @Override
    public void approvalHandle(ApprovalInfoBO approvalInfo) {
        // 已有流程在审批中
        if (isApprovalRunning(approvalInfo.getEntityId(), approvalInfo.getApplyScene())) {
            return;
        }

        // 工号不足6位补零
        if (Objects.isNull(approvalInfo.getApprovalCreator()) || StringUtils.isEmpty(approvalInfo.getApprovalCreator().getEmpId())) {
            log.error("LabelUpdateApprovalServiceImpl approvalHandle exception: empId is empty, entityId:{}", approvalInfo.getEntityId());
            throw new ParamErrorException("empId is empty");
        }
        String empId = empIdAddUpZero(approvalInfo.getApprovalCreator().getEmpId());

        // 获取标签配置
        PicassoChangeRecordBO picassoChangeRecordBO = picassoRecordService.queryLatestBySourceIdAndSourceType(String.valueOf(approvalInfo.getEntityId()), ChangeRecordSourceTypeEnum.LABEL_UPDATE);
        if (Objects.isNull(picassoChangeRecordBO.getAfterData()) || CollectionUtils.isNullOrEmpty(picassoChangeRecordBO.getApprovalInfos())) {
            log.error("LabelUpdateApprovalServiceImpl approvalHandle exception: 标签配置不存在, entityId:{}", approvalInfo.getEntityId());
            return;
        }
        Map<String, String> initData = getInitData(picassoChangeRecordBO);

        // 发起审批
        ProcessInstance result = bpmsCommonService.startProcessInstance(
                ZHUGE_LABEL_UPDATE_BPMS_CODE,
                "诸葛标签创建审批",
                empId,
                initData,
                BPMS_AUTH_KEY);
        int insertApprovalInfo = approvalInfoService.insert(resolveProcessInstanceResult(approvalInfo, result));
        if (insertApprovalInfo <= 0) {
            log.error("LabelUpdateApprovalServiceImpl approvalHandle exception: 创建审批流失败, entityId:{}", approvalInfo.getEntityId());
            throw new RuntimeException("创建审批流失败");
        }

        // 记录审批信息
        int updateRecord = picassoRecordService.updateApprovalInfo(String.valueOf(approvalInfo.getEntityId()), ChangeRecordSourceTypeEnum.LABEL_UPDATE
                , approvalInfo.getApplyScene(), result.getProcessInstanceId());
        if (updateRecord <= 0) {
            log.error("LabelUpdateApprovalServiceImpl approvalHandle exception: 更新记录失败, entityId:{}", approvalInfo.getEntityId());
            throw new RuntimeException("更新记录失败");
        }
        if (print_approval_debug_log) {
            log.info("LabelUpdateApprovalServiceImpl approvalHandle success entityId:{}, result:{}", approvalInfo.getEntityId(), JSON.toJSONString(result));
        }
    }

    @Override
    public void approvalCallBack(String approvalId) {
        ApprovalInfoBO approvalInfoBO = approvalInfoService.queryByApprovalId(approvalId);
        if (Objects.isNull(approvalInfoBO)) {
            log.error("LabelUpdateApprovalServiceImpl approvalCallBack exception: approvalId is not exist, approvalId:{}", approvalId);
            return;
        }

        // 审批流状态更新
        ProcessInstance processInstance = null;
        if (LABEL_UPDATE_SKIP_BPMS_APPROVAL) {
            processInstance = mockProcessInstance(approvalId);
        } else {
            processInstance = bpmsCommonService.getProcessInstance(approvalId, BPMS_AUTH_KEY);
        }
        approvalInfoBO.setApprovalStatus(resolveApprovalStatus(processInstance));
        Boolean updateApproval = approvalInfoService.updateStatusWhileDiff(approvalInfoBO);
        if (!updateApproval) {
            log.error("LabelUpdateApprovalServiceImpl approvalCallBack exception: update approvalInfo fail, approvalId:{}", approvalId);
        }
        if (!Objects.equals(BpmsApprovalStatusEnum.APPROVAL_SUCCESS, approvalInfoBO.getApprovalStatus())) {
            labelInfoService.updateStatus(approvalInfoBO.getEntityId(), LabelStatusEnum.APPROVE_FAILED);
            return;
        }

        // 获取标签配置
        PicassoChangeRecordBO picassoChangeRecordBO = picassoRecordService.queryLatestBySourceIdAndSourceType(String.valueOf(approvalInfoBO.getEntityId()), ChangeRecordSourceTypeEnum.LABEL_UPDATE);
        if (Objects.isNull(picassoChangeRecordBO.getAfterData()) || CollectionUtils.isNullOrEmpty(picassoChangeRecordBO.getApprovalInfos())) {
            return;
        }

        // 标签更新
        LabelInfoDO labelInfoDO = picassoChangeRecordBO.getAfterData().getLabelInfo();
        labelInfoDO.setId(approvalInfoBO.getEntityId());
        labelInfoDO.setStatus(LabelStatusEnum.WAIT_ONLINE.getCode());
        int updateLabel = labelInfoService.updateSelectiveById(approvalInfoBO.getEntityId(), labelInfoDO);
        if (updateLabel <= 0) {
            log.error("LabelUpdateApprovalServiceImpl approvalCallBack exception: update crowd fail, approvalId:{}", approvalId);
        }
        if (print_approval_debug_log) {
            log.info("LabelUpdateApprovalServiceImpl approvalCallBack approvalId:{}, result:{}", approvalId, JSON.toJSONString(processInstance));
        }

        // 流通中心数据回流
        adexFluxLogUtils.printAdexLabelFluxLog(AdexFluxSceneEnum.LABEL_UPDATE, approvalId, approvalInfoBO.getEntityId(), approvalInfoBO.getApprovalCreator().getEmpId());
    }

    protected Map<String, String> getInitData(PicassoChangeRecordBO record) {
        if (Objects.isNull(record) || Objects.isNull(record.getAfterData())
                || Objects.isNull(record.getAfterData().getLabelInfo())) {
            log.error("LabelUpdateApprovalServiceImpl getInitData exception: labelInfoDO is null");
            throw new ParamErrorException("labelInfoDO is null");
        }

        LabelInfoDO labelInfoDO = record.getAfterData().getLabelInfo();
        LabelInfoBackgroundBO labelInfoBO = LabelBackgroundBoConvertor.convertLabelInfoBOFromDO(labelInfoDO);
        Map<String, String> result = new HashMap<>();
        if (Objects.nonNull(labelInfoBO.getBizRegionList())) {
            List<String> bizRegionList = labelInfoBO.getBizRegionList().stream().map(BizRegionEnum::getDesc).collect(Collectors.toList());
            result.put("bizRegionList", JSON.toJSONString(bizRegionList));
        }
        if (Objects.nonNull(labelInfoBO.getProfileCode())) {
            result.put("profileCode", labelInfoBO.getProfileCode().getAccountType());
        }
        if (Objects.nonNull(labelInfoBO.getTimeType())) {
            result.put("timeType", labelInfoBO.getTimeType().getDesc());
        }
        if (Objects.nonNull(labelInfoBO.getLabelType())) {
            result.put("labelType", labelInfoBO.getLabelType().getDesc());
        }
        if (Objects.nonNull(labelInfoBO.getSource())) {
            result.put("source", labelInfoBO.getSource().getDesc());
        }
        if (Objects.nonNull(labelInfoBO.getExpiredType())) {
            result.put("expiredType", labelInfoBO.getExpiredType().getDesc());
        }
        if (Objects.nonNull(labelInfoBO.getExpiredDate())) {
            result.put("expiredDate", labelInfoBO.getExpiredDate().toString());
        }
        if (Objects.nonNull(labelInfoBO.getSourceConfig())) {
            LabelOdpsSourceConfig sourceConfig = labelInfoBO.getSourceConfig();
            if (StringUtils.isNotBlank(sourceConfig.getProject()) && StringUtils.isNotBlank(sourceConfig.getTable())) {
                result.put("tableGuid", sourceConfig.getProject() + "." + sourceConfig.getTable());
            } else if (StringUtils.isNotBlank(sourceConfig.getTable())) {
                result.put("tableGuid", sourceConfig.getTable());
            }
            if (StringUtils.isNotBlank(sourceConfig.getField())) {
                result.put("field", sourceConfig.getField());
            }
            if (Objects.nonNull(sourceConfig.getSqlConfig()) && StringUtils.isNotBlank(sourceConfig.getSqlConfig().getSqlTemplate())) {
                result.put("sqlTemplate", sourceConfig.getSqlConfig().getSqlTemplate());
            }
        }
        if (Objects.nonNull(labelInfoBO.getUpdatePeriod())) {
            result.put("updatePeriod", labelInfoBO.getUpdatePeriod().getName() + "更新");
        }
        if (Objects.nonNull(labelInfoBO.getName())) {
            result.put("name", labelInfoBO.getName());
        }
        if (Objects.nonNull(labelInfoBO.getDescription())) {
            result.put("description", labelInfoBO.getDescription());
        }
        if (Objects.nonNull(labelInfoBO.getOpenScope())) {
            result.put("openScope", labelInfoBO.getOpenScope().getDesc());
        }
        if (Objects.nonNull(labelInfoBO.getSecurityLevel())) {
            result.put("securityLevel", labelInfoBO.getSecurityLevel().toString());
        }
        if (Objects.nonNull(labelInfoBO.getCategoryIdNew())) {
            String categoryName = categoryService.queryCategoryName(labelInfoBO.getCategoryIdNew());
            if (Objects.nonNull(categoryName)) {
                result.put("categoryName", categoryName);
            }
        }
        if (Objects.nonNull(labelInfoBO.getOwner())) {
            result.put("owner", labelInfoBO.getOwner().getEmpId() + "-" + labelInfoBO.getOwner().getNickName());
        }
        if (Objects.nonNull(labelInfoBO.getProcessType())) {
            result.put("processType", labelInfoBO.getProcessType().getDesc());
        }
        if (Objects.nonNull(labelInfoBO.getDataSourceConfig()) && Objects.nonNull(labelInfoBO.getDataSourceConfig().getDataSource())) {
            result.put("dataSource", labelInfoBO.getDataSourceConfig().getDataSource().getDesc());
        }
        if (Objects.nonNull(labelInfoBO.getScope())) {
            result.put("scope", Arrays.stream(labelInfoBO.getScope()).map(LabelScope::getDesc).collect(Collectors.joining(",")));
        }
        if (Objects.nonNull(labelInfoBO.getDataType())) {
            result.put("dataType", labelInfoBO.getDataType().getDesc());
        }
        if (Objects.nonNull(labelInfoBO.getDataConfig())) {
            LabelDataConfig dataConfig = labelInfoBO.getDataConfig();
            if (Objects.nonNull(dataConfig.getDataDesc())) {
                result.put("dataDesc", dataConfig.getDataDesc().getDesc());
                if (Objects.equals(dataConfig.getDataDesc(), LabelDataDescEnum.MANAL_INPUT) && Objects.nonNull(dataConfig.getDataValue())) {
                    result.put("dataValue", JSON.toJSONString(dataConfig.getDataValue()));
                } else if (Objects.equals(dataConfig.getDataDesc(), LabelDataDescEnum.COMMON_ENUM) && Objects.nonNull(labelInfoBO.getDimEnumMetaId())) {
                    result.put("dimEnumMetaId", labelInfoBO.getDimEnumMetaId().toString());
                }
            }
        }

        if (Objects.nonNull(record.getBeforeData()) && Objects.nonNull(record.getBeforeData().getLabelInfo())) {
            result.put("isUpdate", "true");
        } else {
            result.put("isUpdate", "false");
        }
        result.put("applyScene", ApprovalApplySceneEnum.LABEL_UPDATE.getCode());
        return result;
    }
}
