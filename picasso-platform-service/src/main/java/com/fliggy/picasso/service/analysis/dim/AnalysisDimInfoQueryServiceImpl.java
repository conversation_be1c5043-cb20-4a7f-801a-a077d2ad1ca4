package com.fliggy.picasso.service.analysis.dim;

import com.alibaba.fastjson.JSON;
import com.fliggy.olap.client.domain.meta.DimValueDTO;
import com.fliggy.olap.client.domain.meta.DimensionDTO;
import com.fliggy.olap.client.service.OlapQueryService;
import com.fliggy.picasso.client.entity.DimMetaInfoSummary;
import com.fliggy.picasso.client.entity.DimValueInfo;
import com.fliggy.picasso.client.service.AnalysisDimInfoQueryService;
import com.fliggy.picasso.common.domain.category.CategoryDO;
import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.service.category.CategoryService;
import com.fliggy.picasso.service.enumvalue.EnumDimDataDTO;
import com.fliggy.picasso.service.enumvalue.LabelEnumValueService;
import com.fliggy.picasso.service.label.LabelInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class AnalysisDimInfoQueryServiceImpl implements AnalysisDimInfoQueryService {

    @Autowired
    private LabelInfoService labelInfoService;
    @Autowired
    private CategoryService categoryService;
    @Resource
    OlapQueryService olapQueryService;
    @Resource
    LabelEnumValueService labelEnumValueService;

    @Override
    public Map<String, List<DimMetaInfoSummary>> listProfileDimMetaInfo(String codeType) {
        List<LabelInfoDTO> labelInfoDTOS = labelInfoService.listOdpsSourceOnUse();
        if (labelInfoDTOS == null || labelInfoDTOS.isEmpty()) {
            throw new RuntimeException("人群标签查询结果为空，请注意查看！");
        }
        if (StringUtils.isNotBlank(codeType)) {
            labelInfoDTOS = labelInfoDTOS.stream().filter(p -> p.getPhysicalProfileCode().equals(codeType)).collect(Collectors.toList());
        }
        List<CategoryDO> categoryDOS = categoryService.listAll();

        if (categoryDOS == null || categoryDOS.isEmpty()) {
            throw new RuntimeException("目录检索结果为空，请注意查看！");
        }

        Map<Long, String> categroyNameMap = categoryDOS.stream().collect(Collectors.toMap(CategoryDO::getId, CategoryDO::getPropertyName));

        if (categroyNameMap == null || categroyNameMap.isEmpty()) {
            throw new RuntimeException("目录ID或者名称有为空的情况，请注意查看！");
        }

        Map<String, List<DimMetaInfoSummary>> collect = labelInfoDTOS
                .stream()
                .filter(labelInfoDTO -> {
                    return labelInfoDTO.getDataType() == LabelBizDataTypeEnum.ENUM;
                })
                .map(labelInfoDTO -> {
                    DimMetaInfoSummary dimMetaInfoSummary = convertDimMetaInfo(labelInfoDTO);
                    dimMetaInfoSummary.setCategoryName(categroyNameMap.get(dimMetaInfoSummary.getCategroyId()));
                    return dimMetaInfoSummary;
                }).filter(Objects::nonNull)
                .filter(dimMetaInfoSummary -> {
                    return StringUtils.isNotBlank(dimMetaInfoSummary.getCategoryName());
                })
                .collect(Collectors.groupingBy(DimMetaInfoSummary::getCategoryName));
        return collect;
    }

    @Override
    public List<DimMetaInfoSummary> listProfileDimMetaInfoNew(String codeType) {
        List<LabelInfoDTO> labelInfoDTOS = labelInfoService.listOdpsSourceOnUse();
        if (labelInfoDTOS == null || labelInfoDTOS.isEmpty()) {
            throw new RuntimeException("人群标签查询结果为空，请注意查看！");
        }
        if (StringUtils.isNotBlank(codeType)) {
            labelInfoDTOS = labelInfoDTOS.stream().filter(label -> label.getPhysicalProfileCode().equals(codeType)).collect(Collectors.toList());
        }

        return labelInfoDTOS.stream().filter(label -> label.getDataType() == LabelBizDataTypeEnum.ENUM)
                .map(this::convertDimMetaInfo).collect(Collectors.toList());
    }

    @Override
    public List<DimMetaInfoSummary> listMeasureDimMetaInfo(String measuerCodes) {
        if (StringUtils.isBlank(measuerCodes)) {
            throw new RuntimeException("measuerCodes不能为空");
        }
        List<String> strings = Arrays.asList(measuerCodes.split(","));
        List<LabelInfoDTO> labelInfoDTOS = labelInfoService.listOdpsSourceOnUse();
        List<DimMetaInfoSummary> dimOfProfile = labelInfoDTOS
                .stream()
                .filter(labelInfoDTO -> {
                    LabelBizDataTypeEnum dataType = labelInfoDTO.getDataType();
                    return dataType != null && dataType.equals(LabelBizDataTypeEnum.ENUM);
                })
                .map(labelInfoDTO -> {
                    return convertDimMetaInfo(labelInfoDTO);
                }).collect(Collectors.toList());
        com.fliggy.olap.client.domain.Result<List<DimensionDTO>> dimensionDtos = olapQueryService.getCommonDimensions(strings);
        List<DimensionDTO> dimensionDTOList = dimensionDtos.getData();
        List<DimMetaInfoSummary> dimOfMeasure = dimensionDTOList
                .stream()
                .map(dimensionDTO -> {
                    return convertDimMetaInfo(dimensionDTO);
                }).collect(Collectors.toList());
        dimOfProfile.addAll(dimOfMeasure);
        return dimOfProfile;
    }

    @Override
    public List<DimValueInfo> getDimValues(String dimName, Integer dimSourceType) {
        if (StringUtils.isBlank(dimName)) {
            throw new RuntimeException("dim name can not be null");
        }

        switch (dimSourceType) {
            case 0:
                com.fliggy.olap.client.domain.Result<List<DimValueDTO>> dimValues = olapQueryService.getDimValues(dimName);
                if (!dimValues.isSuccess()) {
                    throw new RuntimeException("获取olap相关过滤项失败，" + dimValues.getCode() + "," + dimValues.getCode());
                }
                List<DimValueDTO> data = dimValues.getData();
                List<DimValueInfo> dimValueInfoList = data
                        .stream()
                        .map(dimValueDTO -> {
                            return convertDimValueInfo(dimValueDTO);
                        }).collect(Collectors.toList());
                return dimValueInfoList;
            case 1:
                List<EnumDimDataDTO> enumDimDataDTOS = labelEnumValueService.queryLeafByLabelCode(dimName);
                List<DimValueInfo> collect = enumDimDataDTOS.stream().map(enumDimDataDTO -> {
                    return convertDimValueInfo(enumDimDataDTO);
                }).collect(Collectors.toList());
                return collect;

            default:
                throw new RuntimeException("no such source Type" + dimSourceType);
        }
    }


    private DimMetaInfoSummary convertDimMetaInfo(LabelInfoDTO labelInfoDTO) {
        if (labelInfoDTO == null) {
            return null;
        }

        DimMetaInfoSummary dimMetaInfoSummary = new DimMetaInfoSummary();
        BeanUtils.copyProperties(labelInfoDTO, dimMetaInfoSummary);
        dimMetaInfoSummary.setCode(labelInfoDTO.getCode());
        dimMetaInfoSummary.setName(labelInfoDTO.getName());
        dimMetaInfoSummary.setCategroyId(labelInfoDTO.getCategoryId());
        dimMetaInfoSummary.setDescription(labelInfoDTO.getDescription());
        dimMetaInfoSummary.setDataUpdateTime(labelInfoDTO.getDataUpdateTime());
        if (labelInfoDTO.getCreator() != null) {
            dimMetaInfoSummary.setCreator(JSON.toJSONString(labelInfoDTO.getCreator()));
        }
        dimMetaInfoSummary.setDimSourceType(1);
        return dimMetaInfoSummary;
    }


    private DimMetaInfoSummary convertDimMetaInfo(DimensionDTO dimensionDTO) {
        if (dimensionDTO == null) {
            return null;
        }

        DimMetaInfoSummary dimMetaInfoSummary = new DimMetaInfoSummary();
        BeanUtils.copyProperties(dimensionDTO, dimMetaInfoSummary);
        dimMetaInfoSummary.setCode(dimensionDTO.getDimName());
        dimMetaInfoSummary.setName(dimensionDTO.getDisplayName());
        dimMetaInfoSummary.setDescription(dimensionDTO.getDescription());
        dimMetaInfoSummary.setDimSourceType(0);
        return dimMetaInfoSummary;
    }

    public DimValueInfo convertDimValueInfo(EnumDimDataDTO enumDimDataDTO) {
        DimValueInfo dimValueInfo = new DimValueInfo();
        dimValueInfo.setId(enumDimDataDTO.getEnumId());
        dimValueInfo.setCode(enumDimDataDTO.getEnumCode());
        dimValueInfo.setValue(enumDimDataDTO.getEnumDesc());
        return dimValueInfo;
    }

    public DimValueInfo convertDimValueInfo(DimValueDTO dimValueDTO) {
        DimValueInfo dimValueInfo = new DimValueInfo();
        dimValueInfo.setId(dimValueDTO.getId());
        dimValueInfo.setCode(dimValueDTO.getDimValue());
        dimValueInfo.setValue(dimValueDTO.getDimValue());
        return dimValueInfo;
    }


}
