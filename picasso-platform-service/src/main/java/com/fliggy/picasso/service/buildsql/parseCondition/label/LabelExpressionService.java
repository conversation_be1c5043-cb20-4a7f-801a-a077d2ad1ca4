package com.fliggy.picasso.service.buildsql.parseCondition.label;

import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.entity.condition.GroupConditionDTO;

import java.util.List;
import java.util.Map;

/**
 * 生成标签相关表达式的接口
 */
public interface LabelExpressionService {

    /**
     * 生成标签对应的表达式
     *
     * @param labelCrowdConditions
     * @return
     */
    public Map<String, String> generateLabelExpression(LabelCrowdConditions labelCrowdConditions, List<GroupConditionDTO> groupConditionDTOS);

}
