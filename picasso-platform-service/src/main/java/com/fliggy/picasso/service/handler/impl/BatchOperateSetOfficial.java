package com.fliggy.picasso.service.handler.impl;

import com.fliggy.picasso.common.constants.CrowdLabelBatchOperateActionEnum;
import com.fliggy.picasso.common.domain.label.CrowdLabelBatchOperateDO;
import com.fliggy.picasso.common.domain.label.LabelExtInfo;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.service.handler.BatchOperateHandler;
import com.fliggy.picasso.service.handler.BatchOperateHandlerResolver;
import com.fliggy.picasso.service.label.LabelInfoService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class BatchOperateSetOfficial implements BatchOperateHandler, InitializingBean {

    private final BatchOperateHandlerResolver batchOperateHandlerResolve;
    private final LabelInfoService labelInfoService;

    @Autowired
    public BatchOperateSetOfficial(BatchOperateHandlerResolver resolve, LabelInfoService labelInfoService) {
        this.batchOperateHandlerResolve = resolve;
        this.labelInfoService = labelInfoService;
    }

    @Override
    public boolean process(CrowdLabelBatchOperateDO crowdLabelBatchOperateDO) {
        List<Long> ids = crowdLabelBatchOperateDO.getLabelIdList();

        CrowdLabelBatchOperateActionEnum action = crowdLabelBatchOperateDO.getBatchOperateAction();

        switch (action) {
            case BATCH_SET_OFFICIAL:
                for (Long id : ids) {
                    LabelInfoDTO labelInfoDTO = labelInfoService.find(id);
                    LabelExtInfo extInfo = labelInfoDTO.getExtInfo();
                    List<String> tags = extInfo.getTags() == null ? new ArrayList<>() : extInfo.getTags();
                    if (tags.contains("official")) {
                        continue;
                    }
                    tags.add("official");
                    extInfo.setTags(tags);
                    labelInfoDTO.setExtInfo(extInfo);
                    labelInfoService.updateSelectiveById(id, labelInfoDTO);
                }
                return true;
            case BATCH_REMOVE_OFFICIAL:
                for (Long id : ids) {
                    LabelInfoDTO labelInfoDTO = labelInfoService.find(id);
                    LabelExtInfo extInfo = labelInfoDTO.getExtInfo();
                    List<String> tags = extInfo.getTags();
                    if (tags == null) {
                        continue;
                    }
                    tags.remove("official");
                    extInfo.setTags(tags);
                    labelInfoDTO.setExtInfo(extInfo);
                    labelInfoService.updateSelectiveById(id, labelInfoDTO);
                }
                return true;
            default:
                throw new ParamErrorException(String.format("批量操作加精接口传入action非法：action: %s", action.name()));
        }
    }

    @Override
    public void afterPropertiesSet() {
        batchOperateHandlerResolve.register(CrowdLabelBatchOperateActionEnum.BATCH_SET_OFFICIAL, this);
        batchOperateHandlerResolve.register(CrowdLabelBatchOperateActionEnum.BATCH_REMOVE_OFFICIAL, this);
    }
}
