package com.fliggy.picasso.service.buildsql.parseCondition;

import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.entity.condition.GroupConditionDTO;
import com.fliggy.picasso.entity.condition.TableConditionDTO;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 诸葛圈选条件字段和标签映射接口实现类
 */
@Component
public class LabelConditionMappingServiceImpl implements LabelConditionMappingService {
    @Resource
    LabelInfoService labelInfoService;

    @Resource
    PhysicalProfileInfoService physicalProfileInfoService;

    @Override
    public List<GroupConditionDTO> getLabelAndPhysicalProfileInfo(LabelCrowdConditions labelCrowdConditions) {
        List<GroupConditionDTO> result = new ArrayList<>();
        if (labelCrowdConditions == null || CollectionUtils.isEmpty(labelCrowdConditions.getGroup())) {
            throw new ParamErrorException("生成GroupCondition异常：labelCrowdConditions或group为空！");
        }

        List<LabelCrowdConditions.LabelGroup> group = labelCrowdConditions.getGroup();
        for (int i = 0; i < group.size(); i++) {
            LabelCrowdConditions.LabelGroup labelGroup = group.get(i);
            List<LabelCrowdConditions.LabelValue> label = labelGroup.getLabel();
            List<String> collect = label.stream()
                                        .map(LabelCrowdConditions.LabelValue::getName)
                                        .collect(Collectors.toList());

            List<LabelInfoDTO> labelInfoDTOList = labelInfoService.findByCodes(collect);
            if (CollectionUtils.isEmpty(labelInfoDTOList)) {
                throw new ParamErrorException("生成GroupCondition异常：圈选条件中的标签都不存在！");
            }

            //获取标签对应的Map<标签名，olap表名>
            Map<String, List<PhysicalProfileInfoDTO>> stringListMap = physicalProfileInfoService.queryRefluxLabelsAndProfileInfo(labelInfoDTOList);
            GroupConditionDTO groupConditionDTO = buildTableCondition(stringListMap, i);
            result.add(groupConditionDTO);
        }
        return result;
    }

    @Override
    public Boolean judgeUseValidTable(List<GroupConditionDTO> groupConditionDTOS) {
        for (GroupConditionDTO groupConditionDTO : groupConditionDTOS) {
            List<TableConditionDTO> tableConditions = groupConditionDTO.getTableConditions();
            if (tableConditions.size() > 1) {
                return false;
            }
        }
        return true;
    }

    private GroupConditionDTO buildTableCondition(Map<String, List<PhysicalProfileInfoDTO>> stringListMap, Integer groupIndex) {
        GroupConditionDTO labelConditionDTO = new GroupConditionDTO();
        Set<String> keys = stringListMap.keySet();
        Map<String, TableConditionDTO> tableConditionDTOMap = new HashMap<>(20);
        Map<String, TableConditionDTO> labelTableCondition = new HashMap<>(20);
        Integer tableIndex = 0;
        for (String key : keys) {
            List<PhysicalProfileInfoDTO> physicalProfileInfoDTOS = stringListMap.get(key);
            if (CollectionUtils.isEmpty(physicalProfileInfoDTOS)) {
                throw new ParamErrorException(String.format("生成GroupCondition异常：%s没有对应的物理表！", key));
            }
            PhysicalProfileInfoDTO physicalProfileInfoDTO = physicalProfileInfoDTOS.get(0);
            String olapTable = physicalProfileInfoDTO.getOlapTable();
            String primaryKey = physicalProfileInfoDTO.getPrimaryKey();
            String partition;
            Date olapTableUpdateTime = new Date();
            boolean useMaxPt = false;
            if (physicalProfileInfoDTO.getType().equals(PhysicalProfileInfoDTO.PhysicalProfileType.OFFLINE)) {
                partition = getPreDate(olapTableUpdateTime);
                useMaxPt = true;
            } else {
                partition = getCurrDate(olapTableUpdateTime);
            }

            TableConditionDTO tableConditionDTO;
            if (tableConditionDTOMap.containsKey(olapTable)) {
                tableConditionDTO = tableConditionDTOMap.get(olapTable);
            } else {
                tableConditionDTO = new TableConditionDTO();
                tableConditionDTO.setTableName(olapTable);
                tableConditionDTO.setLastPartition(partition);
                tableConditionDTO.setUseMaxPt(useMaxPt);
                tableConditionDTO.setPrimaryKey(primaryKey);
                String alias = String.format("group%dtable%d", groupIndex, tableIndex);
                tableConditionDTO.setAlias(alias);
                tableConditionDTOMap.put(olapTable, tableConditionDTO);
            }
            labelTableCondition.put(key + groupIndex, tableConditionDTO);
            tableIndex++;
        }
        labelConditionDTO.setLabelConditionMap(labelTableCondition);
        labelConditionDTO.setTableConditions(new ArrayList<>(tableConditionDTOMap.values()));
        return labelConditionDTO;
    }

    /**
     * 获取前一天日期
     *
     * @param currDate
     * @return
     */
    public String getPreDate(Date currDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currDate);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date date = calendar.getTime();
        return sdf.format(date);
    }

    public String getCurrDate(Date currDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currDate);
        Date date = calendar.getTime();
        return sdf.format(date);
    }
}
