package com.fliggy.picasso.service.statistic;

import com.fliggy.picasso.common.enums.statistic.StatisticEntityTypeEnum;
import com.fliggy.picasso.common.enums.statistic.StatisticTimeDimEnum;
import com.fliggy.picasso.common.statistic.OnlineStatisticInfoVO;

import java.util.List;
import java.util.Map;

public interface OnlineStatisticInfoService {

    /**
     * 插入一条统计信息
     * @param onlineStatisticInfoVO
     * @return
     */
    int insert(OnlineStatisticInfoVO onlineStatisticInfoVO);

    /**
     * 批量插入统计信息
     * @param onlineStatisticInfoVOList
     * @return
     */
    int batchInsert(List<OnlineStatisticInfoVO> onlineStatisticInfoVOList);

    /**
     * 更新一条统计信息
     * @param onlineStatisticInfoVO
     * @return
     */
    int update(OnlineStatisticInfoVO onlineStatisticInfoVO);

    /**
     * 批量更新统计信息
     * @param onlineStatisticInfoVOList
     * @return
     */
    int batchUpdate(List<OnlineStatisticInfoVO> onlineStatisticInfoVOList);

    /**
     * 根据entityId和entityType查询统计信息
     * @param entityId
     * @param entityType
     * @return
     */
    List<OnlineStatisticInfoVO> queryByEntityIdAndType(Long entityId, StatisticEntityTypeEnum entityType);

    /**
     * 查询指定entityId和entityType的指定统计维度的统计值
     * @param entityId
     * @param entityType
     * @param statisticDim
     * @return
     */
    Long queryStatisticValByEntityAndDim(Long entityId, StatisticEntityTypeEnum entityType, StatisticTimeDimEnum statisticDim);

    Map<Long, Long> batchQueryStatisticValByEntityAndDim(List<Long> entityIdList, StatisticEntityTypeEnum entityType, StatisticTimeDimEnum statisticDim);

    /**
     * 查询上一次统计的数据
     * @return
     */
    List<OnlineStatisticInfoVO> findAllLastDateStatisticInfo();

    /**
     * 查询所有有效的大人群/池子
     * @param type
     * @return
     */
    List<OnlineStatisticInfoVO> findAllValidHeavyStatisticInfo(StatisticEntityTypeEnum type);

    Boolean delete(Long id);

    Boolean batchDelete(List<Long> ids);
}
