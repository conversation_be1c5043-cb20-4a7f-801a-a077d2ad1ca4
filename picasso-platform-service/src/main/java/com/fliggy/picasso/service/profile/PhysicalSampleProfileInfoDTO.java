package com.fliggy.picasso.service.profile;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import com.fliggy.picasso.entity.odps.OdpsMasterConfig;
import com.google.common.base.Objects;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

/**
 * 采样表的信息数据
 * 将odpsTable修改为采样表，同时新增originOdpsTable原始表字段
 * <AUTHOR>
 */
public class PhysicalSampleProfileInfoDTO extends PhysicalProfileInfoDTO {
    /**
     * 原始ODPS表名
     */
    @Getter
    @Setter
    private String originOdpsTable;

    /**
     * 转化为采样表的数据
     * @param infoDTO 原始表数据
     * @return PhysicalSampleProfileInfoDTO
     */
    public static PhysicalSampleProfileInfoDTO convert2SampleDTO(PhysicalProfileInfoDTO infoDTO) {
        PhysicalSampleProfileInfoDTO sampleDTO = new PhysicalSampleProfileInfoDTO();
        try {
            BeanUtils.copyProperties(infoDTO, sampleDTO);
            // 设置原始表
            sampleDTO.setOriginOdpsTable(sampleDTO.getOdpsTable());
            // 修改为采样表
            sampleDTO.setOdpsTable(sampleDTO.getOdpsTable() + "_sample");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sampleDTO;
    }
}