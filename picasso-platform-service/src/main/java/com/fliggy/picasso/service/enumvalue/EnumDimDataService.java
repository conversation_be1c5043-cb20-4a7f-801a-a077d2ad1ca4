package com.fliggy.picasso.service.enumvalue;

import com.fliggy.picasso.common.domain.label.enumvalue.EnumDimDataDO;
import com.fliggy.picasso.common.domain.label.enumvalue.LabelEnumValueAggregateByMetaId;
import com.fliggy.picasso.common.domain.label.enumvalue.LabelEnumValueFlat;

import java.util.List;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
public interface EnumDimDataService {

    /**
     * 根据参数统计总数
     *
     * @param param
     */
    long count(EnumDimDataParameter param);

    /**
     * @param dimMetaId
     * @param enumCode
     * @return
     */
    EnumDimDataDTO findByEnumCode(Long dimMetaId, String enumCode);

    /**
     * 根据参数查询
     *
     * @param param
     */
    EnumDimDataDTO find(EnumDimDataParameter param);

    /**
     * 列表查询
     *
     * @param param
     */
    List<EnumDimDataDTO> list(EnumDimDataParameter param);

    /**
     * @param dimMetaId
     * @return
     */
    List<EnumDimDataDTO> listByDimMetaId(Long dimMetaId);

    /**
     * 选择性修改
     *
     * @param dto
     * @param param
     */
    void updateSelective(EnumDimDataDTO dto, EnumDimDataParameter param);

    /**
     * 根据ID查询
     *
     * @param id 枚举值ID
     * @return
     */
    EnumDimDataDO queryById(Long id);

    /**
     * 根据索引查询（index:dimMetaId_enumId）
     *
     * @param dimMetaId
     * @param enumId
     * @return
     */
    EnumDimDataDO queryByIndex(Long dimMetaId, Long enumId);


    /**
     * 查询dimMetaId下的最大enumId
     *
     * @param dimMetaId 枚举对象ID
     * @return 最大的enumId
     */
    Long queryMaxEnumId(Long dimMetaId);

    /**
     * 查询所有枚举对象的数据业务日期，其中业务日期取该枚举对象的所有枚举值记录中的最小值。
     *
     * @return 枚举对象id -> 枚举对象的数据业务日期最小值
     */
    List<LabelEnumValueAggregateByMetaId> queryEnumDataBizDs();

    /**
     * 查询当前节点的直接子节点（未逻辑删除）
     * @param dimMetaId
     * @param enumId
     * @return
     */
    List<EnumDimDataDO> queryChild(Long dimMetaId, Long enumId);

    List<EnumDimDataDO> queryAllByDimMetaId(Long id);

    List<LabelEnumValueFlat> queryAllFlat(Long dimMetaId);

    Boolean update(EnumDimDataDO enumDimDataDO);

    /**
     * 根据主键选择性更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(EnumDimDataDO record);

    Long insert(EnumDimDataDO enumDimDataDO);

    /**
     * 查询预览数据，限制3条
     * @param dimMetaId
     * @return
     */
    List<EnumDimDataDTO> queryPreviewData(Long dimMetaId);

}