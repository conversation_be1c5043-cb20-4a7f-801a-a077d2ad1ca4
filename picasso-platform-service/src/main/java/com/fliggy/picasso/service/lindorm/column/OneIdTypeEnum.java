package com.fliggy.picasso.service.lindorm.column;

import lombok.Getter;

/**
 * one id type
 * <AUTHOR>
 */
@Getter
public enum OneIdTypeEnum {

    /**
     * scrm one id
     */
    SCRM("scrm", "scrm_one_id_reverse", "scrm one id"),
    DEVICE("device", "device_one_id_reverse", "device one id"),
    ;

    private String code;
    private String column;
    private String desc;

    OneIdTypeEnum(String code, String column, String desc) {
        this.code = code;
        this.column = column;
        this.desc = desc;
    }
}
