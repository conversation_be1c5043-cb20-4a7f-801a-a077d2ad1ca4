package com.fliggy.picasso.service.buildsql.parseCondition.label;

import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import com.fliggy.picasso.common.enums.label.LabelOperatorEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.*;

/**
 * 数值类型标签表达式生成器
 */
@Component
public class NumberLabelExpressionGenerator extends BaseLabelExpressionGenerator {

    public NumberLabelExpressionGenerator(ExpressionGeneratorResolver expressionGeneratorResolver) {
        super(expressionGeneratorResolver);
    }

    @Override
    public String buildExpression(String labelName, List<String> enumValues, LabelOperatorEnum operatorType) {
        StringBuilder result = new StringBuilder();
        result.append(CAST).append(LEFT_PAREN).append(labelName).append(AS).append(INT8).append(RIGHT_PAREN);
        if (enumValues.size() != 2) {
            throw new ParamErrorException("解析标签圈人逻辑表达式异常，数值型标签筛选参数个数必须等于2。");
        }

        long min = Long.parseLong(enumValues.get(0));
        long max = Long.parseLong(enumValues.get(1));
        if (operatorType != LabelOperatorEnum.NOTIN) {
            result.append(BETWEEN).append(min).append(AND).append(max);
        } else {
            result.append(LESS_THAN).append(min);
            result.append(OR);
            result.append(labelName).append(GREATER_THAN).append(max);
            result.append(OR).append(labelName).append(" is null");
        }
        result.append(RIGHT_PAREN);
        return result.toString();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        expressionGeneratorResolver.register(LabelBizDataTypeEnum.NUMBER, this);
    }
}
