package com.fliggy.picasso.service.analysis.impl;

import java.util.List;
import java.util.Objects;
import java.util.Set;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.odps.OdpsException;
import com.fliggy.picasso.analysis.AnalysisConstants;
import com.fliggy.picasso.analysis.AnalysisV2Meta;
import com.fliggy.picasso.analysis.AnalysisV2Meta.TagInfo;
import com.fliggy.picasso.analysis.AnalysisV2StatusEnum;
import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.utils.NumberUtils;
import com.fliggy.picasso.dao.AnalysisV2DO;
import com.fliggy.picasso.domain.AnalysisTemplateDO;
import com.fliggy.picasso.entity.template.OdpsAnalysisBatchParam;
import com.fliggy.picasso.exception.AnalysisException;
import com.fliggy.picasso.freemarker.TemplateEnum;
import com.fliggy.picasso.freemarker.TemplateReader;
import com.fliggy.picasso.mapper.picasso.AnalysisTemplateMapper;
import com.fliggy.picasso.offline.OdpsService;
import com.fliggy.picasso.query.AnalysisTemplateQuery;
import com.fliggy.picasso.service.analysis.AnalysisTemplateService;
import com.fliggy.picasso.service.analysis.AnalysisV2Service;
import com.fliggy.picasso.service.analysis.AnalysisV2ServiceImpl;
import com.fliggy.picasso.service.analysis.domain.AnalysisTemplateAnalysisRequest;
import com.fliggy.picasso.service.analysis.domain.AnalysisTemplateSingleConfigBO;
import com.fliggy.picasso.service.analysis.domain.AnalysisTemplateVO;
import com.fliggy.picasso.service.permission.PermissionService;
import com.fliggy.picasso.utils.AnalysisUtils;
import com.fliggy.picasso.utils.PageInfoUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.ateye.annotation.Switch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fliggy.picasso.offline.OdpsService.LOW_PRIORITY;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AnalysisTemplateServiceImpl implements AnalysisTemplateService {

    @Switch(description = "分析模板允许包含维度最大数量")
    public Integer maxDimSize = 20;

    private final AnalysisTemplateMapper analysisTemplateMapper;
    private final AnalysisV2Service analysisV2Service;
    private final TemplateReader templateReader;
    private final OdpsService odpsService;
    private final PermissionService permissionService;

    @Autowired
    public AnalysisTemplateServiceImpl(AnalysisTemplateMapper analysisTemplateMapper
        , AnalysisV2Service analysisV2Service
        , TemplateReader templateReader
        , OdpsService odpsService
        , PermissionService permissionService
    ) {
        this.analysisTemplateMapper = analysisTemplateMapper;
        this.analysisV2Service = analysisV2Service;
        this.templateReader = templateReader;
        this.odpsService = odpsService;
        this.permissionService = permissionService;
    }

    @Override
    public Long insert(AnalysisTemplateDO record) {
        if (Objects.isNull(record)) {
            throw new IllegalArgumentException("参数非法：record is null");
        }

        List<AnalysisTemplateSingleConfigBO> configBOS = JSON.parseArray(record.getConfig(), AnalysisTemplateSingleConfigBO.class);
        if (CollectionUtils.isEmpty(configBOS)) {
            throw new IllegalArgumentException("参数非法：config is empty");
        }

        Set<String> dimSet = Sets.newHashSet();
        for (AnalysisTemplateSingleConfigBO configBO : configBOS) {
            if (CollectionUtils.isEmpty(configBO.getTagInfoList())) {
                throw new IllegalArgumentException("参数非法：子分析维度为空");
            }
            for (TagInfo tagInfo : configBO.getTagInfoList()) {
                if (StringUtils.isNotBlank(tagInfo.getName())) {
                    dimSet.add(tagInfo.getName());
                }
            }
        }
        if (dimSet.size() > maxDimSize) {
            throw new IllegalArgumentException("参数非法：分析维度不允许超过" + maxDimSize + "个");
        }

        if (Objects.isNull(record.getPublicType())) {
            throw new IllegalArgumentException("参数非法：publicType属性为空");
        }

        int num = analysisTemplateMapper.insert(record);
        if (num > 0) {
            return record.getId();
        } else {
            return null;
        }
    }

    @Override
    public int deleteById(Long id) {
        if (!NumberUtils.validLong(id)) {
            return 0;
        }

        return analysisTemplateMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int updateById(AnalysisTemplateDO record) {
        if (Objects.isNull(record) || !NumberUtils.validLong(record.getId())) {
            return 0;
        }

        return analysisTemplateMapper.updateByPrimaryKey(record);
    }

    @Override
    public AnalysisTemplateDO queryById(Long id) {
        if (!NumberUtils.validLong(id)) {
            return null;
        }

        return analysisTemplateMapper.selectByPrimaryKey(id);
    }

    @Override
    public PageInfo<AnalysisTemplateVO> pageQuery(AnalysisTemplateQuery query) {
        if (Objects.isNull(query)) {
            return new PageInfo<>(Lists.newArrayList());
        }

        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<AnalysisTemplateDO> listResult = analysisTemplateMapper.select(query);
        return PageInfoUtils.getPageInfo(listResult, this::convertToVO);
    }

    @Override
    public int analysis(AnalysisTemplateAnalysisRequest request) throws OdpsException {
        if (Objects.isNull(request) || request.invalid()) {
            throw new AnalysisException("模板分析异常-参数非法, request=" + request, null);
        }

        AnalysisTemplateVO vo = queryVOById(request.getTemplateId());
        if (Objects.isNull(vo) || CollectionUtils.isEmpty(vo.getConfig())) {
            throw new AnalysisException("模板分析异常-模板不存在, templateId=" + request.getTemplateId(), null);
        }

        // 批量创建分析
        AnalysisV2DO rootDO = analysisV2Service.findById(request.getParentId());
        List<AnalysisV2DO> analysisV2DOS = Lists.newArrayList();
        for (AnalysisTemplateSingleConfigBO configBO : vo.getConfig()) {
            AnalysisV2Meta meta = new AnalysisV2Meta();
            BeanUtils.copyProperties(configBO, meta);
            AnalysisV2DO analysisV2DO = AnalysisV2ServiceImpl.initNormalRecord(rootDO, meta);
            if (Objects.nonNull(analysisV2DO)) {
                analysisV2DOS.add(analysisV2DO);
            }
        }
        if (CollectionUtils.isEmpty(analysisV2DOS)) {
            throw new AnalysisException("模板分析异常-待分析任务为空", null);
        }

        JSONObject odpsJson = execAnalysisSql(vo, rootDO.getCrowdId());
        int num = batchInsertAnalysis(analysisV2DOS, odpsJson, AnalysisV2StatusEnum.RUNNING, AnalysisV2StatusEnum.INIT);
        if (num <= 0) {
            throw new AnalysisException("模板分析异常-更新节点状态失败", null);
        }

        return num;
    }

    /**
     * 批量更新分析节点
     *
     * @param records 待更新节点
     * @param odpsJson odps执行信息
     * @param toStatus 更新状态
     * @param fromStatus 原状态
     * @return 影响行数
     */
    private int batchInsertAnalysis(List<AnalysisV2DO> records, JSONObject odpsJson, AnalysisV2StatusEnum toStatus, AnalysisV2StatusEnum fromStatus) {
        if (CollectionUtils.isEmpty(records) || Objects.isNull(toStatus) || Objects.isNull(fromStatus) || Objects.isNull(odpsJson)) {
            return 0;
        }

        int cnt = 0;
        for (AnalysisV2DO record : records) {
            record.setStatus(toStatus.getStatus());
            record.setExtInfo(odpsJson.toJSONString());

            // 注意第二个参数是用来校验当前状态的
            Long id = analysisV2Service.insert(record);
            if (NumberUtils.validLong(id)) {
                cnt++;
            }
        }

        return cnt;
    }

    /**
     * 查询VO
     *
     * @param id id
     * @return vo
     */
    private AnalysisTemplateVO queryVOById(Long id) {
        AnalysisTemplateDO d = queryById(id);
        return convertToVO(d);
    }

    /**
     * do 转 vo
     *
     * @param d
     * @return
     */
    private AnalysisTemplateVO convertToVO(AnalysisTemplateDO d) {
        if (Objects.isNull(d)) {
            return null;
        }

        AnalysisTemplateVO v = new AnalysisTemplateVO();
        BeanUtils.copyProperties(d, v);
        v.setCreator(JSON.parseObject(d.getCreator(), Employee.class));
        v.setConfig(JSON.parseArray(d.getConfig(), AnalysisTemplateSingleConfigBO.class));

        return v;
    }

    /**
     * 执行分析sql
     *
     * @param templateVO 模板
     * @param crowdId 人群id
     * @return 执行结果信息
     * @throws OdpsException odps异常
     */
    public JSONObject execAnalysisSql(AnalysisTemplateVO templateVO, Long crowdId) throws OdpsException {
        String sql = generateBatchSql(templateVO, crowdId);
        if (StringUtils.isBlank(sql)) {
            throw new AnalysisException("生成分析sql失败", null);
        }

        String odpsTaskName = AnalysisUtils.getOdpsTaskName(crowdId);
        Pair<String, String> executePair = odpsService.asyncExecute(sql, odpsTaskName, LOW_PRIORITY);

        // 记录执行结果，更新状态
        JSONObject execInfo = new JSONObject();
        execInfo.put(OdpsConstants.ODPS_INSTANT_ID, executePair.getLeft());
        execInfo.put(OdpsConstants.ODPS_LOG_VIEW, executePair.getRight());
        execInfo.put(OdpsConstants.ODPS_SQL, sql);
        execInfo.put(OdpsConstants.ODPS_INSTANT_TASK_NAME, odpsTaskName);
        execInfo.put(AnalysisConstants.KEY_IS_BATCH_ANALYSIS, true);

        return execInfo;
    }

    /**
     * 生成批量分析sql
     *
     * @param templateVO 模板
     * @param crowdId 人群id
     * @return sql
     */
    private String generateBatchSql(AnalysisTemplateVO templateVO, Long crowdId) {
        if (Objects.isNull(templateVO) || CollectionUtils.isEmpty(templateVO.getConfig()) || !NumberUtils.validLong(crowdId)) {
            return null;
        }

        // 初始化grouping列表: tagList代表所有需要聚合的标签; groupList代表所有聚合分组, 支持多个tag
        Set<String> tagList = Sets.newHashSet();
        List<String> groupList = Lists.newArrayList();
        String limit = "100";
        String sc = "DESC";
        for (AnalysisTemplateSingleConfigBO configBO : templateVO.getConfig()) {
            if (CollectionUtils.isEmpty(configBO.getTagInfoList())) {
                continue;
            }

            List<String> tmpTagList = Lists.newArrayList();
            for (TagInfo tagInfo : configBO.getTagInfoList()) {
                if (StringUtils.isNotBlank(tagInfo.getName())) {
                    tagList.add(tagInfo.getName());
                    tmpTagList.add(tagInfo.getName());
                }
            }

            if (CollectionUtils.isNotEmpty(tmpTagList)) {
                groupList.add(String.join(",", tmpTagList));
            }

            if (NumberUtils.validInteger(configBO.getLimitCount()) && StringUtils.isNotBlank(configBO.getOrderBy())) {
                limit = String.valueOf(configBO.getLimitCount());
                sc = configBO.getOrderBy();
            }
        }

        OdpsAnalysisBatchParam odpsAnalysisBatchParam = OdpsAnalysisBatchParam.builder()
            .tagList(Lists.newArrayList(tagList))
            .groupList(groupList)
            .limitCount(limit)
            .sc(sc)
            .crowdId(String.valueOf(crowdId))
            .build();

        return templateReader.read(TemplateEnum.ODPS_ANALYSIS_BATCH, odpsAnalysisBatchParam);
    }
}
