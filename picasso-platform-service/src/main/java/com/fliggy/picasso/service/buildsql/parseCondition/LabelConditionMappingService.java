package com.fliggy.picasso.service.buildsql.parseCondition;

import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.entity.condition.GroupConditionDTO;

import java.util.List;

/**
 * 诸葛圈选条件字段和标签映射接口
 */
public interface LabelConditionMappingService {

    /**
     * 获取圈选条件中所有标签字段对应的物理画像表相关信息
     *
     * @param labelCrowdConditions
     * @return
     */
    List<GroupConditionDTO> getLabelAndPhysicalProfileInfo(LabelCrowdConditions labelCrowdConditions);

    /**
     * 判断圈选条件是否只用了固定一张表
     *
     * @param groupConditionDTOS
     * @return
     */
    Boolean judgeUseValidTable(List<GroupConditionDTO> groupConditionDTOS);
}
