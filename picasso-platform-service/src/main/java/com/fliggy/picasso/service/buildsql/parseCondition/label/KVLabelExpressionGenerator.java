package com.fliggy.picasso.service.buildsql.parseCondition.label;

import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import com.fliggy.picasso.common.enums.label.LabelOperatorEnum;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.fliggy.picasso.common.Constant.*;
import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.DOT;
import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.*;

/**
 * kv类型标签表达式生成类
 */
@Component
public class KVLabelExpressionGenerator extends BaseLabelExpressionGenerator {
    public KVLabelExpressionGenerator(ExpressionGeneratorResolver expressionGeneratorResolver) {
        super(expressionGeneratorResolver);
    }

    @Override
    String buildExpression(String labelName, List<String> enumValues, LabelOperatorEnum operatorType) {
        String expression;
        if (operatorType != LabelOperatorEnum.NOTIN) {
            expression = inKVCase(labelName, enumValues, operatorType);
        } else {
            expression = notInKVCase(labelName, enumValues, operatorType);
        }
        return expression;
    }

    private String inKVCase(String labelName, List<String> enumValues, LabelOperatorEnum operatorType) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < enumValues.size(); i++) {
            String value = enumValues.get(i).split(BLANK)[0];
            Double scoreMin = Double.parseDouble(enumValues.get(i).split(BLANK)[2]);
            Double scoreMax = Double.parseDouble(enumValues.get(i).split(BLANK)[3]);

            result.append(CAST).append(LEFT_PAREN).append(GET_JSON_OBJECT).append(LEFT_PAREN).append(labelName).append(COMMA).append(SINGLE_QUOTE)
                    .append(DOLLAR)
                    .append(DOT)
                    .append(value)
                    .append(SINGLE_QUOTE)
                    .append(RIGHT_PAREN).append(AS).append("DOUBLE precision").append(RIGHT_PAREN).append(BETWEEN)
                    .append(scoreMin)
                    .append(AND)
                    .append(scoreMax);

            if (i < enumValues.size() - 1) {
                result.append(OR);
            }
        }
        result.append(RIGHT_PAREN);
        return result.toString();
    }

    private String notInKVCase(String labelName, List<String> enumValues, LabelOperatorEnum operatorType) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < enumValues.size(); i++) {
            String value = enumValues.get(i).split(BLANK)[0];
            Double scoreMin = Double.parseDouble(enumValues.get(i).split(BLANK)[2]);
            Double scoreMax = Double.parseDouble(enumValues.get(i).split(BLANK)[3]);

            result.append(LEFT_PAREN).append(CAST).append(LEFT_PAREN).append(GET_JSON_OBJECT)
                    .append(LEFT_PAREN)
                    .append(labelName)
                    .append(COMMA)
                    .append(SINGLE_QUOTE)
                    .append(DOLLAR)
                    .append(DOT)
                    .append(value)
                    .append(SINGLE_QUOTE).append(RIGHT_PAREN).append(AS).append("DOUBLE precision").append(RIGHT_PAREN)
                    .append(LESS_THAN)
                    .append(scoreMin)
                    .append(OR)
                    .append(CAST).append(LEFT_PAREN).append(GET_JSON_OBJECT)
                    .append(LEFT_PAREN)
                    .append(labelName)
                    .append(COMMA)
                    .append(SINGLE_QUOTE)
                    .append(DOLLAR)
                    .append(DOT)
                    .append(value)
                    .append(SINGLE_QUOTE).append(RIGHT_PAREN).append(AS).append("DOUBLE precision").append(RIGHT_PAREN)
                    .append(GREATER_THAN)
                    .append(scoreMax)
                    .append(OR).append(labelName).append(" is null")
                    .append(RIGHT_PAREN);

            if (i < enumValues.size() - 1) {
                result.append(AND);
            }
        }
        result.append(RIGHT_PAREN);
        return result.toString();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        expressionGeneratorResolver.register(LabelBizDataTypeEnum.KV, this);
    }
}
