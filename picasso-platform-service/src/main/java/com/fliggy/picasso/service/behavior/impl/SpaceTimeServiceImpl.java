package com.fliggy.picasso.service.behavior.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions.LabelParams;
import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import com.fliggy.picasso.common.enums.spacetime.SpaceTimeBehaviorType;
import com.fliggy.picasso.common.enums.spacetime.SpaceTimeConditionType;
import com.fliggy.picasso.common.enums.spacetime.SpaceTimeLabelTimeType;
import com.fliggy.picasso.service.behavior.SpaceTimeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import org.springframework.stereotype.Service;

@Service
public class SpaceTimeServiceImpl implements SpaceTimeService {

    @Switch(description = "时空标签默认枚举423，true/false")
    public Long defaultSpaceTimeEnumId = 423L;
    @Switch(description = "时空标签城市code枚举")
    public Long spaceTimeCityCodeEnumId = 572L;
    @Switch(description = "时空标签酒店星级枚举")
    public Long spaceTimeHotelStarEnumId = 74L;
    @Switch(description = "时空标签景点等级枚举")
    public Long spaceTimeEntranceLevelEnumId = 573L;

    @AteyeInvoker(description = "时空标签-查询时间类型")
    @Override
    public Map<String, String> queryTimeType() {
        SpaceTimeLabelTimeType[] values = SpaceTimeLabelTimeType.values();
        if (values.length == 0) {
            return Maps.newHashMap();
        }

        Map<String, String> result = Maps.newHashMap();
        for (SpaceTimeLabelTimeType value : values) {
            result.put(value.getCode(), value.getDesc());
        }
        return result;
    }

    @AteyeInvoker(description = "时空标签-查询行为类型", paraDesc = "typeList")
    @Override
    public Map<String, String> queryBehaviorType(Boolean filterNoDest) {
        SpaceTimeBehaviorType[] values = SpaceTimeBehaviorType.values();
        if (values.length == 0) {
            return Maps.newHashMap();
        }

        List<SpaceTimeBehaviorType> typeList = null;
        if (filterNoDest) {
            typeList = new ArrayList<>(Arrays.asList(values)).stream()
                    .filter(value -> !Objects.equals(value, SpaceTimeBehaviorType.VISA))
                    .collect(Collectors.toList());
        } else {
            typeList = new ArrayList<>(Arrays.asList(values));
        }

        Map<String, String> result = Maps.newHashMap();
        for (SpaceTimeBehaviorType value : typeList) {
            result.put(value.getCode(), value.getDesc());
        }
        return result;
    }

    @Override
    public Map<String, String> queryBehaviorTypeWithTimeType(Boolean filterNoDest, SpaceTimeLabelTimeType timeType) {
        SpaceTimeBehaviorType[] values = SpaceTimeBehaviorType.values();
        if (values.length == 0) {
            return Maps.newHashMap();
        }

        List<SpaceTimeBehaviorType> typeList = null;
        if (filterNoDest || (Objects.nonNull(timeType) && !Objects.equals(timeType, SpaceTimeLabelTimeType.BUY_TIME))) {
            // 签证没有目的地，并且只有下单时间
            typeList = new ArrayList<>(Arrays.asList(values)).stream()
                    .filter(value -> !Objects.equals(value, SpaceTimeBehaviorType.VISA))
                    .collect(Collectors.toList());
        } else {
            typeList = new ArrayList<>(Arrays.asList(values));
        }

        Map<String, String> result = Maps.newHashMap();
        for (SpaceTimeBehaviorType value : typeList) {
            result.put(value.getCode(), value.getDesc());
        }
        return result;
    }

    @AteyeInvoker(description = "时空标签-查询附加条件")
    @Override
    public List<LabelCrowdConditions.LabelParams> queryConditionType(String behaviorType) {
        SpaceTimeBehaviorType behaviorTypeEnum = SpaceTimeBehaviorType.find(behaviorType);
        if (Objects.isNull(behaviorTypeEnum)) {
            return Lists.newArrayList();
        }

        switch (behaviorTypeEnum) {
            case CIVIL_FLIGHT:
            case INTER_FLIGHT:
            case AIRPORT_TRANSFER:
            case TRAIN_TRANSFER:
            case TRAIN:
            case BUS:
                return getConditionTypeForTraffic(behaviorTypeEnum);
            case HOTEL:
                return getConditionTypeForHotel();
            case ENTRANCE:
            case TICKET:
                return getConditionTypeForTicket(behaviorTypeEnum);
            case RENT_CAR:
                return getConditionTypeForRentCar();
            case DOMESTIC_FUN:
            case INTER_FUN:
            case GROUP_TOUR:
            case INDEPENDENT_TRAVEL:
            case TRIP_CUSTOM:
                return getConditionTypeForHoliday();
            default:
                return Lists.newArrayList();
        }
    }

    private List<LabelParams> getConditionTypeForRentCar() {
        List<LabelCrowdConditions.LabelParams> paramsList = Lists.newArrayList();
        paramsList.add(generateLabelParams(SpaceTimeConditionType.ARR_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, spaceTimeCityCodeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_LIVE_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_BIRTH_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_VALID, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.SAME_DEST_BEHAVIOR, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, true));
        return paramsList;
    }

    private boolean isFlight(SpaceTimeBehaviorType behaviorTypeEnum) {
        return SpaceTimeBehaviorType.CIVIL_FLIGHT.equals(behaviorTypeEnum) || SpaceTimeBehaviorType.INTER_FLIGHT.equals(behaviorTypeEnum);
    }

    private boolean isTrain(SpaceTimeBehaviorType behaviorType) {
        return SpaceTimeBehaviorType.TRAIN.equals(behaviorType);
    }

    /**
     * 交通
     * @return
     */
    private List<LabelCrowdConditions.LabelParams> getConditionTypeForTraffic(SpaceTimeBehaviorType behaviorTypeEnum) {
        List<LabelCrowdConditions.LabelParams> paramsList = Lists.newArrayList();
        paramsList.add(generateLabelParams(SpaceTimeConditionType.DEP_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, spaceTimeCityCodeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.ARR_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, spaceTimeCityCodeEnumId, false));
        if (isFlight(behaviorTypeEnum)) {
            paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_RED_EYE_FLIGHT, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        }
        if (isFlight(behaviorTypeEnum) || isTrain(behaviorTypeEnum)) {
            paramsList.add(generateLabelParams(SpaceTimeConditionType.HAS_CHILDREN, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        }
        paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_LIVE_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_BIRTH_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_VALID, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.SAME_DEST_BEHAVIOR, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, true));
        return paramsList;
    }

    /**
     * 酒店
     * @return
     */
    private List<LabelCrowdConditions.LabelParams> getConditionTypeForHotel() {
        List<LabelCrowdConditions.LabelParams> paramsList = Lists.newArrayList();
        paramsList.add(generateLabelParams(SpaceTimeConditionType.ARR_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, spaceTimeCityCodeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_LIVE_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_VALID, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.HOTEL_STAR, LabelBizDataTypeEnum.SINGLE_ENUM, spaceTimeHotelStarEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.SAME_DEST_BEHAVIOR, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, true));
        return paramsList;
    }

    /**
     * 门票
     * @param behaviorTypeEnum
     * @return
     */
    private List<LabelCrowdConditions.LabelParams> getConditionTypeForTicket(SpaceTimeBehaviorType behaviorTypeEnum) {
        List<LabelCrowdConditions.LabelParams> paramsList = Lists.newArrayList();
        paramsList.add(generateLabelParams(SpaceTimeConditionType.ARR_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, spaceTimeCityCodeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_LIVE_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_VALID, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        if (Objects.equals(SpaceTimeBehaviorType.ENTRANCE, behaviorTypeEnum)) {
            paramsList.add(generateLabelParams(SpaceTimeConditionType.ENTRANCE_LEVEL, LabelBizDataTypeEnum.SINGLE_ENUM,
                    spaceTimeEntranceLevelEnumId, false));
        }
        paramsList.add(generateLabelParams(SpaceTimeConditionType.SAME_DEST_BEHAVIOR, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, true));
        return paramsList;
    }

    /**
     * 度假
     * @return
     */
    private List<LabelCrowdConditions.LabelParams> getConditionTypeForHoliday() {
        List<LabelCrowdConditions.LabelParams> paramsList = Lists.newArrayList();
        paramsList.add(generateLabelParams(SpaceTimeConditionType.ARR_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, spaceTimeCityCodeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_LIVE_CITY, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.IS_VALID, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, false));
        paramsList.add(generateLabelParams(SpaceTimeConditionType.SAME_DEST_BEHAVIOR, LabelBizDataTypeEnum.SINGLE_ENUM, defaultSpaceTimeEnumId, true));
        return paramsList;
    }

    private LabelCrowdConditions.LabelParams generateLabelParams(SpaceTimeConditionType conditionType,
                                                                 LabelBizDataTypeEnum dataType, Long enumId, boolean isSameDestBehavior) {
        LabelCrowdConditions.LabelParams params = new LabelCrowdConditions.LabelParams();
        params.setParamCode(conditionType.getCode());
        params.setParamDesc(conditionType.getDesc());
        params.setParamDataType(dataType);
        params.setParamDimEnumMetaId(enumId);
        if (isSameDestBehavior) {
            params.setParamValue(Collections.singletonList("true true"));
            params.setParamValueSequenceMatchConfig(
                    new LabelCrowdConditions.LabelParams.ValueSequenceMatchConfig("arrCityCode", "arrCityCode"));
        } else {
            params.setParamValueSequenceMatchConfig(
                    new LabelCrowdConditions.LabelParams.ValueSequenceMatchConfig("$.orderInfo.orderId", "$.orderInfo.orderId"));
        }
        return params;
    }
}
