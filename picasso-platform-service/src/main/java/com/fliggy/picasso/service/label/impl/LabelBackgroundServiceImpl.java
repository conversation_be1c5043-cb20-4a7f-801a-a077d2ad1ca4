package com.fliggy.picasso.service.label.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.aliyuncs.dataworks.model.v20200918.GetMetaTableBasicInfoResponse;
import com.fliggy.picasso.common.constants.label.LabelDataSourceEnum;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoQuery;
import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoBO;
import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoVO;
import com.fliggy.picasso.common.domain.crowd.record.AsyncTaskRecordVO;
import com.fliggy.picasso.common.domain.crowd.record.ChangeRecordData;
import com.fliggy.picasso.common.domain.crowd.record.PicassoChangeRecordBO;
import com.fliggy.picasso.common.domain.label.LabelDataConfig;
import com.fliggy.picasso.common.domain.label.LabelDataSourceConfig;
import com.fliggy.picasso.common.domain.label.LabelExtInfo;
import com.fliggy.picasso.common.domain.label.LabelInfoDO;
import com.fliggy.picasso.common.domain.label.LabelOdpsSourceConfig;
import com.fliggy.picasso.common.domain.label.LabelTagConfig;
import com.fliggy.picasso.common.domain.label.enumvalue.EnumDimDataDO;
import com.fliggy.picasso.common.enums.AsyncTaskRecordTypeEnum;
import com.fliggy.picasso.common.enums.AsyncTaskStatusEnum;
import com.fliggy.picasso.common.enums.ChangeRecordSourceTypeEnum;
import com.fliggy.picasso.common.enums.DeletedEnum;
import com.fliggy.picasso.common.enums.LabelScope;
import com.fliggy.picasso.common.enums.LabelType;
import com.fliggy.picasso.common.enums.Period;
import com.fliggy.picasso.common.enums.ProfileCodeEnum;
import com.fliggy.picasso.common.enums.bpms.ApprovalApplySceneEnum;
import com.fliggy.picasso.common.enums.crowd.CrowdTypeEnum;
import com.fliggy.picasso.common.enums.label.BizRegionEnum;
import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import com.fliggy.picasso.common.enums.label.LabelClassifyTabEnum;
import com.fliggy.picasso.common.enums.label.LabelDataDescEnum;
import com.fliggy.picasso.common.enums.label.LabelDataTimeTypeEnum;
import com.fliggy.picasso.common.enums.label.LabelDataValueTypeEnum;
import com.fliggy.picasso.common.enums.label.LabelOpenScopeEnum;
import com.fliggy.picasso.common.enums.label.LabelProcessTypeEnum;
import com.fliggy.picasso.common.enums.label.LabelSeperatorEnum;
import com.fliggy.picasso.common.enums.label.LabelSourceEnum;
import com.fliggy.picasso.common.enums.label.LabelStatusEnum;
import com.fliggy.picasso.common.enums.label.LabelTypeEnum;
import com.fliggy.picasso.common.enums.score.LabelTagEnum;
import com.fliggy.picasso.common.enums.score.ScoreTypeEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.common.odps.OdpsGuid;
import com.fliggy.picasso.common.odps.domain.OdpsRegisterProfileTableDTO;
import com.fliggy.picasso.dao.LabelInfoParam;
import com.fliggy.picasso.dao.LabelInfoParam.Criteria;
import com.fliggy.picasso.employee.Amdp1784EmployeeServiceImpl;
import com.fliggy.picasso.entity.bo.LabelBackgroundSearchInfoBO;
import com.fliggy.picasso.entity.bo.LabelInfoBackgroundBO;
import com.fliggy.picasso.entity.convertor.LabelBackgroundBoConvertor;
import com.fliggy.picasso.entity.param.LabelBackgroundSearchParam;
import com.fliggy.picasso.entity.param.LabelBackgroundUpsertParam;
import com.fliggy.picasso.entity.param.LabelBgConfigUpdateParam;
import com.fliggy.picasso.entity.vo.LabelDataConfigVO;
import com.fliggy.picasso.entity.vo.background.LabelBgUpsertResponse;
import com.fliggy.picasso.mapper.picasso.LabelInfoDAO;
import com.fliggy.picasso.openapi.AliyunOpenApiService;
import com.fliggy.picasso.service.approval.ApprovalInfoService;
import com.fliggy.picasso.service.approval.impl.AbstractApprovalServiceImpl;
import com.fliggy.picasso.service.approval.impl.ApprovalServiceRouter;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.fliggy.picasso.service.enumvalue.EnumDimDataService;
import com.fliggy.picasso.service.label.LabelBackgroundService;
import com.fliggy.picasso.service.odps.OdpsRegisterProfileTableService;
import com.fliggy.picasso.service.permission.PermissionService;
import com.fliggy.picasso.service.record.AsyncTaskRecordService;
import com.fliggy.picasso.service.record.PicassoRecordService;
import com.fliggy.picasso.service.score.ScoreInfoService;
import com.fliggy.picasso.utils.ChangeRecordUtils;
import com.fliggy.picasso.utils.label.LabelUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class LabelBackgroundServiceImpl implements LabelBackgroundService {

    private static final Integer OFFLINE_LABEL_ONLINE_CROWD_MAX_CNT = 50;

    @Switch(description = "创建/编辑标签审批开关，false走审批")
    public boolean debugUpsertLabelApproval = true;

    @Resource
    private LabelInfoDAO labelInfoDAO;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private AsyncTaskRecordService asyncTaskRecordService;
    @Autowired
    private CrowdService crowdService;
    @Resource
    private OdpsRegisterProfileTableService odpsRegisterProfileTableService;
    @Resource
    private ApprovalServiceRouter approvalServiceRouter;
    @Resource
    private PicassoRecordService picassoRecordService;
    @Resource
    private ScoreInfoService scoreInfoService;
    @Resource
    private ApprovalInfoService approvalInfoService;
    @Resource
    private Amdp1784EmployeeServiceImpl amdpEmployeeService;
    @Resource
    private EnumDimDataService enumDimDataService;
    @Autowired
    private AliyunOpenApiService aliyunOpenApiService;

    @AteyeInvoker(description = "queryById", paraDesc = "id")
    @Override
    public LabelInfoBackgroundBO queryById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }

        LabelInfoDO labelInfoDO = labelInfoDAO.selectByPrimaryKey(id);
        if (Objects.isNull(labelInfoDO)) {
            return null;
        }
        return LabelBackgroundBoConvertor.convertLabelInfoBOFromDO(labelInfoDO);
    }

    @Override
    public List<LabelInfoBackgroundBO> queryByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }

        LabelInfoParam param = new LabelInfoParam();
        param.createCriteria().andIdIn(ids);
        List<LabelInfoDO> labelInfoDOList = labelInfoDAO.selectByParam(param);
        if (CollectionUtils.isEmpty(labelInfoDOList)) {
            return Lists.newArrayList();
        }
        return labelInfoDOList.stream().map(LabelBackgroundBoConvertor::convertLabelInfoBOFromDO).collect(Collectors.toList());
    }

    @Override
    public LabelInfoBackgroundBO queryByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }

        LabelInfoParam param = new LabelInfoParam();
        param.createCriteria().andCodeEqualTo(code);
        List<LabelInfoDO> labelInfoDOList = labelInfoDAO.selectByParam(param);
        if (CollectionUtils.isEmpty(labelInfoDOList)) {
            return null;
        }
        return LabelBackgroundBoConvertor.convertLabelInfoBOFromDO(labelInfoDOList.get(0));
    }

    @Override
    public List<LabelInfoBackgroundBO> queryByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }

        LabelInfoParam param = new LabelInfoParam();
        param.createCriteria().andCodeIn(codes);
        List<LabelInfoDO> labelInfoDOList = labelInfoDAO.selectByParam(param);
        if (CollectionUtils.isEmpty(labelInfoDOList)) {
            return Lists.newArrayList();
        }
        return labelInfoDOList.stream().map(LabelBackgroundBoConvertor::convertLabelInfoBOFromDO).collect(Collectors.toList());
    }

    @Override
    public LabelInfoBackgroundBO queryByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }

        LabelInfoParam param = new LabelInfoParam();
        LabelInfoParam.Criteria criteria = param.createCriteria();
        criteria.andNameEqualTo(name);
        criteria.andDeletedEqualTo((byte) 0);
        List<LabelInfoDO> labelInfoDOList = labelInfoDAO.selectByParam(param);
        if (CollectionUtils.isEmpty(labelInfoDOList)) {
            return null;
        }
        return LabelBackgroundBoConvertor.convertLabelInfoBOFromDO(labelInfoDOList.get(0));
    }

    @Override
    public LabelInfoBackgroundBO queryByContent(String content) {
        if (StringUtils.isEmpty(content)) {
            return null;
        }

        if (StringUtils.isNumeric(content)) {
            return queryById(Long.valueOf(content));
        } else {
            LabelInfoParam param = new LabelInfoParam();
            LabelInfoParam.Criteria criteria = param.createCriteria();
            criteria.andNameLike(content).andDeletedEqualTo((byte) 0);
            Criteria orCriteria = param.or();
            orCriteria.andCodeLike(content).andDeletedEqualTo((byte) 0);
            List<LabelInfoDO> labelInfoDOList = labelInfoDAO.selectByParamWithBLOBs(param);
            if (CollectionUtils.isEmpty(labelInfoDOList)) {
                return null;
            }
            return LabelBackgroundBoConvertor.convertLabelInfoBOFromDO(labelInfoDOList.get(0));
        }
    }

    @Override
    public LabelBackgroundSearchInfoBO searchLabelList(LabelBackgroundSearchParam param) {
        if (Objects.isNull(param)) {
            return null;
        }
        LabelBackgroundSearchInfoBO result = new LabelBackgroundSearchInfoBO();
        Map<LabelClassifyTabEnum, Long> tabCntMap = new HashMap<>();

        // 超管
        Long totalCnt = countLabelList(param, false);
        tabCntMap.put(LabelClassifyTabEnum.ALL, totalCnt);
        if (permissionService.isSuperAdmin()) {
            tabCntMap.put(LabelClassifyTabEnum.AUTHORIZED, totalCnt);
            result.setTabCntMap(tabCntMap);
            if (totalCnt == 0) {
                return result;
            }
            List<LabelInfoBackgroundBO> labelBOList = getLabelList(param, false);
            labelBOList.forEach(label -> {
                label.setManagePermission(true);
                label.setEditPermission(true);
            });
            result.setLabelInfoList(labelBOList);
            return result;
        }

        // 全部tab
        Long authCnt = countLabelList(param, true);
        tabCntMap.put(LabelClassifyTabEnum.AUTHORIZED, authCnt);
        result.setTabCntMap(tabCntMap);
        if (Objects.isNull(param.getClassifyTab()) || Objects.equals(param.getClassifyTab(), LabelClassifyTabEnum.ALL)) {
            if (totalCnt == 0) {
                return result;
            }
            List<LabelInfoBackgroundBO> labelBOList = getLabelList(param, false);
            fillUpLabelEditPermission(labelBOList, param.getEmpId());
            result.setLabelInfoList(labelBOList);
            return result;
        }

        // 我有权限tab
        if (authCnt == 0) {
            return result;
        }
        List<LabelInfoBackgroundBO> authLabelBOList = getLabelList(param, true);
        fillUpLabelEditPermission(authLabelBOList, param.getEmpId());
        result.setLabelInfoList(authLabelBOList);
        return result;
    }

    private void fillUpLabelEditPermission(List<LabelInfoBackgroundBO> labelBOList, String empId) {
        if (CollectionUtils.isEmpty(labelBOList)) {
            return;
        }
        for (LabelInfoBackgroundBO labelInfoBackgroundBO : labelBOList) {
            if (Objects.nonNull(labelInfoBackgroundBO.getOwner()) && Objects.equals(labelInfoBackgroundBO.getOwner().getEmpId(), empId)) {
                labelInfoBackgroundBO.setManagePermission(true);
                labelInfoBackgroundBO.setEditPermission(true);
                continue;
            }
            if (CollectionUtils.isNotEmpty(labelInfoBackgroundBO.getManagers())) {
                boolean isManager = false;
                for (Employee manager : labelInfoBackgroundBO.getManagers()) {
                    if (Objects.equals(manager.getEmpId(), empId)) {
                        isManager = true;
                        break;
                    }
                }
                if (isManager) {
                    labelInfoBackgroundBO.setEditPermission(true);
                }
            }
        }
    }

    @Override
    public Boolean updateLabelConfig(LabelBgConfigUpdateParam param) {
        if (Objects.isNull(param) || Objects.isNull(param.getId())) {
            return false;
        }
        LabelInfoBackgroundBO labelInfoBackgroundBO = queryById(param.getId());
        if (Objects.isNull(labelInfoBackgroundBO)) {
            return false;
        }

        // 更新标签配置
        boolean needCreateEnumTask = needCreateEnumTask(labelInfoBackgroundBO);
        Boolean result = updateLabelConfig(param, needCreateEnumTask);
        if (!needCreateEnumTask) {
            return result;
        }

        // 创建自动配置任务
        createEnumTask(labelInfoBackgroundBO);
        return result;
    }

    @AteyeInvoker(description = "offlineLabelOnlineCrowdDoubleCheck", paraDesc = "labelCode")
    @Override
    public Map<Long, String> offlineLabelOnlineCrowdDoubleCheck(String labelCode) {
        if (StringUtils.isBlank(labelCode)) {
            return Maps.newHashMap();
        }

        List<CrowdMetaInfoDO> crowdMetaInfoDOList = crowdService.queryUseLabelCrowd(labelCode);
        if (CollectionUtils.isEmpty(crowdMetaInfoDOList)) {
            return Maps.newHashMap();
        }

        Map<Long, String> result = Maps.newHashMap();
        // 直接使用
        crowdMetaInfoDOList.forEach(crowd -> {
            if (result.size() >= OFFLINE_LABEL_ONLINE_CROWD_MAX_CNT) {
                return;
            }
            result.put(crowd.getId(), crowd.getCrowdName());
        });
        if (result.size() >= OFFLINE_LABEL_ONLINE_CROWD_MAX_CNT) {
            return result;
        }

        // 间接使用
        List<Long> operatedCrowdIds = getOperatedCrowdIds(new ArrayList<>(result.keySet()));
        List<CrowdMetaInfoDO> operateCrowdList = crowdService.queryByIds(operatedCrowdIds);
        operateCrowdList.forEach(crowd -> {
            if (result.size() >= OFFLINE_LABEL_ONLINE_CROWD_MAX_CNT) {
                return;
            }
            result.put(crowd.getId(), crowd.getCrowdName());
        });
        return result;
    }

    @Override
    public LabelBgUpsertResponse createLabel(LabelBackgroundUpsertParam param) {
        preCheckLabelUpsertParam(param);
        String name = param.getName();
        if (Objects.nonNull(param.getTimeType()) && Objects.equals(param.getTimeType(), LabelType.REALTIME)) {
            if (Objects.nonNull(param.getName()) && !param.getName().contains("实时-")) {
                name = "实时-" + param.getName();
            }
        }
        LabelInfoBackgroundBO originLabel = queryByName(name);
        if (Objects.nonNull(originLabel)) {
            throw new ParamErrorException("参数错误: 标签名称已存在");
        }
        boolean superAdmin = permissionService.isSuperAdmin() && debugUpsertLabelApproval;

        // 创建标签
        LabelInfoBackgroundBO labelInfoBackgroundBO = LabelBackgroundBoConvertor.convertLabelInfoUpsertParamToBO(param);
        labelInfoBackgroundBO.setCode(getLabelCode(param.getName()));
        labelInfoBackgroundBO.setStatus(superAdmin ? LabelStatusEnum.WAIT_ONLINE : LabelStatusEnum.APPROVING);
        labelInfoBackgroundBO.setDeleted(false);
        LabelInfoDO labelInfoDO = LabelBackgroundBoConvertor.convertLabelInfoBOToDO(labelInfoBackgroundBO);
        labelInfoDAO.insert(labelInfoDO);
        if (superAdmin) {
            return new LabelBgUpsertResponse(labelInfoDO.getId(), true, false);
        }

        // 创建变更记录，开始审批
        ApprovalInfoBO approvalInfo = generateApprovalInfo(labelInfoDO.getId(), param.getCreator());
        insertLabelUpdateRecord(null, labelInfoDO, approvalInfo, param.getCreator());
        AbstractApprovalServiceImpl approvalService = approvalServiceRouter.choose(ApprovalApplySceneEnum.LABEL_UPDATE);
        approvalService.approvalHandle(approvalInfo);
        LabelBgUpsertResponse result = new LabelBgUpsertResponse(labelInfoDO.getId(), true, true);
        ApprovalInfoBO approvalInfoBO = approvalInfoService.queryLatestByEntityIdAndApplyScene(labelInfoDO.getId(), ApprovalApplySceneEnum.LABEL_UPDATE);
        if (Objects.nonNull(approvalInfoBO)) {
            result.setApprovalId(approvalInfoBO.getApprovalId());
        }
        return result;
    }

    private String getLabelCode(String labelName) {
        String labelCode = LabelUtils.generateLabelCode(labelName);
        boolean isExist = true;
        while (isExist) {
            LabelInfoParam labelInfoParam = new LabelInfoParam();
            labelInfoParam.createCriteria().andCodeEqualTo(labelCode);
            if (labelInfoDAO.countByParam(labelInfoParam) <= 0) {
                isExist = false;
            } else {
                labelCode = LabelUtils.generateLabelCode(labelName);
            }
        }
        return labelCode;
    }

    public void insertLabelUpdateRecord(LabelInfoDO originLabel, LabelInfoDO newLabel, ApprovalInfoBO approvalInfo, Employee operator) {
        PicassoChangeRecordBO picassoChangeRecordBO = new PicassoChangeRecordBO(operator, ChangeRecordSourceTypeEnum.LABEL_UPDATE, String.valueOf(newLabel.getId()));
        if (Objects.nonNull(originLabel)) {
            picassoChangeRecordBO.setBeforeData(new ChangeRecordData(originLabel));
        }
        picassoChangeRecordBO.setAfterData(new ChangeRecordData(newLabel));
        List<ApprovalInfoVO> approvalInfos = Collections.singletonList(approvalInfo.convertToVO());
        picassoChangeRecordBO.setApprovalInfos(ChangeRecordUtils.handleApprovalInfos(approvalInfos, ChangeRecordSourceTypeEnum.LABEL_UPDATE));
        int insertRecord = picassoRecordService.insert(picassoChangeRecordBO);
        if (insertRecord < 1) {
            throw new RuntimeException("变更记录创建出错");
        }
    }

    @Override
    public LabelBgUpsertResponse updateLabel(LabelBackgroundUpsertParam param) {
        preCheckLabelUpsertParam(param);
        if (Objects.isNull(param.getId())) {
            throw new ParamErrorException("参数错误: 标签id不能为空");
        }
        LabelInfoDO originLabel = labelInfoDAO.selectByPrimaryKey(param.getId());
        if (Objects.isNull(originLabel)) {
            throw new ParamErrorException("标签不存在");
        }
        if (Objects.equals(originLabel.getStatus(), LabelStatusEnum.APPROVING.getCode())) {
            throw new ParamErrorException("标签正在审批中，请勿重复操作");
        }

        // 超管，或核心配置未变更不需要审批
        if (permissionService.isSuperAdmin() && debugUpsertLabelApproval) {
            return new LabelBgUpsertResponse(param.getId(), updateLabelConfig(param), false);
        }
        // 审批不通过场景，无论是否修改核心配置都需要重新发起审批
        if (!Objects.equals(originLabel.getStatus(), LabelStatusEnum.APPROVE_FAILED.getCode())
                && !isCoreConfigChange(param, originLabel)) {
            return new LabelBgUpsertResponse(param.getId(), updateLabelConfig(param), false);
        }

        // 更新状态
        boolean update = labelInfoDAO.batchUpdateStatus(Collections.singletonList(param.getId()), LabelStatusEnum.APPROVING.getCode());

        // 创建变更记录，开始审批
        LabelInfoBackgroundBO labelInfoBackgroundBO = LabelBackgroundBoConvertor.convertLabelInfoUpsertParamToBO(param);
        LabelInfoDO labelInfoDO = LabelBackgroundBoConvertor.convertLabelInfoBOToDO(labelInfoBackgroundBO);
        ApprovalInfoBO approvalInfo = generateApprovalInfo(originLabel.getId(), param.getModifier());
        insertLabelUpdateRecord(originLabel, labelInfoDO, approvalInfo, param.getModifier());
        AbstractApprovalServiceImpl approvalService = approvalServiceRouter.choose(ApprovalApplySceneEnum.LABEL_UPDATE);
        approvalService.approvalHandle(approvalInfo);
        LabelBgUpsertResponse result = new LabelBgUpsertResponse(param.getId(), update, true);
        ApprovalInfoBO approvalInfoBO = approvalInfoService.queryLatestByEntityIdAndApplyScene(param.getId(), ApprovalApplySceneEnum.LABEL_UPDATE);
        if (Objects.nonNull(approvalInfoBO)) {
            result.setApprovalId(approvalInfoBO.getApprovalId());
        }
        return result;
    }

    private boolean isCoreConfigChange(LabelBackgroundUpsertParam param, LabelInfoDO originLabel) {
        if (!Objects.equals(param.getName(), originLabel.getName())) {
            return true;
        }

        if (Objects.isNull(param.getSourceConfig()) && Objects.isNull(originLabel.getSourceConfig())) {
            return false;
        }
        if (Objects.isNull(param.getSourceConfig()) || Objects.isNull(originLabel.getSourceConfig())) {
            return true;
        }
        OdpsGuid guid = param.getSourceConfig().getGuid();
        LabelOdpsSourceConfig originSourceConfig = JSON.parseObject(originLabel.getSourceConfig(), LabelOdpsSourceConfig.class);
        OdpsGuid originGuid = originSourceConfig.getGuid();
        if (!Objects.equals(guid.getFullGuid(), originGuid.getFullGuid())) {
            return true;
        }

        // 单一属性标签，字段不同
        if (Objects.equals(param.getLabelType(), LabelTypeEnum.SINGLE_ATTRIBUTE)) {
            if (!Objects.equals(param.getSourceConfig().getField(), originSourceConfig.getField())) {
                return true;
            }
        }
        return false;
    }

    private boolean updateLabelConfig(LabelBackgroundUpsertParam param) {
        LabelInfoBackgroundBO labelInfoBackgroundBO = LabelBackgroundBoConvertor.convertLabelInfoUpsertParamToBO(param);
        labelInfoBackgroundBO.setStatus(LabelStatusEnum.WAIT_ONLINE);
        LabelInfoDO labelInfoDO = LabelBackgroundBoConvertor.convertLabelInfoBOToDO(labelInfoBackgroundBO);
        return labelInfoDAO.updateByPrimaryKeySelective(labelInfoDO) > 0;
    }

    @AteyeInvoker(description = "testLabelSqlCheck", paraDesc = "sql&tableGuid")
    @Override
    public Boolean labelSqlCheck(String sql, String tableGuid) {
        if (StringUtils.isBlank(sql)) {
            throw new ParamErrorException("参数错误: sql不能为空");
        }
        if (StringUtils.isBlank(tableGuid)) {
            throw new ParamErrorException("参数错误: odps表不能为空");
        }

        sql = sql.replace('\n', ' ').toLowerCase();
        int fromIndex = sql.indexOf(" from ");
        if (fromIndex == -1) {
            throw new ParamErrorException("sql格式错误");
        }
        String prefixSql = sql.substring(0, fromIndex).trim();
        if (prefixSql.contains(",")) {
            throw new ParamErrorException("sql格式错误：select必须为单字段，且命名为user_id");
        }
        if (!prefixSql.contains(" user_id")) {
            throw new ParamErrorException("sql格式错误：select必须为单字段，且命名为user_id");
        }

        String suffixSql = sql.substring(fromIndex + 5).trim();
        if (suffixSql.contains("join ")) {
            throw new ParamErrorException("sql格式错误：不允许多表进行关联");
        }
        if (!suffixSql.contains(tableGuid)) {
            throw new ParamErrorException("sql格式错误：表名必须为" + tableGuid);
        }

        if (suffixSql.contains("'${date}'") || suffixSql.contains("'${date}") || suffixSql.contains("${date}'")) {
            throw new ParamErrorException("sql格式错误：${date}变量周围单引号去掉");
        }
        return true;
    }

    private ApprovalInfoBO generateApprovalInfo(Long id, Employee operator) {
        ApprovalInfoBO approvalInfoBO = new ApprovalInfoBO();
        approvalInfoBO.setApprovalCreator(operator);
        approvalInfoBO.setApplyScene(ApprovalApplySceneEnum.LABEL_UPDATE);
        approvalInfoBO.setEntityId(id);
        return approvalInfoBO;
    }

    private void preCheckLabelUpsertParam(LabelBackgroundUpsertParam param) {
        if (Objects.isNull(param)) {
            throw new ParamErrorException("参数错误: 标签创建参数不能为空");
        }
        if (Objects.nonNull(param.getName()) && param.getName().length() > 30) {
            throw new ParamErrorException("参数错误: 标签名称不能超过30个字符");
        }
        if (Objects.nonNull(param.getDescription()) && param.getDescription().length() > 200) {
            throw new ParamErrorException("参数错误: 标签描述不能超过200个字符");
        }

        if (Objects.isNull(param.getSourceConfig())) {
            throw new ParamErrorException("参数错误: 数据源配置不能为空");
        } else if (Objects.equals(param.getLabelType(), LabelTypeEnum.SINGLE_ATTRIBUTE) && Objects.equals(param.getTimeType(), LabelType.OFFLINE)) {
            LabelOdpsSourceConfig odpsSourceConfig = param.getSourceConfig();
            if (StringUtils.isEmpty(odpsSourceConfig.getProject()) || StringUtils.isEmpty(odpsSourceConfig.getTable())) {
                throw new ParamErrorException("参数错误: 表名不能为空");
            }
            if (StringUtils.isEmpty(odpsSourceConfig.getField())) {
                throw new ParamErrorException("参数错误: 字段名不能为空");
            }

            String tableGuid = odpsSourceConfig.getGuid().getGuid();
            GetMetaTableBasicInfoResponse.Data metaTable = aliyunOpenApiService.getMetaTableBasicInfo(tableGuid);
            if (Objects.isNull(metaTable)) {
                throw new ParamErrorException("当前表不存在");
            }
            //if (Objects.isNull(metaTable.getDataSize()) || metaTable.getDataSize() <= 0) {
            //    throw new ParamErrorException("表数据不能为空");
            //}

            OdpsRegisterProfileTableDTO tableDTO = odpsRegisterProfileTableService.find(odpsSourceConfig.getGuid());
            if (Objects.isNull(tableDTO)) {
                throw new ParamErrorException("参数错误: odps表未注册，请先完成表注册");
            }
            if (Objects.equals(tableDTO.getPrimaryKey(), odpsSourceConfig.getField())) {
                throw new ParamErrorException("参数错误: 主键不能为字段名");
            }
            odpsSourceConfig.setPrimaryKey(tableDTO.getPrimaryKey());
            odpsSourceConfig.setPartitionField(tableDTO.getPartitionKey());

            LabelInfoDO onlineLabel = checkTableFieldAlreadyOnline(odpsSourceConfig, param.getId());
            if (Objects.nonNull(onlineLabel)) {
                throw new ParamErrorException("参数错误: 该字段已上线，请勿重复创建");
            }
        } else if (Objects.equals(param.getLabelType(), LabelTypeEnum.SINGLE_ATTRIBUTE) && Objects.equals(param.getTimeType(), LabelType.REALTIME)) {
            LabelOdpsSourceConfig odpsSourceConfig = param.getSourceConfig();
            if (StringUtils.isEmpty(odpsSourceConfig.getProject()) && StringUtils.isEmpty(odpsSourceConfig.getTable())) {
                throw new ParamErrorException("参数错误: 表名不能为空");
            }
        } else if (Objects.equals(param.getLabelType(), LabelTypeEnum.SELF_DEFINED_SQL)) {
            LabelOdpsSourceConfig odpsSourceConfig = param.getSourceConfig();
            if (Objects.isNull(odpsSourceConfig.getSqlConfig())) {
                throw new ParamErrorException("参数错误: sql配置不能为空");
            }
            labelSqlCheck(odpsSourceConfig.getSqlConfig().getSqlTemplate(), odpsSourceConfig.getGuid().getGuid());
        }

        if (Objects.equals(param.getDataType(), LabelBizDataTypeEnum.ENUM) || Objects.equals(param.getDataType(), LabelBizDataTypeEnum.KV)) {
            LabelDataConfigVO dataConfig = param.getDataConfig();
            if (Objects.equals(dataConfig.getDataDesc(), LabelDataDescEnum.COMMON_ENUM)) {
                if (Objects.isNull(dataConfig.getDimEnumId())) {
                    throw new ParamErrorException("参数错误: 枚举id不能为空");
                }
            } else {
                if (Objects.equals(dataConfig.getDataDesc(), LabelDataDescEnum.MANAL_INPUT) && dataConfig.getDataValue().size() > 20) {
                    throw new ParamErrorException("参数错误: 手动输入枚举值不能超过20个");
                }
            }
        }
    }

    /**
     * 检查表字段是否已经上线
     * @param odpsSourceConfig
     * @return
     */
    private LabelInfoDO checkTableFieldAlreadyOnline(LabelOdpsSourceConfig odpsSourceConfig, Long id) {
        if (Objects.isNull(odpsSourceConfig) || StringUtils.isBlank(odpsSourceConfig.getProject())
                || StringUtils.isBlank(odpsSourceConfig.getTable()) || StringUtils.isBlank(odpsSourceConfig.getField())) {
            return null;
        }

        List<LabelInfoDO> labelInfoDOList = labelInfoDAO.checkTableFieldOnline(odpsSourceConfig.getTable(), odpsSourceConfig.getField());
        if (CollectionUtils.isEmpty(labelInfoDOList)) {
            return null;
        }
        // 精确校验，表名相同
        labelInfoDOList = labelInfoDOList.stream().filter(label -> {
            if (Objects.isNull(label.getSourceConfig())) {
                return false;
            }
            LabelOdpsSourceConfig sourceConfig = JSON.parseObject(label.getSourceConfig(), LabelOdpsSourceConfig.class);
            if (Objects.isNull(sourceConfig) || Objects.isNull(sourceConfig.getProject()) || Objects.isNull(sourceConfig.getTable())) {
                return false;
            }
            return sourceConfig.getProject().equals(odpsSourceConfig.getProject()) && sourceConfig.getTable().equals(odpsSourceConfig.getTable());
        }).collect(Collectors.toList());

        if (Objects.nonNull(id)) {
            labelInfoDOList = labelInfoDOList.stream().filter(label -> !Objects.equals(id, label.getId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(labelInfoDOList)) {
            return null;
        }
        return labelInfoDOList.get(0);
    }

    /**
     * 获取所有组合人群id
     * @param crowdIdList
     * @return
     */
    private List<Long> getOperatedCrowdIds(List<Long> crowdIdList) {
        int currentDepth = 0;
        int maxDepth = 20;
        Set<Long> finalCrowdIdSet = new HashSet<>(crowdIdList);
        // 根据人群id，查询间接使用的人群id
        while (currentDepth < maxDepth && !crowdIdList.isEmpty()) {
            Set<Long> tmpCrowdIdSet = new HashSet<>();
            crowdIdList.forEach(crowdId -> {
                List<Long> combinedCrowdIds = getCombinedCrowdIds(crowdId);
                if (CollectionUtils.isNotEmpty(combinedCrowdIds)) {
                    tmpCrowdIdSet.addAll(combinedCrowdIds);
                }
            });

            currentDepth++;
            crowdIdList.clear();
            if (CollectionUtils.isNotEmpty(tmpCrowdIdSet)) {
                crowdIdList.addAll(tmpCrowdIdSet);
                finalCrowdIdSet.addAll(tmpCrowdIdSet);
            }
        }
        return new ArrayList<>(finalCrowdIdSet);
    }

    /**
     * 获取组合人群id
     * @param crowdId
     * @return
     */
    private List<Long> getCombinedCrowdIds(Long crowdId) {
        if (Objects.isNull(crowdId)) {
            return Collections.emptyList();
        }

        Set<Long> result = new HashSet<>();
        CrowdMetaInfoQuery query1 = new CrowdMetaInfoQuery();
        query1.setDeleted((byte) 0);
        query1.setExpiredDateStart(new Date());
        query1.setCrowdType(CrowdTypeEnum.OPERATE_CROWD);
        query1.setExtInfoLike(String.valueOf(crowdId));
        List<Long> operateCrowdIds = crowdService.queryAllIdByInfoQuery(query1);
        if (CollectionUtils.isNotEmpty(operateCrowdIds)) {
            result.addAll(operateCrowdIds);
        }

        CrowdMetaInfoQuery query2 = new CrowdMetaInfoQuery();
        query2.setDeleted((byte) 0);
        query2.setExpiredDateStart(new Date());
        query2.setCrowdType(CrowdTypeEnum.OPERATE_EXPRESSION_CROWD);
        query2.setConditionsLike(String.valueOf(crowdId));
        List<Long> operateExpressionCrowdIds = crowdService.queryAllIdByInfoQuery(query2);
        if (CollectionUtils.isNotEmpty(operateExpressionCrowdIds)) {
            result.addAll(operateExpressionCrowdIds);
        }
        return new ArrayList<>(result);
    }

    /**
     * 创建自动配置任务
     * @param labelInfoBackgroundBO
     * @return
     */
    private boolean createEnumTask(LabelInfoBackgroundBO labelInfoBackgroundBO) {
        if (Objects.isNull(labelInfoBackgroundBO) || Objects.isNull(labelInfoBackgroundBO.getCode())) {
            return false;
        }

        String labelCode = labelInfoBackgroundBO.getCode();
        AsyncTaskRecordVO asyncTaskRecordVO = asyncTaskRecordService.queryByEntity(AsyncTaskRecordTypeEnum.TABLE_ENUM_EXTRACT, labelCode);
        if (Objects.isNull(asyncTaskRecordVO)) {
            asyncTaskRecordVO = new AsyncTaskRecordVO();
            asyncTaskRecordVO.setEntity(labelCode);
            asyncTaskRecordVO.setEntityType(AsyncTaskRecordTypeEnum.TABLE_ENUM_EXTRACT);
            asyncTaskRecordVO.setTaskStatus(AsyncTaskStatusEnum.INIT);
            return asyncTaskRecordService.insert(asyncTaskRecordVO) > 0;
        } else {
            asyncTaskRecordVO.setGmtModified(new Date());
            asyncTaskRecordVO.setTaskStatus(AsyncTaskStatusEnum.INIT);
            return asyncTaskRecordService.update(asyncTaskRecordVO) > 0;
        }
    }

    /**
     * 是否需要创建自动配置任务
     * @param labelInfoBackgroundBO
     * @return
     */
    private boolean needCreateEnumTask(LabelInfoBackgroundBO labelInfoBackgroundBO) {
        if (Objects.isNull(labelInfoBackgroundBO) || Objects.isNull(labelInfoBackgroundBO.getDataType())
                || Objects.isNull(labelInfoBackgroundBO.getDataConfig())) {
            return false;
        }

        if (!LabelBizDataTypeEnum.isEnum(labelInfoBackgroundBO.getDataType())) {
            return false;
        }

        LabelDataConfig dataConfig = labelInfoBackgroundBO.getDataConfig();
        return Objects.nonNull(dataConfig.getDataDesc()) && Objects.equals(dataConfig.getDataDesc(), LabelDataDescEnum.AUTO_CONFIG);
    }

    private Boolean updateLabelConfig(LabelBgConfigUpdateParam param, boolean needCreateEnumTask) {
        LabelInfoDO originLabel = labelInfoDAO.selectByPrimaryKey(param.getId());
        if (Objects.isNull(originLabel)) {
            throw new ParamErrorException("标签不存在");
        }

        LabelInfoDO updateDO = new LabelInfoDO();
        updateDO.setId(param.getId());
        if (Objects.nonNull(param.getOpenScope())) {
            updateDO.setOpenScope(param.getOpenScope().getCode());
            if (Objects.equals(param.getOpenScope(), LabelOpenScopeEnum.PUBLIC)) {
                updateDO.setSharers(StringUtils.EMPTY);
            } else if (Objects.equals(param.getOpenScope(), LabelOpenScopeEnum.PRIVATE) && Objects.nonNull(param.getSharers())) {
                updateDO.setSharers(JSON.toJSONString(param.getSharers()));
            }
        }
        if (Objects.nonNull(param.getTagConfig())) {
            updateDO.setTagConfig(JSON.toJSONString(param.getTagConfig()));
            List<String> tagList = StringUtils.isEmpty(originLabel.getTagList()) ? new ArrayList<>() : JSON.parseArray(originLabel.getTagList(), String.class);
            if (Objects.nonNull(param.getTagConfig().getIsOfficial()) && param.getTagConfig().getIsOfficial()) {
                if (!tagList.contains(LabelTagEnum.OFFICIAL.getCode())) {
                    tagList.add(LabelTagEnum.OFFICIAL.getCode());
                    updateDO.setTagList(JSON.toJSONString(tagList));
                }
            } else {
                if (tagList.contains(LabelTagEnum.OFFICIAL.getCode())) {
                    tagList.remove(LabelTagEnum.OFFICIAL.getCode());
                    updateDO.setTagList(JSON.toJSONString(tagList));
                }
            }
        }
        if (Objects.nonNull(param.getOwner())) {
            updateDO.setDataOwner(JSON.toJSONString(param.getOwner()));
        }
        if (CollectionUtils.isNotEmpty(param.getManagers())) {
            updateDO.setManagers(JSON.toJSONString(param.getManagers()));
        }
        if (Objects.nonNull(param.getStatus())) {
            if (Objects.equals(param.getStatus(), LabelStatusEnum.ACTIVATE) && needCreateEnumTask) {
                updateDO.setStatus(LabelStatusEnum.ONLINE_ING.getCode());
            } else {
                updateDO.setStatus(param.getStatus().getCode());
            }
        }
        return labelInfoDAO.updateByPrimaryKeySelective(updateDO) > 0;
    }

    private Long countLabelList(LabelBackgroundSearchParam param, boolean isAuth) {
        String content = null;
        if (StringUtils.isNotBlank(param.getContent())) {
            content = param.getContent();
        }
        Long categoryIdNew = null;
        if (Objects.nonNull(param.getCategoryIdNew())) {
            categoryIdNew = param.getCategoryIdNew();
        }
        List<String> bizRegionList = null;
        if (CollectionUtils.isNotEmpty(param.getBizRegionList())) {
            bizRegionList = param.getBizRegionList().stream().map(BizRegionEnum::getCode).collect(Collectors.toList());
        }
        List<Byte> statusList = null;
        if (CollectionUtils.isNotEmpty(param.getStatusList())) {
            statusList = param.getStatusList().stream().map(LabelStatusEnum::getCode).collect(Collectors.toList());
        }
        List<String> openScopeList = null;
        if (CollectionUtils.isNotEmpty(param.getOpenScopeList())) {
            openScopeList = param.getOpenScopeList().stream().map(LabelOpenScopeEnum::getCode).collect(Collectors.toList());
        }
        List<String> tagList = null;
        if (CollectionUtils.isNotEmpty(param.getTagList())) {
            tagList = param.getTagList().stream().map(LabelTagEnum::getCode).collect(Collectors.toList());
        }
        String empId = null;
        if (isAuth && StringUtils.isNotBlank(param.getEmpId())) {
            empId = param.getEmpId();
        }
        return (long) labelInfoDAO.countLabelList(content, categoryIdNew, bizRegionList, statusList, openScopeList, tagList, empId);
    }

    private List<LabelInfoBackgroundBO> getLabelList(LabelBackgroundSearchParam param, boolean isAuth) {
        String content = null;
        if (StringUtils.isNotBlank(param.getContent())) {
            content = param.getContent();
        }
        Long categoryIdNew = null;
        if (Objects.nonNull(param.getCategoryIdNew())) {
            categoryIdNew = param.getCategoryIdNew();
        }
        List<String> bizRegionList = null;
        if (CollectionUtils.isNotEmpty(param.getBizRegionList())) {
            bizRegionList = param.getBizRegionList().stream().map(BizRegionEnum::getCode).collect(Collectors.toList());
        }
        List<Byte> statusList = null;
        if (CollectionUtils.isNotEmpty(param.getStatusList())) {
            statusList = param.getStatusList().stream().map(LabelStatusEnum::getCode).collect(Collectors.toList());
        }
        List<String> openScopeList = null;
        if (CollectionUtils.isNotEmpty(param.getOpenScopeList())) {
            openScopeList = param.getOpenScopeList().stream().map(LabelOpenScopeEnum::getCode).collect(Collectors.toList());
        }
        List<String> tagList = null;
        if (CollectionUtils.isNotEmpty(param.getTagList())) {
            tagList = param.getTagList().stream().map(LabelTagEnum::getCode).collect(Collectors.toList());
        }
        String empId = null;
        if (isAuth && StringUtils.isNotBlank(param.getEmpId())) {
            empId = param.getEmpId();
        }

        Integer pageNo = param.getPageNo();
        if (Objects.isNull(pageNo) || pageNo < 0) {
            pageNo = 1;
        }
        Integer pageSize = param.getPageSize();
        if (Objects.isNull(pageSize) || pageSize < 0) {
            pageSize = 10;
        }
        Integer pageIndex = (pageNo - 1) * pageSize;
        List<LabelInfoDO> labelInfoDOList = labelInfoDAO.searchLabelList(content, categoryIdNew, bizRegionList, statusList, openScopeList, tagList, empId, pageIndex, pageSize);
        List<LabelInfoBackgroundBO> labelInfoBOList = labelInfoDOList.stream().map(LabelBackgroundBoConvertor::convertLabelInfoBOFromDO).collect(Collectors.toList());
        fillUpLabelBackgroundInfos(labelInfoBOList);
        return labelInfoBOList;
    }

    private void fillUpLabelBackgroundInfos(List<LabelInfoBackgroundBO> labels) {
        List<String> labelCodes = labels.stream().map(LabelInfoBackgroundBO::getCode).collect(Collectors.toList());
        Map<String, BigDecimal> scoreMap = scoreInfoService.batchQueryLabelScoreByScoreType(labelCodes, ScoreTypeEnum.HEALTH_SCORE);
        for (LabelInfoBackgroundBO label : labels) {
            if (scoreMap.containsKey(label.getCode())) {
                BigDecimal bigDecimal = scoreMap.get(label.getCode());
                label.setHealthScore(String.valueOf(bigDecimal));
            }

            if (Objects.equals(label.getStatus(), LabelStatusEnum.APPROVING) || Objects.equals(label.getStatus(), LabelStatusEnum.APPROVE_FAILED)) {
                ApprovalInfoBO approvalInfoBO = approvalInfoService.queryLatestByEntityIdAndApplyScene(label.getId(), ApprovalApplySceneEnum.LABEL_UPDATE);
                label.setApproveId(Objects.isNull(approvalInfoBO) ? "" : approvalInfoBO.getApprovalId());
            }

            if (Objects.nonNull(label.getModifier()) && StringUtils.isNotBlank(label.getModifier().getEmpId()) && StringUtils.isBlank(label.getModifier().getNickName())) {
                if (Objects.nonNull(label.getCreator()) && Objects.equals(label.getCreator().getEmpId(), label.getModifier().getEmpId())) {
                    label.setModifier(new Employee(label.getCreator().getEmpId(), label.getCreator().getNickName()));
                } else {
                    label.getModifier().setNickName(amdpEmployeeService.getNameByEmpId(label.getModifier().getEmpId()));
                }
            }
        }
    }

    @AteyeInvoker(description = "testSearchLabelList", paraDesc = "empId&content&bizRegions&status&openScopes&tags&classifyTab&pageNo&pageSize")
    public void testSearchLabelList(String empId, String content, String bizRegions, String status, String openScopes,
                                  String tags, String classifyTab, Integer pageNo, Integer pageSize) {
        LabelBackgroundSearchParam param = new LabelBackgroundSearchParam();
        param.setEmpId(empId);
        if (StringUtils.isNotBlank(content)) {
            param.setContent(content);
        }
        if (StringUtils.isNotBlank(bizRegions)) {
            param.setBizRegionList(Arrays.stream(bizRegions.split(",")).map(BizRegionEnum::valueOf).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(status)) {
            param.setStatusList(Arrays.stream(status.split(",")).map(LabelStatusEnum::valueOf).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(openScopes)) {
            param.setOpenScopeList(Arrays.stream(openScopes.split(",")).map(LabelOpenScopeEnum::valueOf).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(tags)) {
            param.setTagList(Arrays.stream(tags.split(",")).map(LabelTagEnum::valueOf).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(classifyTab)) {
            param.setClassifyTab(LabelClassifyTabEnum.valueOf(classifyTab));
        }
        param.setPageNo(pageNo);
        param.setPageSize(pageSize);
        LabelBackgroundSearchInfoBO searchInfoBO = searchLabelList(param);
        Ateye.out.println(JSON.toJSONString(searchInfoBO));
    }

    @AteyeInvoker(description = "testUpdateLabelConfig", paraDesc = "id&openScope&sharers&tagConfig&status&owner&managers")
    public void testUpdateLabelConfig(Long id, String openScope, String sharers, String tagConfig,
                                      String status, String owner, String managers) {
        LabelBgConfigUpdateParam param = new LabelBgConfigUpdateParam();
        param.setId(id);
        if (StringUtils.isNotBlank(openScope)) {
            param.setOpenScope(LabelOpenScopeEnum.valueOf(openScope));
        }
        if (StringUtils.isNotBlank(sharers)) {
            param.setSharers(JSON.parseArray(sharers, Employee.class));
        }
        if (StringUtils.isNotBlank(tagConfig)) {
            param.setTagConfig(JSON.parseObject(tagConfig, LabelTagConfig.class));
        }
        if (StringUtils.isNotBlank(status)) {
            param.setStatus(LabelStatusEnum.valueOf(status));
        }
        if (StringUtils.isNotBlank(owner)) {
            param.setOwner(JSON.parseObject(owner, Employee.class));
        }
        if (StringUtils.isNotBlank(managers)) {
            param.setManagers(JSON.parseArray(managers, Employee.class));
        }
        Boolean result = updateLabelConfig(param);
        Ateye.out.println(result);
    }

    @AteyeInvoker(description = "testCreateLabel", paraDesc = "id&bizRegions&profileCode&timeType&labelType&project&table&field&type&updatePeriod&name&description&openScope&securityLevel&categoryId&owner&processType&dataSource&scopes&dataType&dataDesc&isUpdate")
    public void testCreateLabel(Long id, String bizRegions, ProfileCodeEnum profileCode, LabelType timeType, LabelTypeEnum labelType, String project,
                                String table, String field, String type, Period updatePeriod, String name, String description,
                                LabelOpenScopeEnum openScope, Integer securityLevel, Long categoryId, String owner, LabelProcessTypeEnum processType,
                                LabelDataSourceEnum dataSource, String scopes, LabelBizDataTypeEnum dataType, LabelDataDescEnum dataDesc, boolean isUpdate) {
        LabelBackgroundUpsertParam param = new LabelBackgroundUpsertParam();
        if (Objects.nonNull(id)) {
            param.setId(id);
        }
        if (Objects.nonNull(bizRegions)) {
            List<BizRegionEnum> bizRegionList = new ArrayList<>(Arrays.asList(bizRegions.split(","))).stream().map(
                BizRegionEnum::valueOf).collect(Collectors.toList());
            param.setBizRegionList(bizRegionList);
        }
        if (Objects.nonNull(profileCode)) {
            param.setAccountType(profileCode);
        }
        if (Objects.nonNull(timeType)) {
            param.setTimeType(timeType);
        }
        if (Objects.nonNull(labelType)) {
            param.setLabelType(labelType);
        }
        if (Objects.nonNull(project) && Objects.nonNull(table) && Objects.nonNull(field) && Objects.nonNull(type)) {
            param.setSource(LabelSourceEnum.ODPS);
            LabelOdpsSourceConfig labelOdpsSourceConfig = new LabelOdpsSourceConfig();
            labelOdpsSourceConfig.setProject(project);
            labelOdpsSourceConfig.setTable(table);
            labelOdpsSourceConfig.setField(field);
            labelOdpsSourceConfig.setType(type);
            param.setSourceConfig(labelOdpsSourceConfig);
        }
        if (Objects.nonNull(updatePeriod)) {
            param.setUpdatePeriod(updatePeriod);
        }
        if (Objects.nonNull(name)) {
            param.setName(name);
        }
        if (Objects.nonNull(description)) {
            param.setDescription(description);
        }
        if (Objects.nonNull(openScope)) {
            param.setOpenScope(openScope);
        }
        if (Objects.nonNull(securityLevel)) {
            param.setSecurityLevel(securityLevel);
        }
        if (Objects.nonNull(categoryId)) {
            param.setCategoryIdNew(categoryId);
        }
        if (Objects.nonNull(owner)) {
            param.setOwner(JSON.parseObject(owner, Employee.class));
        }
        if (Objects.nonNull(processType)) {
            param.setProcessType(processType);
        }
        if (Objects.nonNull(dataSource)) {
            LabelDataSourceConfig labelDataSourceConfig = new LabelDataSourceConfig();
            labelDataSourceConfig.setDataSource(dataSource);
            param.setDataSourceConfig(labelDataSourceConfig);
        }
        if (Objects.nonNull(scopes)) {
            param.setScopeList(JSON.parseArray(scopes, LabelScope.class));
        }
        if (Objects.nonNull(dataType)) {
            param.setDataType(dataType);
        }
        if (Objects.nonNull(dataDesc)) {
            LabelDataConfigVO labelDataConfig = new LabelDataConfigVO();
            labelDataConfig.setDataDesc(dataDesc);
            labelDataConfig.setDataValueType(LabelDataValueTypeEnum.STRING);
            param.setDataConfig(labelDataConfig);
        }
        if (isUpdate) {
            LabelBgUpsertResponse result = updateLabel(param);
            Ateye.out.println("update:" + JSON.toJSONString(result));
        } else {
            LabelBgUpsertResponse result = createLabel(param);
            Ateye.out.println("create label id:" + JSON.toJSONString(result));
        }
    }

    @AteyeInvoker(description = "batchUpdateLabelType", paraDesc = "ids&labelType")
    public void batchUpdateLabelType(String ids, String labelType) {
        List<Long> idList = JSON.parseArray(ids, Long.class);

        int cnt = 0;
        for (Long id : idList) {
            LabelInfoDO updateDO = new LabelInfoDO();
            updateDO.setId(id);
            updateDO.setLabelType(labelType);
            cnt += labelInfoDAO.updateByPrimaryKeySelective(updateDO);
        }
        Ateye.out.println("update:" + cnt);
    }

    @AteyeInvoker(description = "batchUpdateLabelProcessType", paraDesc = "ids&processType")
    public void batchUpdateLabelProcessType(String ids, String processType) {
        List<Long> idList = JSON.parseArray(ids, Long.class);

        int cnt = 0;
        for (Long id : idList) {
            LabelInfoDO updateDO = new LabelInfoDO();
            updateDO.setId(id);
            updateDO.setProcessType(processType);
            cnt += labelInfoDAO.updateByPrimaryKeySelective(updateDO);
        }
        Ateye.out.println("update:" + cnt);
    }

    @AteyeInvoker(description = "batchUpdateLabelBasicInfo", paraDesc = "isSingleTest&id")
    public void batchUpdateLabelBasicInfo(boolean isSingleTest, Long id) {
        List<LabelInfoDO> labelInfoDOList = new ArrayList<>();
        if (isSingleTest) {
            labelInfoDOList.add(labelInfoDAO.selectByPrimaryKey(id));
        } else {
            LabelInfoParam labelInfoParam = new LabelInfoParam();
            LabelInfoParam.Criteria criteria = labelInfoParam.createCriteria();
            criteria.andIdGreaterThan(0L);
            criteria.andDeletedEqualTo(DeletedEnum.fromBoolean(false));
            labelInfoDOList = labelInfoDAO.selectByParamWithBLOBs(labelInfoParam);
        }

        int cnt = 0;
        for (LabelInfoDO labelInfoDO : labelInfoDOList) {
            LabelInfoDO updateDO = new LabelInfoDO();
            updateDO.setId(labelInfoDO.getId());
            updateDO.setOpenScope(LabelOpenScopeEnum.PUBLIC.getCode());
            List<Employee> managers = new ArrayList<>();
            if (StringUtils.isNotBlank(labelInfoDO.getBizOwner())) {
                managers.add(JSON.parseObject(labelInfoDO.getBizOwner(), Employee.class));
            }
            if (StringUtils.isNotBlank(labelInfoDO.getDataOwner())) {
                managers.add(JSON.parseObject(labelInfoDO.getDataOwner(), Employee.class));
            }
            if (StringUtils.isNotBlank(labelInfoDO.getQualityOwner())) {
                managers.add(JSON.parseObject(labelInfoDO.getQualityOwner(), Employee.class));
            }
            updateDO.setManagers(JSON.toJSONString(managers));

            LabelExtInfo extInfo = null;
            if (Objects.nonNull(labelInfoDO.getExtInfo())) {
                extInfo = JSON.parseObject(labelInfoDO.getExtInfo(), LabelExtInfo.class);
            }

            LabelTagConfig labelTagConfig = new LabelTagConfig(false, true, true);
            updateDO.setTagConfig(JSON.toJSONString(labelTagConfig));

            if (Objects.nonNull(labelInfoDO.getDataType())) {
                LabelDataConfig labelDataConfig = new LabelDataConfig();
                if (Objects.equals(labelInfoDO.getDataType(), LabelBizDataTypeEnum.ENUM.getValue())) {
                    labelDataConfig.setDataValueType(LabelDataValueTypeEnum.STRING);
                    labelDataConfig.setDataDesc(LabelDataDescEnum.COMMON_ENUM);
                } else if (Objects.equals(labelInfoDO.getDataType(), LabelBizDataTypeEnum.MULTI_VALUE.getValue())) {
                    labelDataConfig.setDataValueType(LabelDataValueTypeEnum.STRING);
                    labelDataConfig.setMultiEnumSep(LabelSeperatorEnum.COMMA);
                    labelDataConfig.setDataDesc(LabelDataDescEnum.COMMON_ENUM);
                } else if (Objects.equals(labelInfoDO.getDataType(), LabelBizDataTypeEnum.NUMBER.getValue())) {
                    labelDataConfig.setDataValueType(LabelDataValueTypeEnum.BIGINT);
                } else if (Objects.equals(labelInfoDO.getDataType(), LabelBizDataTypeEnum.DATE.getValue())) {
                    labelDataConfig.setDataValueType(LabelDataValueTypeEnum.STRING);
                    labelDataConfig.setTimeType(LabelDataTimeTypeEnum.YMDHMS);
                } else if (Objects.equals(labelInfoDO.getDataType(), LabelBizDataTypeEnum.KV.getValue())) {
                    labelDataConfig.setDataValueType(LabelDataValueTypeEnum.STRING);
                    labelDataConfig.setKvSep(LabelSeperatorEnum.COLON);
                    labelDataConfig.setKvGroupSep(LabelSeperatorEnum.COMMA);
                    labelDataConfig.setKvDisplayKey("目标");
                    labelDataConfig.setKvDisplayValue("权重");
                    labelDataConfig.setDataDesc(LabelDataDescEnum.COMMON_ENUM);
                    if (Objects.nonNull(extInfo) && CollectionUtils.isNotEmpty(extInfo.getWeightRange())) {
                        labelDataConfig.setKvWeightRange(extInfo.getWeightRange());
                    }
                }
                updateDO.setDataConfig(JSON.toJSONString(labelDataConfig));
            }
            cnt += labelInfoDAO.updateByPrimaryKeySelective(updateDO);
        }
        Ateye.out.println("update:" + cnt);
    }

    @AteyeInvoker(description = "batchUpdateDoubleLabelManagers", paraDesc = "isSingleTest&id")
    public void batchUpdateDoubleLabelManagers(boolean isSingleTest, Long id) {
        List<LabelInfoDO> labelInfoDOList = new ArrayList<>();
        if (isSingleTest) {
            labelInfoDOList.add(labelInfoDAO.selectByPrimaryKey(id));
        } else {
            LabelInfoParam labelInfoParam = new LabelInfoParam();
            LabelInfoParam.Criteria criteria = labelInfoParam.createCriteria();
            criteria.andIdGreaterThan(0L);
            criteria.andDeletedEqualTo(DeletedEnum.fromBoolean(false));
            labelInfoDOList = labelInfoDAO.selectByParam(labelInfoParam);
        }

        int cnt = 0;
        for (LabelInfoDO labelInfoDO : labelInfoDOList) {
            if (Objects.isNull(labelInfoDO.getId()) || Objects.isNull(labelInfoDO.getManagers())) {
                continue;
            }
            List<Employee> managers = JSON.parseArray(labelInfoDO.getManagers(), Employee.class);

            Set<String> employeeIdSet = new HashSet<>();
            List<Employee> employeeList = new ArrayList<>();
            for (Employee employee : managers) {
                if (StringUtils.isBlank(employee.getEmpId())) {
                    continue;
                }
                if (employeeIdSet.contains(employee.getEmpId())) {
                    continue;
                }
                employeeIdSet.add(employee.getEmpId());
                employeeList.add(employee);
            }

            LabelInfoDO updateDO = new LabelInfoDO();
            updateDO.setId(labelInfoDO.getId());
            updateDO.setManagers(JSON.toJSONString(employeeList));
            cnt += labelInfoDAO.updateByPrimaryKeySelective(updateDO);
        }
        Ateye.out.println("update:" + cnt);
    }

    @AteyeInvoker(description = "batchConvertEnumInfo", paraDesc = "ids")
    public void batchConvertEnumInfo(String ids) {
        List<Long> idList = JSON.parseArray(ids, Long.class);
        LabelInfoParam labelInfoParam = new LabelInfoParam();
        labelInfoParam.createCriteria().andIdIn(idList);
        List<LabelInfoDO> labelInfoDOList = labelInfoDAO.selectByParamWithBLOBs(labelInfoParam);

        int cnt = 0;
        for (LabelInfoDO labelInfoDO : labelInfoDOList) {
            if (Objects.isNull(labelInfoDO.getDimEnumMetaId())) {
                continue;
            }
            LabelInfoBackgroundBO labelInfoBackgroundBO = LabelBackgroundBoConvertor.convertLabelInfoBOFromDO(labelInfoDO);
            LabelDataConfig labelDataConfig = Objects.isNull(labelInfoBackgroundBO.getDataConfig()) ? new LabelDataConfig() : labelInfoBackgroundBO.getDataConfig();
            labelDataConfig.setDataDesc(LabelDataDescEnum.MANAL_INPUT);
            labelDataConfig.setDataValue(getEnumValueMap(labelInfoDO.getDimEnumMetaId()));

            LabelInfoDO updateDO = new LabelInfoDO();
            updateDO.setId(labelInfoDO.getId());
            updateDO.setDataConfig(JSON.toJSONString(labelDataConfig));
            cnt += labelInfoDAO.updateByPrimaryKeySelective(updateDO);
        }
        Ateye.out.println("update:" + cnt);
    }

    @AteyeInvoker(description = "updateLabelDataConfig", paraDesc = "id&configJson")
    public int updateLabelDataConfig(Long id, String configJson) {
        LabelDataConfig labelDataConfig = JSON.parseObject(configJson, LabelDataConfig.class);
        LabelInfoDO updateDO = new LabelInfoDO();
        updateDO.setId(id);
        updateDO.setDataConfig(JSON.toJSONString(labelDataConfig));
        return labelInfoDAO.updateByPrimaryKeySelective(updateDO);
    }

    private Map<String, String> getEnumValueMap(Long dimEnumid) {
        Map<String, String> enumValueMap = new HashMap<>();
        List<EnumDimDataDO> enumDimDataDOList = enumDimDataService.queryAllByDimMetaId(dimEnumid);
        for (EnumDimDataDO enumDimDataDO : enumDimDataDOList) {
            enumValueMap.put(enumDimDataDO.getEnumCode(), enumDimDataDO.getEnumDesc());
        }
        return enumValueMap;
    }

    @AteyeInvoker(description = "batchUpdateLabelDataSourceConfig", paraDesc = "ids")
    public void batchUpdateLabelDataSourceConfig(String ids) {
        List<Long> idList = JSON.parseArray(ids, Long.class);
        LabelInfoParam labelInfoParam = new LabelInfoParam();
        labelInfoParam.createCriteria().andIdIn(idList);
        List<LabelInfoDO> labelInfoDOList = labelInfoDAO.selectByParamWithBLOBs(labelInfoParam);
        int cnt = 0;
        for (LabelInfoDO labelInfoDO : labelInfoDOList) {
            if (Objects.isNull(labelInfoDO.getId()) || Objects.isNull(labelInfoDO.getExtInfo())) {
                continue;
            }
            LabelExtInfo labelExtInfo = JSON.parseObject(labelInfoDO.getExtInfo(), LabelExtInfo.class);
            if (Objects.isNull(labelExtInfo) || Objects.isNull(labelExtInfo.getDataSourceConfig())) {
                continue;
            }
            LabelDataSourceConfig dataSourceConfig = labelExtInfo.getDataSourceConfig();
            dataSourceConfig.setNeedApproval(labelExtInfo.getNeedApproval());
            labelExtInfo.setDataSourceConfig(dataSourceConfig);

            LabelInfoDO updateDO = new LabelInfoDO();
            updateDO.setId(labelInfoDO.getId());
            updateDO.setExtInfo(JSON.toJSONString(labelExtInfo));
            cnt += labelInfoDAO.updateByPrimaryKeySelective(updateDO);
        }
        Ateye.out.println("update:" + cnt);
    }

    @AteyeInvoker(description = "批量调整时间类型", paraDesc = "ids")
    public void testBatchUpdateLabelDataConfigTimeType(String ids) {
        List<Long> idList = JSON.parseArray(ids, Long.class);
        LabelInfoParam labelInfoParam = new LabelInfoParam();
        labelInfoParam.createCriteria().andIdIn(idList);
        List<LabelInfoDO> labelInfoDOList = labelInfoDAO.selectByParamWithBLOBs(labelInfoParam);
        int cnt = 0;
        for (LabelInfoDO labelInfoDO : labelInfoDOList) {
            if (Objects.isNull(labelInfoDO.getId()) || Objects.isNull(labelInfoDO.getDataConfig())) {
                continue;
            }
            LabelDataConfig labelDataConfig = JSON.parseObject(labelInfoDO.getDataConfig(), LabelDataConfig.class);
            labelDataConfig.setTimeType(LabelDataTimeTypeEnum.YMDHMS);

            LabelInfoDO updateDO = new LabelInfoDO();
            updateDO.setId(labelInfoDO.getId());
            updateDO.setDataConfig(JSON.toJSONString(labelDataConfig));
            cnt += labelInfoDAO.updateByPrimaryKeySelective(updateDO);
        }
        Ateye.out.println("update:" + cnt);
    }

}
