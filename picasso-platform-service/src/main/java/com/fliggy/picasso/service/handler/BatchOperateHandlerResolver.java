package com.fliggy.picasso.service.handler;

import com.fliggy.picasso.common.constants.CrowdLabelBatchOperateActionEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class BatchOperateHandlerResolver {

    private Map<CrowdLabelBatchOperateActionEnum, BatchOperateHandler> handlerMap = new HashMap<>();

    public BatchOperateHandler resolve(CrowdLabelBatchOperateActionEnum action) {
        return handlerMap.get(action);
    }

    public void register(CrowdLabelBatchOperateActionEnum action, BatchOperateHandler handler) {
        handlerMap.put(action, handler);
    }
}
