package com.fliggy.picasso.service.approval.impl;

import java.util.List;
import java.util.Objects;

import com.alibaba.alipmc.api.model.bpm.ProcessInstance;
import com.alibaba.fastjson.JSON;

import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoBO;
import com.fliggy.picasso.common.domain.crowd.record.ChangeApprovalInfo;
import com.fliggy.picasso.common.domain.crowd.record.PicassoChangeRecordBO;
import com.fliggy.picasso.common.enums.ChangeRecordSourceTypeEnum;
import com.fliggy.picasso.common.enums.bpms.AdexFluxSceneEnum;
import com.fliggy.picasso.common.enums.bpms.ApprovalApplySceneEnum;
import com.fliggy.picasso.common.enums.bpms.BpmsApprovalStatusEnum;
import com.fliggy.picasso.common.enums.crowd.CrowdTypeEnum;
import com.fliggy.picasso.common.enums.group.GroupApplySceneEnum;
import com.fliggy.picasso.common.enums.group.GroupStatusEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.entity.bo.CrowdInfoBO;
import com.fliggy.picasso.utils.AdexFluxLogUtils;
import com.fliggy.picasso.utils.ChangeRecordUtils;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class CrowdUpdateApprovalServiceImpl extends AbstractApprovalServiceImpl {

    @Switch(description = "跳过bpms审批流程")
    public Boolean CROWD_UPDATE_SKIP_BPMS_APPROVAL = false;

    @Override
    public ApprovalApplySceneEnum getApplyScene() {
        return ApprovalApplySceneEnum.CROWD_UPDATE;
    }

    @Override
    public Boolean approvalJudge(CrowdInfoBO crowdInfoBO) {
        if (Objects.isNull(crowdInfoBO)) {
            log.error("CrowdUpdateApprovalServiceImpl approvalJudge exception: crowdMetaInfoVO is null");
            throw new ParamErrorException("crowdMetaInfoDO is null");
        }

        if (Objects.isNull(crowdInfoBO.getCrowdType()) || !Objects.equals(crowdInfoBO.getCrowdType(), CrowdTypeEnum.LABEL_CROWD)) {
            return false;
        }

        // 已有流程在审批中
        if (Objects.nonNull(crowdInfoBO.getId()) && isApprovalRunning(crowdInfoBO.getId(), ApprovalApplySceneEnum.CROWD_UPDATE)) {
            return false;
        }

        // 匹配/PUSH场景才走审批
        List<GroupApplySceneEnum> applyScenes = crowdInfoBO.getApplyScenes();
        if (CollectionUtils.isNullOrEmpty(applyScenes) || !supportMatchOrPushScene(applyScenes)) {
            return false;
        }

        // 未使用需要审批的流通中心标签
        if (!useNeedApprovalAdexLabel(crowdInfoBO)) {
            return false;
        }

        // 更新场景，配置未变更，且审批成功，不需要走审批
        boolean judgeResult = Objects.isNull(crowdInfoBO.getId()) || !checkConditionUnchanged(crowdInfoBO)
                || checkApplySceneChangedAndSupportMatchOrPushScene(crowdInfoBO)
                || !isApprovalSuccess(crowdInfoBO.getId(), ApprovalApplySceneEnum.CROWD_UPDATE);
        if (print_approval_debug_log) {
            log.info("CrowdUpdateApprovalServiceImpl approvalJudge crowdId:{}, result:{}", crowdInfoBO.getId(), judgeResult);
        }
        return judgeResult;
    }

    @Override
    public void approvalHandle(ApprovalInfoBO approvalInfo) {
        // 已有流程在审批中
        if (isApprovalRunning(approvalInfo.getEntityId(), approvalInfo.getApplyScene())) {
            return;
        }

        // 工号不足6位补零
        if (Objects.isNull(approvalInfo.getApprovalCreator()) || StringUtils.isEmpty(approvalInfo.getApprovalCreator().getEmpId())) {
            log.error("CrowdUpdateApprovalServiceImpl approvalHandle exception: empId is empty, entityId:{}", approvalInfo.getEntityId());
            throw new ParamErrorException("empId is empty");
        }
        String empId = empIdAddUpZero(approvalInfo.getApprovalCreator().getEmpId());

        // 发起审批
        ProcessInstance result = bpmsCommonService.startProcessInstance(
                ADEX_LABEL_USER_BPMS_CODE,
                "流通中心标签使用审批",
                empId,
                getInitData(approvalInfo),
                BPMS_AUTH_KEY);
        int insertApprovalInfo = approvalInfoService.insert(resolveProcessInstanceResult(approvalInfo, result));
        if (insertApprovalInfo <= 0) {
            log.error("CrowdUpdateApprovalServiceImpl approvalHandle exception: 创建审批流失败, entityId:{}", approvalInfo.getEntityId());
            throw new RuntimeException("创建审批流失败");
        }

        // 记录审批信息
        int updateRecord = picassoRecordService.updateApprovalInfo(String.valueOf(approvalInfo.getEntityId()), ChangeRecordSourceTypeEnum.CROWD_UPDATE
                , approvalInfo.getApplyScene(), result.getProcessInstanceId());
        if (updateRecord <= 0) {
            log.error("CrowdUpdateApprovalServiceImpl approvalHandle exception: 更新记录失败, entityId:{}", approvalInfo.getEntityId());
            throw new RuntimeException("更新记录失败");
        }
        if (print_approval_debug_log) {
            log.info("CrowdUpdateApprovalServiceImpl approvalHandle success entityId:{}, result:{}", approvalInfo.getEntityId(), JSON.toJSONString(result));
        }
    }

    @Override
    public void approvalCallBack(String approvalId) {
        ApprovalInfoBO approvalInfoBO = approvalInfoService.queryByApprovalId(approvalId);
        if (Objects.isNull(approvalInfoBO)) {
            log.error("CrowdUpdateApprovalServiceImpl approvalCallBack exception: approvalId is not exist, approvalId:{}", approvalId);
            return;
        }

        ProcessInstance processInstance = null;
        if (CROWD_UPDATE_SKIP_BPMS_APPROVAL) {
            processInstance = mockProcessInstance(approvalId);
        } else {
            processInstance = bpmsCommonService.getProcessInstance(approvalId, BPMS_AUTH_KEY);
        }
        approvalInfoBO.setApprovalStatus(resolveApprovalStatus(processInstance));
        Boolean updateApproval = approvalInfoService.updateStatusWhileDiff(approvalInfoBO);
        if (!updateApproval) {
            log.error("CrowdUpdateApprovalServiceImpl approvalCallBack exception: update approvalInfo fail, approvalId:{}", approvalId);
            return;
        }
        if (!Objects.equals(BpmsApprovalStatusEnum.APPROVAL_SUCCESS, approvalInfoBO.getApprovalStatus())) {
            return;
        }

        // 获取人群配置
        PicassoChangeRecordBO picassoChangeRecordBO = picassoRecordService.queryLatestBySourceIdAndSourceType(String.valueOf(approvalInfoBO.getEntityId()), ChangeRecordSourceTypeEnum.CROWD_UPDATE);
        if (Objects.isNull(picassoChangeRecordBO.getAfterData()) || CollectionUtils.isNullOrEmpty(picassoChangeRecordBO.getApprovalInfos())) {
            // log
            return;
        }
        ChangeApprovalInfo changeApprovalInfo = ChangeRecordUtils.getRecordApprovalInfoByApplyScene(picassoChangeRecordBO.getApprovalInfos(), ApprovalApplySceneEnum.CROWD_UPDATE);
        if (Objects.isNull(changeApprovalInfo) || Objects.isNull(changeApprovalInfo.getNeedApproval()) || !changeApprovalInfo.getNeedApproval()
                || !Objects.equals(approvalInfoBO.getApprovalId(), changeApprovalInfo.getApprovalId())) {
            // log
            return;
        }

        // 人群更新
        CrowdMetaInfoDO crowdMetaInfoDO = picassoChangeRecordBO.getAfterData().getCrowdMetaInfo();
        crowdMetaInfoDO.setId(approvalInfoBO.getEntityId());
        crowdMetaInfoDO.setCrowdStatus(GroupStatusEnum.INIT);
        Boolean updateCrowd = crowdService.updateAcrossFront(crowdMetaInfoDO);
        if (!updateCrowd) {
            log.error("CrowdUpdateApprovalServiceImpl approvalCallBack exception: update crowd fail, approvalId:{}", approvalId);
        }
        if (print_approval_debug_log) {
            log.info("CrowdUpdateApprovalServiceImpl approvalCallBack success, approvalId:{}, result:{}", approvalId, JSON.toJSONString(processInstance));
        }

        // 流通中心数据回流
        adexFluxLogUtils.printCrowdRelatedAdexLabelFluxLog(AdexFluxSceneEnum.CROWD_UPDATE, approvalInfoBO.getEntityId(),
            approvalInfoBO.getEntityId(), approvalInfoBO.getApprovalCreator().getEmpId());
    }

    /**
     * 判断标签人群配置是否变更
     * @param crowdInfoBO
     * @return
     */
    private Boolean checkConditionUnchanged(CrowdInfoBO crowdInfoBO) {
        if (Objects.isNull(crowdInfoBO) || Objects.isNull(crowdInfoBO.getConditions())
                || Objects.isNull(crowdInfoBO.getCrowdType()) || !Objects.equals(CrowdTypeEnum.LABEL_CROWD, crowdInfoBO.getCrowdType())) {
            return true;
        }

        CrowdMetaInfoDO originCrowdMetaInfoDO = crowdService.queryById(crowdInfoBO.getId());
        return Objects.nonNull(originCrowdMetaInfoDO) && Objects.nonNull(originCrowdMetaInfoDO.getConditions())
                && Objects.equals(originCrowdMetaInfoDO.getConditions(), crowdInfoBO.getConditions());
    }

    /**
     * 判断应用场景是否人群配置是否变更
     * @param crowdInfoBO
     * @return
     */
    private Boolean checkApplySceneChangedAndSupportMatchOrPushScene(CrowdInfoBO crowdInfoBO) {
        if (Objects.isNull(crowdInfoBO) || CollectionUtils.isNullOrEmpty(crowdInfoBO.getApplyScenes())) {
            return true;
        }

        CrowdMetaInfoDO originCrowdMetaInfoDO = crowdService.queryById(crowdInfoBO.getId());
        return Objects.nonNull(originCrowdMetaInfoDO) && Objects.nonNull(originCrowdMetaInfoDO.getCrowdApplyScene())
                && !Objects.equals(originCrowdMetaInfoDO.getCrowdApplyScene(), crowdInfoBO.getApplyScenes())
                && supportMatchOrPushScene(crowdInfoBO.getApplyScenes());
    }

    private boolean supportMatchOrPushScene(List<GroupApplySceneEnum> applyScenes) {
        return applyScenes.contains(GroupApplySceneEnum.MATCH)
                || applyScenes.contains(GroupApplySceneEnum.PUSH);
    }
}
