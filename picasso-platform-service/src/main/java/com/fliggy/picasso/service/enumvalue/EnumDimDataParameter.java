package com.fliggy.picasso.service.enumvalue;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@Data
public class EnumDimDataParameter implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    private Long id;

    /**
     *   枚举id。具有实际的意义，存量的枚举，需要使用原有id。不具有实际意义的枚举该值为-1。
     */
    private Long enumId;

    /**
     *   外键，维度id。
     */
    private Long dimMetaId;

    /**
     *   枚举代码（转换前）
     */
    private String enumCode;

    private List<String> enumCodes;

    /**
     *   枚举描述（转换后）
     */
    private String enumDesc;

    /**
     *   父节点id（0代表根节点）
     */
    private Long parentId;

    /**
     *   是否叶子节点
     */
    private Byte leaf;

    /**
     *   节点层级
     */
    private String level;

    /**
     *   是否逻辑删除（0:否，1:是）
     */
    private Byte deleted;

    /**
     *   数据业务日期，格式YYYYMMDD
     */
    private String ds;
}