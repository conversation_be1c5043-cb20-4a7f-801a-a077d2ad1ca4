package com.fliggy.picasso.service.profile.impl;

import com.fliggy.picasso.common.domain.profile.ProfileDagConfigVO;
import com.fliggy.picasso.convertor.ProfileDagConfigConvertor;
import com.fliggy.picasso.dao.ProfileDagConfigDO;
import com.fliggy.picasso.mapper.picasso.ProfileDagConfigDAO;
import com.fliggy.picasso.service.profile.ProfileDagNodeConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ProfileDagNodeConfigServiceImpl implements ProfileDagNodeConfigService {

    @Resource
    private ProfileDagConfigDAO profileDagConfigDAO;

    @Override
    public long insert(ProfileDagConfigVO profileDagConfigVO) {
        ProfileDagConfigDO profileDagConfigDO = ProfileDagConfigConvertor.convertToDO(profileDagConfigVO);
        return profileDagConfigDAO.insert(profileDagConfigDO);
    }

    @Override
    public int update(ProfileDagConfigVO profileDagConfigVO) {
        ProfileDagConfigDO profileDagConfigDO = ProfileDagConfigConvertor.convertToDO(profileDagConfigVO);
        return profileDagConfigDAO.updateByPrimaryKeySelective(profileDagConfigDO);
    }

    @Override
    public ProfileDagConfigVO selectById(Long id) {
        ProfileDagConfigDO profileDagConfigDO = profileDagConfigDAO.selectByPrimaryKey(id);
        return ProfileDagConfigConvertor.convertToVO(profileDagConfigDO);
    }

    @Override
    public List<ProfileDagConfigVO> queryAllLatestConfigs() {
        List<ProfileDagConfigDO> profileDagConfigDOList = profileDagConfigDAO.queryAllLatestConfigs();
        return profileDagConfigDOList.stream().map(ProfileDagConfigConvertor::convertToVO).collect(Collectors.toList());
    }
}
