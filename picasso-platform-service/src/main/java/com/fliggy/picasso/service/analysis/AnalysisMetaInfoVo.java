package com.fliggy.picasso.service.analysis;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class AnalysisMetaInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     *   创建时间
     */
    @Getter
    @Setter
    private Date gmtCreate;

    /**
     *   修改时间
     */
    @Getter
    @Setter
    private Date gmtModified;

    /**
     *   分析名称
     */
    @Getter
    @Setter
    private String analysisName;

    /**
     *   分析描述
     */
    @Getter
    @Setter
    private String analysisDesc;

    /**
     *   分析类型
     */
    @Getter
    @Setter
    private String analysisType;

    /**
     *   创建人
     */
    @Getter
    @Setter
    private String creator;

    /**
     *   管理员
     */
    @Getter
    @Setter
    private String operator;

    /**
     *   分析状态
     */
    @Getter
    @Setter
    private String status;

    /**
     *   最后更新时间
     */
    @Getter
    @Setter
    private Date lastUpdateTime;

    /**
     *   是否删除
     */
    @Getter
    @Setter
    private Byte isDeleted;

    /**
     *   分析配置
     */
    @Getter
    @Setter
    private String analysisConfig;

    /**
     *   分析扩展信息
     */
    @Getter
    @Setter
    private String extInfo;
}