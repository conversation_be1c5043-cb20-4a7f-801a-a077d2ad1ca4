package com.fliggy.picasso.service.profile;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.common.domain.profile.PhysicalProfileSceneConfig;
import com.fliggy.picasso.common.enums.DeletedEnum;
import com.fliggy.picasso.common.enums.PhysicalProfileSceneEnum;
import com.fliggy.picasso.common.enums.PhysicalProfileType;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.dao.PhysicalProfileInfoDO;
import com.fliggy.picasso.dao.PhysicalProfileInfoParam;
import com.fliggy.picasso.dao.PhysicalProfileInfoParam.Criteria;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.entity.odps.OdpsMasterConfig;
import com.fliggy.picasso.mapper.picasso.PhysicalProfileInfoDAO;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.label.LabelOrderService;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO.PhysicalProfileStatus;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class PhysicalProfileInfoServiceImpl implements PhysicalProfileInfoService {

    private static final Logger log = LoggerFactory.getLogger(PhysicalProfileInfoServiceImpl.class);

    @Autowired
    private PhysicalProfileInfoDAO physicalProfileInfoDAO;

    @Autowired
    private PhysicalProfileInfoConverter physicalProfileInfoConverter;

    @Autowired
    private LabelInfoService labelInfoService;

    @Autowired
    private LabelOrderService labelOrderService;

    /**
     * 根据参数统计总数
     *
     * @param param
     */
    @Override
    public long count(PhysicalProfileInfoParameter param) {
        PhysicalProfileInfoParam physicalProfileInfoParam = new PhysicalProfileInfoParam();
        Criteria criteria = physicalProfileInfoParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        return physicalProfileInfoDAO.countByParam(physicalProfileInfoParam);
    }

    /**
     * 根据参数查询
     *
     * @param param
     */
    @Override
    public PhysicalProfileInfoDTO find(PhysicalProfileInfoParameter param) {
        PhysicalProfileInfoParam physicalProfileInfoParam = new PhysicalProfileInfoParam();
        Criteria criteria = physicalProfileInfoParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        List<PhysicalProfileInfoDO> list = physicalProfileInfoDAO.selectByParam(physicalProfileInfoParam);
        if (null == list || list.isEmpty()) {
            return null;
        }
        return physicalProfileInfoConverter.convertFromDO(list.get(0));
    }

    /**
     * 根据参数查询
     *
     * @param physicalProfileCode
     */
    @Override
    public PhysicalProfileInfoDTO findByCode(String physicalProfileCode) {
        PhysicalProfileInfoParam physicalProfileInfoParam = new PhysicalProfileInfoParam();
        Criteria criteria = physicalProfileInfoParam.createCriteria();
        criteria.andPhysicalProfileCodeEqualTo(physicalProfileCode);
        List<PhysicalProfileInfoDO> list = physicalProfileInfoDAO.selectByParam(physicalProfileInfoParam);
        if (null == list || list.isEmpty()) {
            return null;
        }
        return physicalProfileInfoConverter.convertFromDO(list.get(0));
    }

    /**
     * 列表查询
     *
     * @param param
     */
    @Override
    public List<PhysicalProfileInfoDTO> list(PhysicalProfileInfoParameter param) {
        PhysicalProfileInfoParam physicalProfileInfoParam = new PhysicalProfileInfoParam();
        Criteria criteria = physicalProfileInfoParam.createCriteria();

        if (param.getId() != null) {
            criteria.andIdEqualTo(param.getId());
        }

        if (param.getProfileId() != null) {
            criteria.andProfileIdEqualTo(param.getProfileId());
        }

        if (param.getProfileCode() != null) {
            criteria.andProfileCodeEqualTo(param.getProfileCode());
        }

        if (StringUtils.isNotEmpty(param.getProfileName())) {
            criteria.andProfileNameLike("%" + param.getProfileName() + "%");
        }

        if (param.getPhysicalProfileCode() != null) {
            criteria.andPhysicalProfileCodeEqualTo(param.getPhysicalProfileCode());
        }

        if (StringUtils.isNotEmpty(param.getPhysicalProfileName())) {
            criteria.andPhysicalProfileNameLike("%" + param.getProfileName() + "%");
        }

        if (param.getType() != null) {
            criteria.andTypeEqualTo(param.getType().getCode());
        }

        if (param.getDeleted() != null) {
            criteria.andDeletedEqualTo(param.getDeleted());
        }

        if (param.getStatus() != null) {
            criteria.andStatusEqualTo(param.getStatus());
        }

        return selectByParamWithBLOBs(physicalProfileInfoParam);
    }

    @Override
    public List<PhysicalProfileInfoDTO> list(Set<Long> ids) {
        List<PhysicalProfileInfoDO> physicalProfileInfoDOS = physicalProfileInfoDAO.selectByIds(ids);
        return physicalProfileInfoDOS
                .stream()
                .map(physicalProfileInfoDO -> {
                    return physicalProfileInfoConverter.convertFromDO(physicalProfileInfoDO);
                }).collect(Collectors.toList());
    }

    /**
     * @param physicalProfileInfoParam
     * @return
     */
    private List<PhysicalProfileInfoDTO> selectByParamWithBLOBs(PhysicalProfileInfoParam physicalProfileInfoParam) {

        List<PhysicalProfileInfoDO> list = physicalProfileInfoDAO.selectByParamWithBLOBs(physicalProfileInfoParam);
        if (null == list || list.isEmpty()) {
            return null;
        }
        List<PhysicalProfileInfoDTO> result = Lists.newArrayList();
        for (PhysicalProfileInfoDO record : list) {
            PhysicalProfileInfoDTO physicalProfileInfoDTO = physicalProfileInfoConverter.convertFromDO(record);
            result.add(physicalProfileInfoDTO);
        }

        return result;
    }

    @AteyeInvoker(description = "手动新增/更新物理画像", paraDesc = "物理画像信息（JSON格式字符串）")
    public boolean saveOrUpdatePhyProfileManully(String phyProfileString) {
        if (StringUtils.isBlank(phyProfileString)) {
            return false;
        }
        PhysicalProfileInfoDO physicalProfileInfoDO = JSON.parseObject(phyProfileString, PhysicalProfileInfoDO.class);
        if (Objects.nonNull(physicalProfileInfoDO.getId())){
            physicalProfileInfoDAO.updateByPrimaryKeySelective(physicalProfileInfoDO);
        }else {
            physicalProfileInfoDAO.insert(physicalProfileInfoDO);
        }
        return true;
    }

    public static void main(String[] args) {
        PhysicalProfileInfoDO physicalProfileInfoDO = new PhysicalProfileInfoDO();
//        physicalProfileInfoDO.setId(28L);
        physicalProfileInfoDO.setGmtCreate(new Date());
        physicalProfileInfoDO.setGmtModified(new Date());
        physicalProfileInfoDO.setProfileId(15L);
        physicalProfileInfoDO.setProfileCode("device");
        physicalProfileInfoDO.setProfileName("设备画像");
        physicalProfileInfoDO.setPrimaryKey("device_one_id");
        physicalProfileInfoDO.setPhysicalProfileCode("device_p");
        physicalProfileInfoDO.setPhysicalProfileName("设备画像");
        physicalProfileInfoDO.setType((byte) 0);
        physicalProfileInfoDO.setStatus(PhysicalProfileStatus.REFLUX_PREPARATION.getCode());
        physicalProfileInfoDO.setOdpsTable("trip_device_wide_table");
        physicalProfileInfoDO.setOdpsProject("trip_profile");
        physicalProfileInfoDO.setOdpsTableComment("设备画像宽表");
        physicalProfileInfoDO.setOdpsTableLifecycle(15);
        physicalProfileInfoDO.setOlapTable("null");
        physicalProfileInfoDO.setCreator("395824");
        physicalProfileInfoDO.setModifier("395824");
        physicalProfileInfoDO.setDeleted((byte) 0);
        physicalProfileInfoDO.setOdpsTablePtKey("ds");
        physicalProfileInfoDO.setOdpsTableUpdateTime(new Date());
        physicalProfileInfoDO.setOlapTableUpdateTime(new Date());
        physicalProfileInfoDO.setKvstoreUpdateTime(new Date());
        OdpsMasterConfig odpsMasterConfig = new OdpsMasterConfig();
        odpsMasterConfig.setProject("trip_profile");
        odpsMasterConfig.setTable("trip_device_one_id");
        physicalProfileInfoDO.setOdpsMasterConfig(JSON.toJSONString(odpsMasterConfig));

        PhysicalProfileSceneConfig physicalProfileSceneConfig = new PhysicalProfileSceneConfig();
        physicalProfileSceneConfig.setPhysicalProfileSceneName(PhysicalProfileSceneEnum.PUT_IN_LINDORM.getSceneName());
        List<PhysicalProfileSceneConfig> configs = Collections.singletonList(physicalProfileSceneConfig);
        physicalProfileInfoDO.setPhysicalProfileScene(JSON.toJSONString(configs));
        System.out.println(JSON.toJSONString(physicalProfileInfoDO));
    }

    /**
     * 创建
     *
     * @param physicalProfileInfoDTO
     */
    @Override
    public void create(PhysicalProfileInfoDTO physicalProfileInfoDTO) {
        PhysicalProfileInfoDO record = new PhysicalProfileInfoDO();
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setProfileId(physicalProfileInfoDTO.getProfileId());
        record.setProfileCode(physicalProfileInfoDTO.getProfileCode());
        record.setProfileName(physicalProfileInfoDTO.getProfileName());
        record.setPrimaryKey(physicalProfileInfoDTO.getPrimaryKey());
        record.setPhysicalProfileCode(physicalProfileInfoDTO.getPhysicalProfileCode());
        record.setPhysicalProfileName(physicalProfileInfoDTO.getPhysicalProfileName());
        record.setType(physicalProfileInfoDTO.getType().getCode());
        record.setStatus(PhysicalProfileStatus.REFLUX_PREPARATION.getCode());
        record.setOdpsProject(physicalProfileInfoDTO.getOdpsProject()==null ? "null" : physicalProfileInfoDTO.getOdpsProject());
        record.setOdpsTable(physicalProfileInfoDTO.getOdpsTable()==null? "null" : physicalProfileInfoDTO.getOdpsTable());
        record.setOdpsTableLifecycle(physicalProfileInfoDTO.getOdpsTableLifecycle());
        record.setOdpsTableComment(physicalProfileInfoDTO.getOdpsTableComment());
        record.setOdpsTablePtKey(physicalProfileInfoDTO.getOdpsTablePtKey());
        record.setOlapTable(physicalProfileInfoDTO.getOlapTable()==null ? "null" : physicalProfileInfoDTO.getOlapTable());
        record.setOdpsMasterConfig(JSON.toJSONString(physicalProfileInfoDTO.getOdpsMasterConfig()));
        record.setCreator(physicalProfileInfoDTO.getCreator());
        record.setModifier(physicalProfileInfoDTO.getModifier());
        record.setDeleted((byte) 0);
        record.setOdpsTableUpdateTime(new Date());
        record.setOlapTableUpdateTime(new Date());
        record.setKvstoreUpdateTime(new Date());
        record.setPhysicalProfileScene(physicalProfileInfoDTO.getPhysicalProfileSceneConfigList()==null ?
                null : JSON.toJSONString(physicalProfileInfoDTO.getPhysicalProfileSceneConfigList()));
        physicalProfileInfoDAO.insert(record);
    }

    @Override
    public int deleteLogicById(Long id) {
        return physicalProfileInfoDAO.deleteLogicById(id);
    }

    /**
     * @return 物理画像表和其包含标签(已排序)
     */
    @Override
    public Map<PhysicalProfileInfoDTO, List<LabelInfoDTO>> queryRefluxProfileAndLabels() {
        List<PhysicalProfileInfoDTO> profiles = queryAll();
        Map<PhysicalProfileInfoDTO, List<LabelInfoDTO>> map = Maps.newConcurrentMap();

        if (CollectionUtils.isEmpty(profiles)) {
            return null;
        }

        profiles
                .parallelStream()
                .forEach(
                        profile -> {
                            List<LabelInfoDTO> labels = labelInfoService.queryRefluxLabelsByProfile(
                                    profile.getPhysicalProfileCode());
                            labels = labelOrderService.reSort(labels);
                            if (CollectionUtils.isNotEmpty(labels)) {
                                map.put(profile, labels);
                            }
                        }
                );

        return map;
    }

    /**
     * @return 物理画像表和其包含标签(已排序)
     */
    @Override
    public Map<PhysicalProfileInfoDTO, List<LabelInfoDTO>> queryRefluxProfileAndLabels(List<String> profileCodes) {
        List<PhysicalProfileInfoDTO> profiles = queryByCodes(profileCodes);

        Map<PhysicalProfileInfoDTO, List<LabelInfoDTO>> map = Maps.newConcurrentMap();

        if (CollectionUtils.isEmpty(profiles)) {
            return null;
        }

        profiles
                .parallelStream()
                .forEach(
                        profile -> {
                            List<LabelInfoDTO> labels = labelInfoService.queryRefluxLabelsByProfile(
                                    profile.getPhysicalProfileCode());
                            labels = labelOrderService.reSort(labels);
                            if (CollectionUtils.isNotEmpty(labels)) {
                                map.put(profile, labels);
                            }
                        }
                );

        return map;
    }

    @Override
    public List<PhysicalProfileInfoDTO> queryAll() {
        PhysicalProfileInfoParameter param = new PhysicalProfileInfoParameter();
        param.setType(PhysicalProfileType.OFFLINE);
        param.setDeleted(DeletedEnum.fromBoolean(false));

        return list(param);
    }

    @Override
    public List<PhysicalProfileInfoDTO> queryByCodes(List<String> profileCodes) {
        PhysicalProfileInfoParam physicalProfileInfoParam = new PhysicalProfileInfoParam();
        Criteria criteria = physicalProfileInfoParam.createCriteria();
        criteria.andTypeEqualTo(PhysicalProfileType.OFFLINE.getCode());
        criteria.andDeletedEqualTo(DeletedEnum.fromBoolean(false));
        criteria.andPhysicalProfileCodeIn(profileCodes);

        return selectByParamWithBLOBs(physicalProfileInfoParam);
    }

    @Override
    public Integer updateSelectiveById(Long id, PhysicalProfileInfoDTO updatePhysicalProfileInfoDTO) {
        PhysicalProfileInfoParam param = new PhysicalProfileInfoParam();
        Criteria criteria = param.createCriteria();
        criteria.andIdEqualTo(id);

        PhysicalProfileInfoDO physicalProfileInfoDO = physicalProfileInfoConverter.convertFromDTO(
                updatePhysicalProfileInfoDTO);
        physicalProfileInfoDO.setGmtModified(new Date());

        return physicalProfileInfoDAO.updateByParamSelective(physicalProfileInfoDO, param);
    }

    @Override
    public Integer updateStatus(Long id, PhysicalProfileStatus status) {
        PhysicalProfileInfoDTO physicalProfileInfoDTO = new PhysicalProfileInfoDTO();
        // 更新odps表更新时间
        if (PhysicalProfileStatus.REFLUXED.equals(status)) {
            physicalProfileInfoDTO.setOdpsTableUpdateTime(new Date());
        }
        physicalProfileInfoDTO.setStatus(status);
        return updateSelectiveById(id, physicalProfileInfoDTO);
    }

    @Override
    public Map<String, List<PhysicalProfileInfoDTO>> queryRefluxLabelsAndProfileInfo(List<LabelInfoDTO> labelInfoDTOList) {
        Map<String, List<PhysicalProfileInfoDTO>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(labelInfoDTOList)) {
            return result;
        }

        List<String> physicalProfileCodes = labelInfoDTOList.stream().map(LabelInfoDTO::getPhysicalProfileCode).collect(Collectors.toList());
        PhysicalProfileInfoParam physicalProfileInfoParam = new PhysicalProfileInfoParam();
        Criteria criteria = physicalProfileInfoParam.createCriteria();
        criteria.andDeletedEqualTo(DeletedEnum.fromBoolean(false));
        criteria.andPhysicalProfileCodeIn(physicalProfileCodes);
        List<PhysicalProfileInfoDTO> profiles = selectByParamWithBLOBs(physicalProfileInfoParam);
        if (CollectionUtils.isEmpty(profiles)) {
            return result;
        }

        Map<String, List<PhysicalProfileInfoDTO>> physicalProfileInfoMap = profiles.stream().collect(Collectors.groupingBy(PhysicalProfileInfoDTO::getPhysicalProfileCode));
        for (LabelInfoDTO labelInfoDTO : labelInfoDTOList) {
            String code = labelInfoDTO.getCode();
            String profileCode = labelInfoDTO.getPhysicalProfileCode();
            result.put(code, physicalProfileInfoMap.get(profileCode));
        }
        return result;
    }

    @Override
    public List<ProfileEnumType> queryProfileEnumTypes(String profileCode) {
        PhysicalProfileInfoParameter param = new PhysicalProfileInfoParameter();
        param.setDeleted((byte) 0);
        if (StringUtils.isNotBlank(profileCode)) {
            param.setProfileCode(profileCode);
        }
        List<PhysicalProfileInfoDTO> profileInfoDTOList = list(param);

        return profileInfoDTOList.stream().map(ProfileEnumType::convertToProfileEnumType).collect(Collectors.toList());
    }
}