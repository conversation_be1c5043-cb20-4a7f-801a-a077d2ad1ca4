package com.fliggy.picasso.service.enumvalue;

import com.fliggy.picasso.common.domain.label.enumvalue.EnumDimDataDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@Service
public class EnumDimDataConverter {

    /**
     * DTO模型转换成DO模型
     * @param enumDimDataDTO
     */
    public EnumDimDataDO convertFromDTO(EnumDimDataDTO enumDimDataDTO) {
        EnumDimDataDO enumDimDataDO = new EnumDimDataDO();
        BeanUtils.copyProperties(enumDimDataDTO,enumDimDataDO);
        return enumDimDataDO;
    }

    /**
     * DO模型转换成DTO模型
     * @param enumDimDataDO
     */
    public EnumDimDataDTO convertFromDO(EnumDimDataDO enumDimDataDO) {
        EnumDimDataDTO enumDimDataDTO = new EnumDimDataDTO();
        BeanUtils.copyProperties(enumDimDataDO,enumDimDataDTO);
        return enumDimDataDTO;
    }
}