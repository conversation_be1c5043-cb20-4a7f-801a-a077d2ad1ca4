package com.fliggy.picasso.service.handler.impl;

import com.fliggy.picasso.common.enums.label.LabelClassifyTabEnum;
import com.fliggy.picasso.common.enums.label.LabelOpenScopeEnum;
import com.fliggy.picasso.domain.LabelInfoHoloDO;
import com.fliggy.picasso.entity.bo.LabelInfoFrontBO;
import com.fliggy.picasso.entity.bo.LabelMarketSearchInfoBO;
import com.fliggy.picasso.entity.param.LabelMarketSearchParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class AuthorizedLabelMarketSearchHandler extends AbstractLabelMarketSearchHandler {

    @Override
    public LabelClassifyTabEnum getLabelClassifyTab() {
        return LabelClassifyTabEnum.AUTHORIZED;
    }

    @Override
    public LabelMarketSearchInfoBO process(LabelMarketSearchParam param) {
        if (Objects.isNull(param)) {
            return null;
        }

        // 搜索排序
        List<LabelInfoHoloDO> labels = queryLabelList(param);

        // 计数
        LabelMarketSearchInfoBO result = new LabelMarketSearchInfoBO();
        Map<String, Boolean> labelPermissions = checkLabelPermission(labels, param.getBucId());
        Map<String, Integer> labelCollectInfo = checkLabelCollected(labels, param.getEmpId());
        Map<LabelClassifyTabEnum, Long> tabCntMap = getTabCntMap(labels, labelPermissions, labelCollectInfo, param.getEmpId());
        result.setTabCntMap(tabCntMap);

        // 过滤没有使用权限的标签
        List<LabelInfoHoloDO> finalLabels = null;
        if (permissionService.isSuperAdmin()) {
            finalLabels = labels;
        } else {
            finalLabels = labels.stream().filter(labelInfoHoloDO ->  {
                // 公开标签
                if (Objects.isNull(labelInfoHoloDO.getOpenScope()) || Objects.equals(labelInfoHoloDO.getOpenScope(), LabelOpenScopeEnum.PUBLIC.getCode())) {
                    // 不需要鉴权
                    if (Objects.isNull(labelInfoHoloDO.getSecurityLevel()) || labelInfoHoloDO.getSecurityLevel()<=1) {
                        return true;
                    }
                    // 需要鉴权，且已授权的标签
                    if (Objects.nonNull(labelInfoHoloDO.getSecurityLevel()) && labelInfoHoloDO.getSecurityLevel()>1
                            && labelPermissions.containsKey(labelInfoHoloDO.getCode()) && labelPermissions.get(labelInfoHoloDO.getCode())) {
                        return true;
                    }
                    return false;
                }
                // 私有标签，标签负责人/管理员/分享者可见
                if (Objects.equals(labelInfoHoloDO.getOpenScope(), LabelOpenScopeEnum.PRIVATE.getCode()) && StringUtils.isNotBlank(param.getEmpId())) {
                    String dataOwner = labelInfoHoloDO.getDataOwner();
                    if (StringUtils.isNotBlank(dataOwner) && dataOwner.contains(param.getEmpId())) {
                        return true;
                    }
                    String managers = labelInfoHoloDO.getManagers();
                    if (StringUtils.isNotBlank(managers) && managers.contains(param.getEmpId())) {
                        return true;
                    }
                    String sharers = labelInfoHoloDO.getSharers();
                    if (StringUtils.isNotBlank(sharers) && sharers.contains(param.getEmpId())) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
        }

        // 分页，补数据
        List<LabelInfoHoloDO> pageLabels = pageLabels(finalLabels, param.getPageNo(), param.getPageSize());
        List<LabelInfoFrontBO> labelInfoFrontBOList = convertToBO(pageLabels);
        addUpExtraInfo(labelInfoFrontBOList, labelPermissions, labelCollectInfo);
        result.setLabelInfoList(labelInfoFrontBOList);
        return result;
    }

}
