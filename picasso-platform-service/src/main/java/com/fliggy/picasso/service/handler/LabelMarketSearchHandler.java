package com.fliggy.picasso.service.handler;

import com.fliggy.picasso.common.enums.label.LabelClassifyTabEnum;
import com.fliggy.picasso.entity.bo.LabelMarketSearchInfoBO;
import com.fliggy.picasso.entity.param.LabelMarketSearchParam;

import java.util.List;

public interface LabelMarketSearchHandler {

    /**
     * 标签tab
     * @return
     */
    LabelClassifyTabEnum getLabelClassifyTab();

    /**
     * 处理搜索
     * @param param
     * @return
     */
    LabelMarketSearchInfoBO process(LabelMarketSearchParam param);
}
