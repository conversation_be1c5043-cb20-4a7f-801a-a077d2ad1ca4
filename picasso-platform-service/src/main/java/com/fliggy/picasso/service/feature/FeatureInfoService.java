package com.fliggy.picasso.service.feature;

import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public interface FeatureInfoService {
    /**
     * 根据参数统计总数
     * @param param
     */
    long count(FeatureInfoParameter param);

    /**
     * 根据参数查询
     * @param param
     */
    FeatureInfoDTO find(FeatureInfoParameter param);

    /**
     * 列表查询
     * @param param
     */
    List<FeatureInfoDTO> list(FeatureInfoParameter param);

    /**
     * 创建
     * @param param
     */
    void create(FeatureInfoParameter param);

    /**
     * 选择性修改
     * @param dto
     * @param param
     */
    void updateSelective(FeatureInfoDTO dto, FeatureInfoParameter param);
}