package com.fliggy.picasso.service.lindorm;

import com.alibaba.lindorm.client.exception.LindormException;
import com.aliyun.odps.OdpsException;
import com.fliggy.picasso.common.enums.crowd.CrowdIdMappingTaskStatusEnum;
import com.fliggy.picasso.service.lindorm.domain.OneIdMappingDO;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface OneIdMappingService {

    /**
     * 批量插入数据
     * @param records
     * @return
     */
    int batchUpsert(List<OneIdMappingDO> records);

    /**
     * 删除数据
     * @param record
     * @return
     */
    int delete(OneIdMappingDO record);

    /**
     * 批量删除数据
     * @param records
     * @return
     */
    int batchDelete(List<OneIdMappingDO> records);

    /**
     * id mapping one id
     * @param sourceIdType
     * @param sourceId
     * @return
     */
    Set<String> idMappingToOneId(String sourceIdType, String sourceId) throws LindormException;

    /**
     * one id mapping id
     * @param oneId
     * @param targetIdType
     * @return
     */
    String idMappingFromOneId(String oneId, String targetIdType) throws LindormException;

    /**
     * id mapping
     * @param sourceIdType
     * @param sourceId
     * @param targetIdType
     * @return
     */
    Set<String> idMapping(String sourceIdType, String sourceId, String targetIdType) throws LindormException;

    /**
     * 离线批量id mapping
     * @param crowdId 人群id
     * @Return
     */
    Pair<String, String> batchIdMapping(String crowdId) throws OdpsException;

    /**
     * 查询id mapping任务状态
     * @param instanceId
     * @return
     */
    CrowdIdMappingTaskStatusEnum queryIdMappingTaskStatus(String instanceId);

}
