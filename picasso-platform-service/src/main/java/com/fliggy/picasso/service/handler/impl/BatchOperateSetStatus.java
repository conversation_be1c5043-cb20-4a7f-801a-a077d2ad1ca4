package com.fliggy.picasso.service.handler.impl;

import com.fliggy.picasso.client.entity.enums.LabelStatus;
import com.fliggy.picasso.common.constants.CrowdLabelBatchOperateActionEnum;
import com.fliggy.picasso.common.domain.label.CrowdLabelBatchOperateDO;
import com.fliggy.picasso.common.enums.label.LabelStatusEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.service.handler.BatchOperateHandler;
import com.fliggy.picasso.service.handler.BatchOperateHandlerResolver;
import com.fliggy.picasso.service.label.LabelInfoService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BatchOperateSetStatus implements BatchOperateHandler, InitializingBean {


    private final BatchOperateHandlerResolver batchOperateHandlerResolver;
    private final LabelInfoService labelInfoService;

    @Autowired
    public BatchOperateSetStatus(BatchOperateHandlerResolver resolver, LabelInfoService labelInfoService) {
        this.batchOperateHandlerResolver = resolver;
        this.labelInfoService = labelInfoService;
    }

    @Override
    public boolean process(CrowdLabelBatchOperateDO crowdLabelBatchOperateDO) {
        if (crowdLabelBatchOperateDO == null) {
            throw new ParamErrorException("[BatchOperateSetStatus]批量操作错误，crowdLabelBatchOperateDO为null。");
        }

        List<Long> ids = crowdLabelBatchOperateDO.getLabelIdList();
        CrowdLabelBatchOperateActionEnum action = crowdLabelBatchOperateDO.getBatchOperateAction();

        switch (action) {
            case BATCH_SET_ACTIVATE:
                return labelInfoService.batchUpdateStatus(ids, LabelStatus.convertFromStatus(LabelStatusEnum.ACTIVATE).getCode());
            case BATCH_SET_OFFLINE:
                return labelInfoService.batchUpdateStatus(ids, LabelStatus.convertFromStatus(LabelStatusEnum.OFFLINE).getCode());
            case BATCH_SET_WAIT_OFFLINE:
                return labelInfoService.batchUpdateStatus(ids, LabelStatus.convertFromStatus(LabelStatusEnum.WAIT_OFFLINE).getCode());
            case BATCH_SET_WAIT_ONLINE:
                return labelInfoService.batchUpdateStatus(ids, LabelStatus.convertFromStatus(LabelStatusEnum.WAIT_ONLINE).getCode());
            default:
                return false;
        }
    }

    @Override
    public void afterPropertiesSet() {
        batchOperateHandlerResolver.register(CrowdLabelBatchOperateActionEnum.BATCH_SET_ACTIVATE, this);
        batchOperateHandlerResolver.register(CrowdLabelBatchOperateActionEnum.BATCH_SET_OFFLINE, this);
        batchOperateHandlerResolver.register(CrowdLabelBatchOperateActionEnum.BATCH_SET_WAIT_OFFLINE, this);
        batchOperateHandlerResolver.register(CrowdLabelBatchOperateActionEnum.BATCH_SET_WAIT_ONLINE, this);
    }
}
