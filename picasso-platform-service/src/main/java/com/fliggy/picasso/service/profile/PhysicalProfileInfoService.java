package com.fliggy.picasso.service.profile;

import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO.PhysicalProfileStatus;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
public interface PhysicalProfileInfoService {

    /**
     * 根据参数统计总数
     *
     * @param param
     */
    long count(PhysicalProfileInfoParameter param);

    /**
     * 根据参数查询
     *
     * @param param
     */
    PhysicalProfileInfoDTO find(PhysicalProfileInfoParameter param);

    /**
     * 根据Code查询
     *
     * @param physicalProfileCode
     * @return
     */
    PhysicalProfileInfoDTO findByCode(String physicalProfileCode);

    /**
     * 列表查询
     *
     * @param param
     */
    List<PhysicalProfileInfoDTO> list(PhysicalProfileInfoParameter param);

    /**
     * 根据传入ID列表做列表查询
     *
     * @param ids
     * @return
     */
    List<PhysicalProfileInfoDTO> list(Set<Long> ids);

    /**
     * 创建
     *
     * @param physicalProfileInfoDTO
     */
    void create(PhysicalProfileInfoDTO physicalProfileInfoDTO);

    /**
     * 逻辑删除
     * @param id
     * @return
     */
    int deleteLogicById(Long id);

    /**
     * 查询待回流的物理画像以及标签
     *
     * @return
     */
    Map<PhysicalProfileInfoDTO, List<LabelInfoDTO>> queryRefluxProfileAndLabels();

    /**
     * 查询待回流的物理画像以及标签
     *
     * @param profileCodes
     * @return
     */
    Map<PhysicalProfileInfoDTO, List<LabelInfoDTO>> queryRefluxProfileAndLabels(List<String> profileCodes);

    /**
     * 查询所有物理画像
     * @return
     */
    List<PhysicalProfileInfoDTO> queryAll();

    /**
     * 根据Code列表查询
     * @param profileCodes
     * @return
     */
    List<PhysicalProfileInfoDTO> queryByCodes(List<String> profileCodes);

    /**
     * 根据主键选择性更新
     *
     * @param id
     * @param updatePhysicalProfileInfoDTO
     * @return
     */
    Integer updateSelectiveById(Long id, PhysicalProfileInfoDTO updatePhysicalProfileInfoDTO);

    /**
     * 根据ID更新物理画像状态
     *
     * @param id     物理画像ID
     * @param status 物理画像状态
     * @return
     */
    Integer updateStatus(Long id, PhysicalProfileStatus status);

    /**
     * @param labelInfoDTOList
     * @return
     */
    Map<String, List<PhysicalProfileInfoDTO>> queryRefluxLabelsAndProfileInfo(List<LabelInfoDTO> labelInfoDTOList);

    /**
     * 查询物理画像枚举类型
     * @return
     */
    List<ProfileEnumType> queryProfileEnumTypes(String profileCode);
}