package com.fliggy.picasso.service.analysis;

import java.util.Map;

import com.google.common.collect.Maps;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/3/25 下午5:09
 */
public enum AnalysisStatus {

    /**
     * 分析状态
     */
    INIT("INIT"),

    ;
    @Getter
    private String name;

    AnalysisStatus(String name) {
        this.name = name;
    }

    private static final Map<String, AnalysisStatus> CODE_MAP = Maps.newHashMap();

    static {
        for (AnalysisStatus value : values()) {
            CODE_MAP.put(value.getName(), value);
        }
    }

    public static AnalysisStatus fromCode(String code) {
        return CODE_MAP.getOrDefault(code, null);
    }
}
