package com.fliggy.picasso.service.profile;

import com.ali.unit.rule.util.lang.CollectionUtils;
import com.alibaba.fastjson.JSON;

import com.fliggy.picasso.common.domain.profile.PhysicalProfileSceneConfig;
import com.fliggy.picasso.dao.PhysicalProfileInfoDO;
import com.fliggy.picasso.entity.odps.OdpsMasterConfig;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO.PhysicalProfileStatus;
import com.fliggy.picasso.service.profile.PhysicalProfileInfoDTO.PhysicalProfileType;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class PhysicalProfileInfoConverter {

    /**
     * DTO模型转换成DO模型
     *
     * @param physicalProfileInfoDTO
     */
    public PhysicalProfileInfoDO convertFromDTO(PhysicalProfileInfoDTO physicalProfileInfoDTO) {
        PhysicalProfileInfoDO physicalProfileInfoDO = new PhysicalProfileInfoDO();
        BeanUtils.copyProperties(physicalProfileInfoDTO, physicalProfileInfoDO);

        if (physicalProfileInfoDTO.getType() != null) {
            physicalProfileInfoDO.setType(physicalProfileInfoDTO.getType().getCode());
        }

        if (physicalProfileInfoDTO.getStatus() != null) {
            physicalProfileInfoDO.setStatus(physicalProfileInfoDTO.getStatus().getCode());
        }

        if (physicalProfileInfoDTO.getOdpsMasterConfig() != null) {
            String config = JSON.toJSONString(physicalProfileInfoDTO.getOdpsMasterConfig());
            physicalProfileInfoDO.setOdpsMasterConfig(config);
        }

        if (CollectionUtils.isNotEmpty(physicalProfileInfoDTO.getPhysicalProfileSceneConfigList())){
            String sceneConfigStr = JSON.toJSONString(physicalProfileInfoDTO.getPhysicalProfileSceneConfigList());
            physicalProfileInfoDO.setPhysicalProfileScene(sceneConfigStr);
        }

        return physicalProfileInfoDO;
    }

    /**
     * DO模型转换成DTO模型
     *
     * @param physicalProfileInfoDO
     */
    public PhysicalProfileInfoDTO convertFromDO(PhysicalProfileInfoDO physicalProfileInfoDO) {
        PhysicalProfileInfoDTO physicalProfileInfoDTO = new PhysicalProfileInfoDTO();
        BeanUtils.copyProperties(physicalProfileInfoDO, physicalProfileInfoDTO);

        if (physicalProfileInfoDO.getStatus() != null) {
            physicalProfileInfoDTO.setStatus(PhysicalProfileStatus.fromCode(physicalProfileInfoDO.getStatus()));
        }

        if (physicalProfileInfoDO.getType() != null) {
            physicalProfileInfoDTO.setType(PhysicalProfileType.fromCode(physicalProfileInfoDO.getType()));
        }

        if (physicalProfileInfoDO.getOdpsMasterConfig() != null) {
            OdpsMasterConfig config = JSON.parseObject(physicalProfileInfoDO.getOdpsMasterConfig(), OdpsMasterConfig.class);
            physicalProfileInfoDTO.setOdpsMasterConfig(config);
        }
        if (physicalProfileInfoDO.getPhysicalProfileScene() != null){
            List<PhysicalProfileSceneConfig> physicalProfileSceneConfigs = JSON.parseArray(physicalProfileInfoDO.getPhysicalProfileScene(), PhysicalProfileSceneConfig.class);
            physicalProfileInfoDTO.setPhysicalProfileSceneConfigList(physicalProfileSceneConfigs);
        }

        return physicalProfileInfoDTO;
    }
}