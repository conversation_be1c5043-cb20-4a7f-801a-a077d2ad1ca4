package com.fliggy.picasso.service.lindorm.impl;

import com.alibaba.lindorm.client.exception.LindormException;
import com.aliyun.odps.Instance;
import com.aliyun.odps.OdpsException;
import com.fliggy.picasso.common.enums.crowd.CrowdIdMappingTaskStatusEnum;
import com.fliggy.picasso.offline.OdpsService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Set;

import static com.fliggy.picasso.common.Constant.ODPS_PROJECT_TRIP_PROFILE;
import static com.fliggy.picasso.common.constants.DateConstants.DATE_FORMAT_YYYYMMDD;
import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.CROWD_TABLE_ALL_SNAPSHOT;
import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.ODPS_ID_MAPPING_TABLE_NAME;
import static com.fliggy.picasso.offline.OdpsService.LOW_PRIORITY;
import static com.fliggy.picasso.service.lindorm.column.OneIdKeyTypeEnum.SCRM_ONE_ID;
import static com.fliggy.picasso.service.lindorm.column.OneIdMappingColumnEnum.ONE_ID_REVERSE;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Service
public class ScrmOneIdMappingServiceImpl extends AbstractOneIdMappingServiceImpl {

    @Value("${lindorm.scrm.mapping.tableName}")
    private String tableName;

    public ScrmOneIdMappingServiceImpl() {
        super.setOneIdReverseLindormColumn(ONE_ID_REVERSE.getCode());
    }

    @Override
    public Set<String> idMappingToOneId(String sourceIdType, String sourceId) throws LindormException {
        super.setTableName(tableName);
        return super.idMappingToOneId(sourceIdType, sourceId);
    }

    @Override
    public String idMappingFromOneId(String oneId, String targetIdType) throws LindormException {
        super.setTableName(tableName);
        return super.idMappingFromOneId(oneId, targetIdType);
    }

    @Override
    public Set<String> idMapping(String sourceIdType, String sourceId, String targetIdType) throws LindormException {
        super.setTableName(tableName);
        return super.idMapping(sourceIdType, sourceId, targetIdType);
    }

    @Override
    public Pair<String, String> batchIdMapping(String taobaoCrowdId) throws OdpsException {
        String sql = generateCreateSql(taobaoCrowdId) + generateLoadSql(taobaoCrowdId);
        return odpsService.asyncExecute(sql, generateTaskName(taobaoCrowdId), LOW_PRIORITY);
    }

    @Override
    public CrowdIdMappingTaskStatusEnum queryIdMappingTaskStatus(String instanceId) {
        Instance.TaskStatus.Status status = odpsService.checkInstanceStatus(instanceId);
        switch (status) {
            case SUCCESS:
                return CrowdIdMappingTaskStatusEnum.SUCCESS;
            case FAILED:
            case SUSPENDED:
                return CrowdIdMappingTaskStatusEnum.FAILED;
            default:
                return CrowdIdMappingTaskStatusEnum.RUNNING;
        }
    }

    private String generateTaskName(String taobaoCrowdId) {
        return "scrm_one_id_mapping_task_" + taobaoCrowdId;
    }

    private String generateTableName(String taobaoCrowdId) {
        return String.format(ODPS_ID_MAPPING_TABLE_NAME, SCRM_ONE_ID.getCode(), taobaoCrowdId);
    }

    private String generateCreateSql(String taobaoCrowdId) {
        String format = "CREATE TABLE IF NOT EXISTS %s ( id STRING ) PARTITIONED BY ( ds STRING ) LIFECYCLE 30;";
        return String.format(format, generateTableName(taobaoCrowdId));
    }

    private String generateLoadSql(String taobaoCrowdId) {
        String bizDate = DateTime.now().plusDays(-1).toString(DATE_FORMAT_YYYYMMDD);
        String format = "INSERT OVERWRITE TABLE %s PARTITION (ds = '%s')"
                + "SELECT scrm_one_id FROM (SELECT user_id,scrm_one_id FROM trip_profile.trip_scrm_one_id WHERE ds = MAX_PT('trip_profile.trip_scrm_one_id')) a " +
                "JOIN (SELECT user_id FROM %s.%s WHERE group_id='%s') b ON a.user_id=b.user_id;";
        return String.format(format, generateTableName(taobaoCrowdId), bizDate, ODPS_PROJECT_TRIP_PROFILE, CROWD_TABLE_ALL_SNAPSHOT, taobaoCrowdId);
    }
}
