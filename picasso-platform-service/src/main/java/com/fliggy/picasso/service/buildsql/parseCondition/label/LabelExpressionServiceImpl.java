package com.fliggy.picasso.service.buildsql.parseCondition.label;

import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.common.enums.label.LabelOperatorEnum;
import com.fliggy.picasso.entity.condition.GroupConditionDTO;
import com.fliggy.picasso.entity.condition.TableConditionDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.fliggy.picasso.common.Constant.BLANK;

/**
 * 生成标签相关表达式的接口实现类
 */
@Component
public class LabelExpressionServiceImpl implements LabelExpressionService {
    @Autowired
    ExpressionGeneratorResolver expressionGeneratorResolver;

    @Override
    public Map<String, String> generateLabelExpression(LabelCrowdConditions labelCrowdConditions, List<GroupConditionDTO> groupConditionDTOS) {
        Map<String, String> result = new HashMap<>(16);
        //获取标签标识对应的物理画像相关信息
        List<LabelCrowdConditions.LabelGroup> group = labelCrowdConditions.getGroup();
        for (int i = 0; i < group.size(); i++) {
            LabelCrowdConditions.LabelGroup labelGroup = group.get(i);
            String expression = labelGroup.getExpression();
            //获取group内的连接符号(and、or、notin)
            List<LabelOperatorEnum> labelOperatorTyps = getLabelOperatorTyps(expression);
            List<LabelCrowdConditions.LabelValue> labels = labelGroup.getLabel();
            GroupConditionDTO groupConditionDTO = groupConditionDTOS.get(i);
            Map<String, TableConditionDTO> labelConditionMap = groupConditionDTO.getLabelConditionMap();
            List<TableConditionDTO> tableConditions = groupConditionDTO.getTableConditions();
            for (int j = 0; j < labels.size(); j++) {
                LabelCrowdConditions.LabelValue labelValue = labels.get(j);
                String name = labelValue.getName();
                TableConditionDTO tableConditionDTO = labelConditionMap.get(name + i);
                if (tableConditionDTO == null) {
                    throw new RuntimeException(name + "对应的表名查询不到！");
                }
                String olapTable;
                if (tableConditions.size() <= 1) {
                    olapTable = tableConditionDTO.getTableName();
                } else {
                    olapTable = tableConditionDTO.getAlias();
                }
                //拼接标签对应表的字段
                String labelName = olapTable + "." + name + BLANK + i;
                BaseLabelExpressionGenerator labelExpressionGenerator = expressionGeneratorResolver.resolve(labelValue.getType());
                if (labelExpressionGenerator == null) {
                    throw new RuntimeException("没有" + labelValue.getType().getDesc() + "标签类型");
                }
                Map<String, String> stringStringMap = labelExpressionGenerator.generateExpression(labelName, labelValue.getValue(), labelOperatorTyps.get(j));
                result.putAll(stringStringMap);
            }
        }
        return result;
    }

    private List<LabelOperatorEnum> getLabelOperatorTyps(String expression) {
        List<LabelOperatorEnum> operator = new LinkedList<>();
        operator.add(LabelOperatorEnum.NONE);

        String[] ops = expression.split(BLANK);
        for (String op : ops) {
            if ("AND".equalsIgnoreCase(op)) {
                operator.add(LabelOperatorEnum.AND);
            } else if ("OR".equalsIgnoreCase(op)) {
                operator.add(LabelOperatorEnum.OR);
            } else if ("NOTIN".equalsIgnoreCase(op)) {
                operator.add(LabelOperatorEnum.NOTIN);
            }
        }
        return operator;
    }
}
