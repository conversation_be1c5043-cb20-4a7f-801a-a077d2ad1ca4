package com.fliggy.picasso.service.enumvalue;

import com.fliggy.picasso.common.domain.label.enumvalue.EnumDimDataDO;
import com.fliggy.picasso.common.domain.label.enumvalue.LabelEnumValueAggregateByMetaId;
import com.fliggy.picasso.common.domain.label.enumvalue.LabelEnumValueFlat;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.dao.EnumDimDataParam;
import com.fliggy.picasso.dao.EnumDimDataParam.Criteria;
import com.fliggy.picasso.mapper.picasso.EnumDimDataDAO;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class EnumDimDataServiceImpl implements EnumDimDataService {

    @Autowired
    private EnumDimDataDAO enumDimDataDAO;

    @Autowired
    private EnumDimDataConverter enumDimDataConverter;

    /**
     * 根据参数统计总数
     *
     * @param param
     */
    @Override
    public long count(EnumDimDataParameter param) {
        EnumDimDataParam enumDimDataParam = new EnumDimDataParam();
        Criteria criteria = enumDimDataParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        return enumDimDataDAO.countByParam(enumDimDataParam);
    }

    /**
     * 根据DimMetaId&EnumCode查询标签枚举值（唯一）
     *
     * @param dimMetaId
     * @param enumCode
     * @return
     */
    @Override
    public EnumDimDataDTO findByEnumCode(Long dimMetaId, String enumCode) {
        if (dimMetaId == null || enumCode == null) {
            throw new RuntimeException("DimMetaId Or EnumCode Can't Be NULL");
        }

        EnumDimDataParam enumDimDataParam = new EnumDimDataParam();
        Criteria criteria = enumDimDataParam.createCriteria();
        criteria.andDimMetaIdEqualTo(dimMetaId);
        criteria.andEnumCodeEqualTo(enumCode);

        List<EnumDimDataDO> list = enumDimDataDAO.selectByParam(enumDimDataParam);
        if (null == list || list.isEmpty()) {
            return null;
        }

        return enumDimDataConverter.convertFromDO(list.get(0));
    }

    /**
     * 根据参数查询
     *
     * @param param
     */
    @Override
    public EnumDimDataDTO find(EnumDimDataParameter param) {
        EnumDimDataParam enumDimDataParam = new EnumDimDataParam();
        Criteria criteria = enumDimDataParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        List<EnumDimDataDO> list = enumDimDataDAO.selectByParam(enumDimDataParam);
        if (null == list || list.isEmpty()) {
            return null;
        }
        return enumDimDataConverter.convertFromDO(list.get(0));
    }

    /**
     * 列表查询
     *
     * @param param
     */
    @Override
    public List<EnumDimDataDTO> list(EnumDimDataParameter param) {
        EnumDimDataParam enumDimDataParam = new EnumDimDataParam();
        Criteria criteria = enumDimDataParam.createCriteria();

        if (param.getEnumCode() != null) {
            criteria.andEnumCodeEqualTo(param.getEnumCode());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getEnumCodes())) {
            criteria.andEnumCodeIn(param.getEnumCodes());
        }

        if (param.getDimMetaId() != null) {
            criteria.andDimMetaIdEqualTo(param.getDimMetaId());
        }

        if (param.getLeaf() != null) {
            criteria.andLeafEqualTo(param.getLeaf());
        }

        List<EnumDimDataDO> list = enumDimDataDAO.selectByParam(enumDimDataParam);
        if (null == list || list.isEmpty()) {
            return Lists.newArrayList();
        }
        List<EnumDimDataDTO> result = new ArrayList<>();
        for (EnumDimDataDO record : list) {
            EnumDimDataDTO enumDimDataDTO = enumDimDataConverter.convertFromDO(record);
            result.add(enumDimDataDTO);
        }
        return result;
    }

    /**
     * 列表查询
     *
     * @param dimMetaId
     */
    @Override
    public List<EnumDimDataDTO> listByDimMetaId(Long dimMetaId) {
        EnumDimDataParameter param = new EnumDimDataParameter();
        param.setDimMetaId(dimMetaId);
        return list(param);
    }

    /**
     * 修改
     *
     * @param dto
     * @param param
     */
    @Override
    public void updateSelective(EnumDimDataDTO dto, EnumDimDataParameter param) {
        EnumDimDataDO record = enumDimDataConverter.convertFromDTO(dto);
        record.setGmtModified(new Date());
        EnumDimDataParam enumDimDataParam = new EnumDimDataParam();
        Criteria criteria = enumDimDataParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        enumDimDataDAO.updateByParamSelective(record, enumDimDataParam);
    }

    @Override
    public EnumDimDataDO queryById(Long id) {
        return enumDimDataDAO.selectByPrimaryKey(id);
    }

    @Override
    public EnumDimDataDO queryByIndex(Long dimMetaId, Long enumId) {
        return enumDimDataDAO.queryByIndex(dimMetaId, enumId);
    }

    @Override
    public Long queryMaxEnumId(Long dimMetaId) {
        return enumDimDataDAO.queryMaxEnumId(dimMetaId);
    }

    @Override
    public List<LabelEnumValueAggregateByMetaId> queryEnumDataBizDs() {
        return enumDimDataDAO.queryEnumDataBizDs();
    }

    @Override
    public List<EnumDimDataDO> queryChild(Long dimMetaId, Long enumId) {
        return enumDimDataDAO.queryChild(dimMetaId, enumId);
    }

    @Override
    public List<EnumDimDataDO> queryAllByDimMetaId(Long id) {
        return enumDimDataDAO.queryAllByDimMetaId(id);
    }

    @Override
    public List<LabelEnumValueFlat> queryAllFlat(Long dimMetaId) {
        return enumDimDataDAO.queryAllFlat(dimMetaId);
    }

    @Override
    public Boolean update(EnumDimDataDO enumDimDataDO) {
        return enumDimDataDAO.updateByPrimaryKey(enumDimDataDO) > 0;
    }

    @Override
    public int updateByPrimaryKeySelective(EnumDimDataDO record) {
        return enumDimDataDAO.updateByPrimaryKeySelective(record);
    }

    @Override
    public Long insert(EnumDimDataDO enumDimDataDO) {
        return (long)enumDimDataDAO.insert(enumDimDataDO);
    }

    @Override
    public List<EnumDimDataDTO> queryPreviewData(Long dimMetaId) {
        if (Objects.isNull(dimMetaId)) {
            return Lists.newArrayList();
        }

        EnumDimDataParam enumDimDataParam = new EnumDimDataParam();
        Criteria criteria = enumDimDataParam.createCriteria();
        criteria.andDimMetaIdEqualTo(dimMetaId);
        criteria.andDeletedEqualTo((byte) 0);
        enumDimDataParam.setPage(true).setPageStart(0).setPageSize(3);
        List<EnumDimDataDO> enumDimDataDOList = enumDimDataDAO.selectByParam(enumDimDataParam);
        if (CollectionUtils.isNullOrEmpty(enumDimDataDOList)) {
            return Lists.newArrayList();
        }
        return enumDimDataDOList.stream().map(enumDimDataDO -> enumDimDataConverter.convertFromDO(enumDimDataDO)).collect(Collectors.toList());
    }
}