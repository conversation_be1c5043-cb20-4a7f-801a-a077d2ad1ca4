package com.fliggy.picasso.service.feature;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fliggy.picasso.dao.FeatureInfoDO;
import com.fliggy.picasso.dao.FeatureInfoParam;
import com.fliggy.picasso.dao.FeatureInfoParam.Criteria;
import com.fliggy.picasso.mapper.picasso.FeatureInfoDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class FeatureInfoServiceImpl implements FeatureInfoService {

    @Autowired
    private FeatureInfoDAO featureInfoDAO;

    @Autowired
    private FeatureInfoConverter featureInfoConverter;

    /**
     * 根据参数统计总数
     *
     * @param param
     */
    @Override
    public long count(FeatureInfoParameter param) {
        FeatureInfoParam featureInfoParam = new FeatureInfoParam();
        Criteria criteria = featureInfoParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        return featureInfoDAO.countByParam(featureInfoParam);
    }

    /**
     * 根据参数查询
     *
     * @param param
     */
    @Override
    public FeatureInfoDTO find(FeatureInfoParameter param) {
        FeatureInfoParam featureInfoParam = new FeatureInfoParam();
        Criteria criteria = featureInfoParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        List<FeatureInfoDO> list = featureInfoDAO.selectByParam(featureInfoParam);
        if (null == list || list.isEmpty()) {
            return null;
        }
        return featureInfoConverter.convertFromDO(list.get(0));
    }

    /**
     * 列表查询
     *
     * @param param
     */
    @Override
    public List<FeatureInfoDTO> list(FeatureInfoParameter param) {
        FeatureInfoParam featureInfoParam = new FeatureInfoParam();
        Criteria criteria = featureInfoParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        List<FeatureInfoDO> list = featureInfoDAO.selectByParam(featureInfoParam);
        if (null == list || list.isEmpty()) {
            return null;
        }
        List<FeatureInfoDTO> result = new ArrayList<>();
        for (FeatureInfoDO record : list) {
            FeatureInfoDTO featureInfoDTO = featureInfoConverter.convertFromDO(record);
            result.add(featureInfoDTO);
        }
        return result;
    }

    /**
     * 创建
     *
     * @param param
     */
    @Override
    public void create(FeatureInfoParameter param) {
        FeatureInfoDO record = new FeatureInfoDO();
        record.setId(param.getId());
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setCode(param.getCode());
        record.setName(param.getName());
        record.setDescription(param.getDescription());
        record.setLabelId(param.getLabelId());
        record.setCreator(param.getCreator());
        record.setModifier(param.getModifier());
        record.setDeleted(param.getDeleted());
        featureInfoDAO.insert(record);
    }

    /**
     * 修改
     *
     * @param dto
     * @param param
     */
    @Override
    public void updateSelective(FeatureInfoDTO dto, FeatureInfoParameter param) {
        FeatureInfoDO record = featureInfoConverter.convertFromDTO(dto);
        record.setGmtModified(new Date());
        FeatureInfoParam featureInfoParam = new FeatureInfoParam();
        Criteria criteria = featureInfoParam.createCriteria();
        //TODO 注意：需要根据业务实际情况自行编写WHERE条件
        featureInfoDAO.updateByParamSelective(record, featureInfoParam);
    }
}