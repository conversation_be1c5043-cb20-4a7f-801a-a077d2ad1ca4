package com.fliggy.picasso.service.profile;

import com.fliggy.picasso.dao.LogicProfileInfoDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@Service
public class LogicProfileInfoConverter {

    /**
     * DTO模型转换成DO模型
     * @param logicProfileInfoDTO
     */
    public LogicProfileInfoDO convertFromDTO(LogicProfileInfoDTO logicProfileInfoDTO) {
        LogicProfileInfoDO logicProfileInfoDO = new LogicProfileInfoDO();
        BeanUtils.copyProperties(logicProfileInfoDTO,logicProfileInfoDO);
        return logicProfileInfoDO;
    }

    /**
     * DO模型转换成DTO模型
     * @param logicProfileInfoDO
     */
    public LogicProfileInfoDTO convertFromDO(LogicProfileInfoDO logicProfileInfoDO) {
        LogicProfileInfoDTO logicProfileInfoDTO = new LogicProfileInfoDTO();
        BeanUtils.copyProperties(logicProfileInfoDO,logicProfileInfoDTO);
        return logicProfileInfoDTO;
    }
}