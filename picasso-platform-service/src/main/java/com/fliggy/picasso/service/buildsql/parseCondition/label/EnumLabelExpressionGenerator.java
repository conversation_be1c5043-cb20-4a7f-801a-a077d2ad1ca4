package com.fliggy.picasso.service.buildsql.parseCondition.label;

import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import com.fliggy.picasso.common.enums.label.LabelOperatorEnum;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.fliggy.picasso.common.Constant.*;
import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.*;

/**
 * 枚举类型标签表达式生成器
 */
@Component
public class EnumLabelExpressionGenerator extends BaseLabelExpressionGenerator {
    public EnumLabelExpressionGenerator(ExpressionGeneratorResolver expressionGeneratorResolver) {
        super(expressionGeneratorResolver);
    }

    @Override
    String buildExpression(String labelName, List<String> enumValues, LabelOperatorEnum operatorType) {
        StringBuilder expression = new StringBuilder();
        expression.append(labelName);
        String enumValue;
        if (operatorType == LabelOperatorEnum.NOTIN) {
            expression.append(NOT);
        }
        expression.append(IN).append(LEFT_PAREN);
        for (int i = 0; i < enumValues.size(); i++) {
            if (enumValues.get(i).split(BLANK).length == 2) {
                // 枚举值中不包含空格
                enumValue = enumValues.get(i).split(BLANK)[0];
            } else {
                enumValue = enumValues.get(i).substring(0, enumValues.get(i).length() / 2);
            }
            expression.append(SINGLE_QUOTE)
                    .append(enumValue)
                    .append(SINGLE_QUOTE);
            if (i < enumValues.size() - 1) {
                expression.append(COMMA);
            } else {
                expression.append(RIGHT_PAREN);
            }
        }
        // bug fix: 保持和标签组间排除的语义相同 label not in ('1') or label is null
        if (operatorType == LabelOperatorEnum.NOTIN) {
            expression.append(OR).append(labelName).append(" is null");
        }
        expression.append(RIGHT_PAREN);
        return expression.toString();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        expressionGeneratorResolver.register(LabelBizDataTypeEnum.ENUM, this);
    }
}
