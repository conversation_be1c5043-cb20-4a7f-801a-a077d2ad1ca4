package com.fliggy.picasso.service.crowd.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.common.domain.CrowdExportTaskDO;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoQuery;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoSearch;
import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.common.domain.crowd.ab.GroupBucketInfoDO;
import com.fliggy.picasso.common.domain.crowd.ab.GroupBucketInfoDO.ChildCrowd;
import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoBO;
import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoConfig;
import com.fliggy.picasso.common.domain.crowd.approval.ApprovalInfoVO;
import com.fliggy.picasso.common.domain.crowd.record.ChangeRecordData;
import com.fliggy.picasso.common.domain.crowd.record.PicassoChangeRecordBO;
import com.fliggy.picasso.common.enums.ChangeRecordSourceTypeEnum;
import com.fliggy.picasso.common.enums.LabelType;
import com.fliggy.picasso.common.enums.ProfileCodeEnum;
import com.fliggy.picasso.common.enums.bpms.ApprovalApplySceneEnum;
import com.fliggy.picasso.common.enums.crowd.CrowdCircleTypeEnum;
import com.fliggy.picasso.common.enums.crowd.CrowdClassifyTabEnum;
import com.fliggy.picasso.common.enums.crowd.CrowdTypeEnum;
import com.fliggy.picasso.common.enums.group.GroupApplySceneEnum;
import com.fliggy.picasso.common.enums.group.GroupStatusEnum;
import com.fliggy.picasso.common.enums.group.GroupTypeEnum;
import com.fliggy.picasso.common.enums.group.ProfileEnum;
import com.fliggy.picasso.common.enums.label.LabelStatusEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.config.switcher.CrowdSwitchConfig;
import com.fliggy.picasso.entity.bo.CrowdInfoBO;
import com.fliggy.picasso.entity.bo.CrowdSearchInfoBO;
import com.fliggy.picasso.entity.bo.LabelInfoBackgroundBO;
import com.fliggy.picasso.entity.convertor.CrowdInfoBoConvertor;
import com.fliggy.picasso.entity.param.CrowdSearchParam;
import com.fliggy.picasso.entity.param.CrowdUpsertParam;
import com.fliggy.picasso.group.crowd.dag.CrowdBuildDagFacade;
import com.fliggy.picasso.group.schedulerx.dag.CrowdBuildDagProcessor;
import com.fliggy.picasso.groupv2.groupbuild.dag.generator.GroupDagGenerator;
import com.fliggy.picasso.mapper.tripcrowd.CrowdMapper;
import com.fliggy.picasso.offline.OdpsService;
import com.fliggy.picasso.service.approval.ApprovalInfoService;
import com.fliggy.picasso.service.approval.impl.AbstractApprovalServiceImpl;
import com.fliggy.picasso.service.approval.impl.ApprovalServiceRouter;
import com.fliggy.picasso.service.crowd.CrowdCircleService;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.fliggy.picasso.service.crowd.export.CrowdExportService;
import com.fliggy.picasso.service.crowd.handler.GroupMatchTypeHandler;
import com.fliggy.picasso.service.group.GroupInfoService;
import com.fliggy.picasso.service.group.GroupRelationInfoService;
import com.fliggy.picasso.service.label.LabelBackgroundService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.fliggy.picasso.service.permission.PermissionService;
import com.fliggy.picasso.service.record.PicassoRecordService;
import com.fliggy.picasso.service.tair.TairService;
import com.fliggy.picasso.utils.ApprovalUtils;
import com.fliggy.picasso.utils.ChangeRecordUtils;
import com.fliggy.picasso.utils.CrowdUtils;
import com.fliggy.picasso.utils.EnvUtils;
import com.fliggy.picasso.utils.label.LabelUtils;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.OPERATE_CROWD_LEFT;
import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.OPERATE_CROWD_LEFT_REALTIME_TYPE;
import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.OPERATE_CROWD_RIGHT;
import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.OPERATE_CROWD_RIGHT_REALTIME_TYPE;

@Service
public class CrowdCircleServiceImpl implements CrowdCircleService {

    @Switch(description = "人群非必要配置修改不重跑")
    public static String crowdExtInfoWhiteList = "crowdBuildRecord,OSS_BUILT_DATE,UPDATE_MODEL";
    @Switch(description = "人群组合次数校验白名单")
    public static String checkOperateCntWhiteList = "";

    private String checkCrowdValidationMsgFormat = "该引用人群的上游嵌套人群包含已过期、使用待下线和已下线标签的人群(%s)";

    @Resource
    private CrowdMapper crowdDAO;
    @Resource
    private CrowdService crowdService;
    @Autowired
    private PermissionService permissionService;
    @Resource
    private GroupInfoService groupInfoService;
    @Resource
    private GroupRelationInfoService groupRelationInfoService;
    @Resource
    private LabelInfoService labelInfoService;
    @Resource
    private LabelBackgroundService labelBackgroundService;
    @Resource
    private ApprovalServiceRouter approvalServiceRouter;
    @Resource
    private GroupDagGenerator groupDagGenerator;
    @Resource
    private GroupMatchTypeHandler groupMatchTypeHandler;
    @Autowired
    private TairService<Long, CrowdMetaInfoDO> tairService;
    @Resource
    private PicassoRecordService picassoRecordService;
    @Resource
    private ApprovalInfoService approvalInfoService;
    @Resource
    private CrowdExportService crowdExportService;
    @Resource
    private CrowdBuildDagProcessor crowdBuildDagProcessor;
    @Resource
    private CrowdBuildDagFacade crowdBuildDagFacade;

    @AteyeInvoker(description = "查询人群", paraDesc = "id")
    @Override
    public CrowdInfoBO queryById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        CrowdMetaInfoDO crowdMetaInfoDO = crowdDAO.queryById(id);
        if (Objects.isNull(crowdMetaInfoDO)) {
            return null;
        }
        return CrowdInfoBoConvertor.convertCrowdInfoBOFromDO(crowdMetaInfoDO);
    }

    @Override
    public CrowdInfoBO queryByIdWithChildInfo(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        CrowdMetaInfoDO crowdMetaInfoDO = crowdDAO.queryById(id);
        if (Objects.isNull(crowdMetaInfoDO)) {
            return null;
        }
        CrowdInfoBO crowdInfoBO = CrowdInfoBoConvertor.convertCrowdInfoBOFromDO(crowdMetaInfoDO);
        if (Objects.isNull(crowdInfoBO)) {
            return null;
        }

        if (Objects.isNull(crowdMetaInfoDO.getCrowdBucketInfo()) || CollectionUtils.isEmpty(crowdMetaInfoDO.getCrowdBucketInfo().getChildCrowdList())) {
            return crowdInfoBO;
        }
        List<ChildCrowd> childCrowdList = crowdMetaInfoDO.getCrowdBucketInfo().getChildCrowdList();
        List<Long> childCrowdIds = childCrowdList.stream().map(GroupBucketInfoDO.ChildCrowd::getChildCrowdId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(childCrowdIds)) {
            return crowdInfoBO;
        }
        crowdInfoBO.setChildCrowds(queryByIds(childCrowdIds));
        return crowdInfoBO;
    }

    @Override
    public List<CrowdInfoBO> queryByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<CrowdMetaInfoDO> crowdMetaInfoDOList = crowdDAO.queryByIds(ids);
        if (CollectionUtils.isEmpty(crowdMetaInfoDOList)) {
            return Lists.newArrayList();
        }
        return crowdMetaInfoDOList.stream().map(CrowdInfoBoConvertor::convertCrowdInfoBOFromDO).collect(Collectors.toList());
    }

    @Override
    public CrowdInfoBO queryByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        CrowdMetaInfoDO crowdMetaInfoDO = crowdDAO.queryByName(name);
        if (Objects.isNull(crowdMetaInfoDO)) {
            return null;
        }
        return CrowdInfoBoConvertor.convertCrowdInfoBOFromDO(crowdMetaInfoDO);
    }

    @Override
    public CrowdSearchInfoBO searchCrowdList(CrowdSearchParam param) {
        if (Objects.isNull(param)) {
            return null;
        }
        if (Objects.nonNull(param.getGroupId())) {
            List<Long> ids = groupRelationInfoService.listEntityIdByGroupId(param.getGroupId());
            if (CollectionUtils.isEmpty(ids)) {
                return CrowdSearchInfoBO.emptySearchInfoBO();
            }
            param.setIds(ids);
        }

        CrowdSearchInfoBO result = new CrowdSearchInfoBO();
        result.setTabCntMap(getTabCntMap(param));

        List<CrowdMetaInfoDO> crowdList = getCrowdList(param);
        result.setCrowdInfoList(fillUpCrowdWithChildInfos(param.getEmpId(), crowdList));
        return result;
    }

    @Override
    public Boolean updateOwner(Employee operator, Long id, Employee owner) {
        CrowdInfoBO crowdInfoBO = queryByIdWithChildInfo(id);
        if (Objects.isNull(crowdInfoBO)) {
            throw new ParamErrorException("人群不存在");
        }

        if (!permissionService.isSuperAdmin() && !Objects.equals(crowdInfoBO.getOwner().getEmpId(), operator.getEmpId())) {
            throw new ParamErrorException("无权限修改人群");
        }

        if (CollectionUtils.isNotEmpty(crowdInfoBO.getChildCrowds())) {
            crowdInfoBO.getChildCrowds().forEach(crowd -> updateOwner(operator, crowd.getId(), owner));
        }

        CrowdMetaInfoDO updateDO = new CrowdMetaInfoDO();
        updateDO.setId(id);
        updateDO.setCreator(owner);
        updateDO.setEditTime(new Date());
        return crowdDAO.update(updateDO);
    }

    @Override
    public Boolean updateOwnerWithoutAuth(Long id, Employee owner) {
        CrowdInfoBO crowdInfoBO = queryByIdWithChildInfo(id);
        if (Objects.isNull(crowdInfoBO)) {
            throw new ParamErrorException(id + "人群不存在，请检查");
        }

        if (CollectionUtils.isNotEmpty(crowdInfoBO.getChildCrowds())) {
            crowdInfoBO.getChildCrowds().forEach(crowd -> updateOwnerWithoutAuth(crowd.getId(), owner));
        }

        CrowdMetaInfoDO updateDO = new CrowdMetaInfoDO();
        updateDO.setId(id);
        updateDO.setCreator(owner);
        updateDO.setEditTime(new Date());
        return crowdDAO.update(updateDO);
    }

    @Override
    public Boolean updateManager(Employee operator, Long id, List<Employee> managers) {
        CrowdInfoBO crowdInfoBO = queryByIdWithChildInfo(id);
        if (Objects.isNull(crowdInfoBO)) {
            throw new ParamErrorException("人群不存在");
        }

        if (!hasEditPermission(operator, crowdInfoBO)) {
            throw new ParamErrorException("无权限修改人群");
        }

        if (CollectionUtils.isNotEmpty(crowdInfoBO.getChildCrowds())) {
            crowdInfoBO.getChildCrowds().forEach(crowd -> updateManager(operator, crowd.getId(), managers));
        }

        CrowdMetaInfoDO updateDO = new CrowdMetaInfoDO();
        updateDO.setId(id);
        updateDO.setOperator(managers);
        updateDO.setEditTime(new Date());
        return crowdDAO.update(updateDO);
    }

    @AteyeInvoker(description = "更新人群更新类型", paraDesc = "id&needUpdate")
    @Override
    public Boolean updateUpdateType(Long id, Boolean needUpdate) {
        if (Objects.isNull(id) || Objects.isNull(needUpdate)) {
            return false;
        }

        CrowdMetaInfoDO updateDO = new CrowdMetaInfoDO();
        updateDO.setId(id);
        updateDO.setNeedUpdate((byte)(needUpdate ? 1 : 0));
        return crowdDAO.update(updateDO);
    }

    @Override
    public Long createCrowd(CrowdUpsertParam param) {
        // 补充审批信息
        addUpApprovalInfo(param);
        List<ApprovalInfoVO> approvalInfos = param.getApprovalInfos();
        CrowdMetaInfoDO crowdMetaInfoDO = CrowdInfoBoConvertor.convertCrowdInfoDOFromBO(param.convert2CrowdInfoBO());
        crowdMetaInfoDO.putExtInfo(EnvUtils.ENV, EnvUtils.currentEnv());
        if (CollectionUtils.isEmpty(approvalInfos) || !Objects.equals(CrowdTypeEnum.LABEL_CROWD, param.getCrowdType())) {
            // 不需要审批，直接创建
            Long crowdId = insert(crowdMetaInfoDO);
            crowdGroup(param.getOperator().getEmpId(), crowdId, param.getGroupIds(), false);
            return crowdId;
        }
        ApprovalUtils.preCheckApprovalInfos(approvalInfos);

        // 人群创建
        preCheckInsertCrowd(crowdMetaInfoDO);
        if (ApprovalUtils.isCrowdUpdateApprovalScene(param.getApprovalInfos())) {
            crowdMetaInfoDO.setCrowdStatus(GroupStatusEnum.DRAFT);
        }
        crowdService.insertCrowdAndRecordTransactional(crowdMetaInfoDO, approvalInfos);
        crowdGroup(param.getOperator().getEmpId(), crowdMetaInfoDO.getId(), param.getGroupIds(), false);

        // 审批处理
        approvalInfos.forEach(approvalInfoVO -> {
            approvalInfoVO.setEntityId(crowdMetaInfoDO.getId());
            AbstractApprovalServiceImpl approvalService = approvalServiceRouter.choose(approvalInfoVO.getApplyScene());
            approvalService.approvalHandle(approvalInfoVO.convertToBO(crowdMetaInfoDO.getId()));
        });
        return crowdMetaInfoDO.getId();
    }

    @Override
    public Boolean updateCrowd(CrowdUpsertParam param) {
        crowdGroup(param.getOperator().getEmpId(), param.getId(), param.getGroupIds(), true);
        // 补充审批信息
        addUpApprovalInfo(param);
        List<ApprovalInfoVO> approvalInfos = param.getApprovalInfos();
        CrowdMetaInfoDO crowdMetaInfoDO = CrowdInfoBoConvertor.convertCrowdInfoDOFromBO(param.convert2CrowdInfoBO());
        crowdMetaInfoDO.setEditTime(new Date());

        // 创建变更记录
        CrowdMetaInfoDO originCrowd = crowdDAO.queryById(param.getId());
        insertCrowdUpdateRecord(originCrowd, crowdMetaInfoDO, approvalInfos, param.getOperator());

        // 无审批信息，直接更新
        if (CollectionUtils.isEmpty(approvalInfos)) {
            return update(crowdMetaInfoDO);
        }

        // 审批处理
        ApprovalUtils.preCheckApprovalInfos(approvalInfos);
        approvalInfos.forEach(approvalInfoVO -> {
            approvalInfoVO.setEntityId(param.getId());
            AbstractApprovalServiceImpl approvalService = approvalServiceRouter.choose(approvalInfoVO.getApplyScene());
            approvalService.approvalHandle(approvalInfoVO.convertToBO(param.getId()));
        });
        return true;
    }

    @Override
    public Boolean deleteCrowd(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }

        CrowdMetaInfoDO crowdMetaInfoDO = crowdDAO.queryById(id);
        if (Objects.isNull(crowdMetaInfoDO)) {
            return false;
        }
        if (Objects.nonNull(crowdMetaInfoDO.getCrowdBucketInfo())
            && CollectionUtils.isNotEmpty(crowdMetaInfoDO.getCrowdBucketInfo().getChildCrowdList())) {
            for (ChildCrowd childCrowd : crowdMetaInfoDO.getCrowdBucketInfo().getChildCrowdList()) {
                if (Objects.isNull(childCrowd) || Objects.isNull(childCrowd.getChildCrowdId())) {
                    continue;
                }
                deleteCrowd(childCrowd.getChildCrowdId());
            }
        }

        CrowdMetaInfoDO updateDO = new CrowdMetaInfoDO();
        updateDO.setId(id);
        updateDO.setDeleted(Byte.valueOf("1"));
        updateDO.setEditTime(new Date());
        return crowdDAO.update(updateDO);
    }

    @Override
    public void crowdGroup(String empId, Long crowdId, List<Long> groupIds, boolean isUpdate) {
        if (Objects.isNull(crowdId) || CollectionUtils.isEmpty(groupIds)) {
            return;
        }
        if (!isUpdate) {
            groupRelationInfoService.batchGroup(crowdId, groupIds);
        }
        groupRelationInfoService.handleGroup(empId, crowdId, groupIds);
    }

    @Override
    public Boolean isNameDuplicate(String name, Long crowdId) {
        if (StringUtils.isEmpty(name)) {
            return false;
        }

        CrowdInfoBO crowdInfoBO = queryByName(name);
        if (Objects.isNull(crowdInfoBO)) {
            return false;
        }
        return Objects.isNull(crowdId) || !Objects.equals(crowdId, crowdInfoBO.getId());
    }

    @Override
    public Boolean isCrowdRealTime(CrowdCircleTypeEnum circleType, LabelCrowdConditions conditions) {
        if (Objects.isNull(circleType) || Objects.isNull(conditions)) {
            return false;
        }
        if (Objects.equals(CrowdCircleTypeEnum.LABEL_CROWD, circleType)) {
            List<String> labelCodes = CrowdUtils.getLabelCodesInLabelCrowd(conditions);
            List<LabelInfoBackgroundBO> labelInfoList = labelBackgroundService.queryByCodes(labelCodes);
            if (CollectionUtils.isEmpty(labelInfoList)) {
                return false;
            }
            return labelInfoList.stream().anyMatch(labelInfo -> Objects.nonNull(labelInfo.getTimeType())
                && Objects.equals(labelInfo.getTimeType(), LabelType.REALTIME));
        }
        if (Objects.equals(CrowdCircleTypeEnum.OPERATE_CROWD, circleType)) {
            List<Long> childCrowdIds = CrowdUtils.getChildCrowdIdsInOperateExpressionCrowd(conditions);
            List<CrowdMetaInfoDO> crowdInfoList = crowdDAO.queryByIds(childCrowdIds);
            if (CollectionUtils.isEmpty(crowdInfoList)) {
                return false;
            }
            return crowdInfoList.stream().anyMatch(CrowdMetaInfoDO::doesRealTime);
        }
        return false;
    }

    @Override
    public List<CrowdInfoBO> findChildCrowdsByConditions(LabelCrowdConditions conditions) {
        if (Objects.isNull(conditions)) {
            return Collections.emptyList();
        }
        List<Long> childCrowdIds = CrowdUtils.getChildCrowdIdsInOperateExpressionCrowd(conditions);
        List<CrowdMetaInfoDO> crowdInfoList = crowdDAO.queryByIds(childCrowdIds);
        if (CollectionUtils.isEmpty(crowdInfoList)) {
            return Collections.emptyList();
        }
        return crowdInfoList.stream().map(CrowdInfoBoConvertor::convertCrowdInfoBOFromDO).collect(Collectors.toList());
    }

    @AteyeInvoker(description = "校验人群组合次数", paraDesc = "crowdId&operateCnt")
    @Override
    public Boolean checkCrowdOperateCnt(Long crowdId, Integer operateCnt) {
        if (StringUtils.isNotBlank(checkOperateCntWhiteList)) {
            List<String> whiteList = new ArrayList<>(Arrays.asList(checkOperateCntWhiteList.split(",")));
            if (whiteList.contains(String.valueOf(crowdId))) {
                return true;
            }
        }

        int cnt = 0;
        List<Long> operateCrowdIds = Collections.singletonList(crowdId);
        while (CollectionUtils.isNotEmpty(operateCrowdIds) && cnt < operateCnt) {
            cnt++;
            Set<Long> tmpCrowdIds = new HashSet<>();
            operateCrowdIds.forEach(operateCrowdId -> {
                CrowdMetaInfoDO crowdMetaInfoDO = crowdDAO.queryById(operateCrowdId);
                if (Objects.isNull(crowdMetaInfoDO)) {
                    return;
                }
                if (!CrowdTypeEnum.isOperate(crowdMetaInfoDO.getCrowdType())) {
                    return;
                }
                tmpCrowdIds.addAll(CrowdUtils.getChildCrowdIds(crowdMetaInfoDO));
            });
            operateCrowdIds = new ArrayList<>(tmpCrowdIds);
        }
        return CollectionUtils.isEmpty(operateCrowdIds) && cnt < operateCnt;
    }

    @AteyeInvoker(description = "校验人群有效性", paraDesc = "crowdId")
    @Override
    public Boolean checkCrowdValidation(Long crowdId) {
        if (Objects.isNull(crowdId)) {
            return true;
        }
        CrowdMetaInfoDO crowdMetaInfoDO = crowdDAO.queryById(crowdId);
        if (Objects.isNull(crowdMetaInfoDO) || crowdMetaInfoDO.doesDeleted() || crowdMetaInfoDO.doesExpired()) {
            throw new ParamErrorException("不允许引用已过期的人群：" + crowdId);
        }

        if (Objects.equals(crowdMetaInfoDO.getCrowdType(), CrowdTypeEnum.LABEL_CROWD)) {
            Boolean result = checkLabelCrowdValidationInOperate(crowdMetaInfoDO.getConditions());
            if (!result) {
                throw new ParamErrorException("不允许引用含待下线和已下线标签的人群：" + crowdId);
            }
        } else if (Objects.equals(crowdMetaInfoDO.getCrowdType(), CrowdTypeEnum.OPERATE_CROWD)
            || Objects.equals(crowdMetaInfoDO.getCrowdType(), CrowdTypeEnum.OPERATE_EXPRESSION_CROWD)) {
            List<String> invalidCrowdIdsByLevel = new ArrayList<>();
            checkOperateCrowdValidation(crowdId, invalidCrowdIdsByLevel, "");
            if (CollectionUtils.isNotEmpty(invalidCrowdIdsByLevel)) {
                String errorMessage = formatInvalidCrowdIdsMessage(invalidCrowdIdsByLevel);
                throw new ParamErrorException(errorMessage);
            }
        }
        return true;
    }

    @Override
    public Boolean postpone(Long crowdId, Long timestamp) {
        if (crowdId == null || crowdId < 0) {
            throw new ParamErrorException(String.format("人群延期出错，人群ID：%s 格式异常。", crowdId));
        }

        CrowdInfoBO crowdInfoBO = queryByIdWithChildInfo(crowdId);
        if (crowdInfoBO == null) {
            throw new ParamErrorException(String.format("人群延期出错，人群ID：%d不存在。", crowdId));
        }

        Date expiredDate = DateUtils.getDateFromTimeStamp(timestamp);
        if (expiredDate.before(new Date())) {
            throw new ParamErrorException("人群延期出错，新的过期时间不能早于现在。");
        }

        if (DateUtils.getIntervalDays(new Date(), expiredDate) > 90) {
            throw new ParamErrorException("人群延期出错，一次延期不能超过90天。");
        }

        if (CollectionUtils.isNotEmpty(crowdInfoBO.getChildCrowds())) {
            crowdInfoBO.getChildCrowds().forEach(childCrowd -> postpone(childCrowd.getId(), timestamp));
        }

        CrowdExportTaskDO taskDO = crowdExportService.queryByCrowdId(crowdId);
        if (Objects.nonNull(taskDO)) {
            CrowdExportTaskDO updateTaskDO = new CrowdExportTaskDO();
            updateTaskDO.setId(taskDO.getId());
            updateTaskDO.setExportEnd(expiredDate);
            crowdExportService.update(updateTaskDO);
        }

        CrowdMetaInfoDO updateDO = new CrowdMetaInfoDO();
        updateDO.setId(crowdId);
        updateDO.setEditTime(new Date());
        updateDO.setExpiredDate(expiredDate);
        return crowdDAO.update(updateDO);
    }

    @AteyeInvoker(description = "人群延期", paraDesc = "人群ID&有效期时间戳（毫秒）")
    public void postponeManually(Long crowdId, Long timestamp) {
        CrowdInfoBO crowdInfoBO = queryByIdWithChildInfo(crowdId);
        if (crowdInfoBO == null) {
            throw new ParamErrorException(String.format("人群延期出错，人群ID：%d不存在。", crowdId));
        }

        Date expiredDate = DateUtils.getDateFromTimeStamp(timestamp);
        if (expiredDate.before(new Date())) {
            throw new ParamErrorException("人群延期出错，新的过期时间不能早于现在。");
        }

        if (CollectionUtils.isNotEmpty(crowdInfoBO.getChildCrowds())) {
            crowdInfoBO.getChildCrowds().forEach(childCrowd -> postpone(childCrowd.getId(), timestamp));
        }

        CrowdExportTaskDO taskDO = crowdExportService.queryByCrowdId(crowdId);
        if (Objects.nonNull(taskDO)) {
            CrowdExportTaskDO updateTaskDO = new CrowdExportTaskDO();
            updateTaskDO.setId(taskDO.getId());
            updateTaskDO.setExportEnd(expiredDate);
            crowdExportService.update(updateTaskDO);
        }

        CrowdMetaInfoDO updateDO = new CrowdMetaInfoDO();
        updateDO.setId(crowdId);
        updateDO.setEditTime(new Date());
        updateDO.setExpiredDate(expiredDate);
        Ateye.out.println(crowdId + "延期结果:" + crowdDAO.update(updateDO));
    }

    @AteyeInvoker(description = "批量人群延期", paraDesc = "人群ID列表（英文逗号间隔）&有效期时间戳（毫秒）")
    public void batchPostponeManually(String crowdIds, Long timestamp) {
        String[] crowdIdList = crowdIds.split(",");
        for (String crowdIdStr : crowdIdList) {
            postponeManually(Long.valueOf(crowdIdStr), timestamp);
        }
    }

    @Override
    public Boolean checkLabelCrowdValidation(Long crowdId) {
        if (Objects.isNull(crowdId)) {
            return true;
        }
        CrowdMetaInfoDO crowdMetaInfoDO = crowdDAO.queryById(crowdId);
        if (Objects.isNull(crowdMetaInfoDO) || crowdMetaInfoDO.doesDeleted() || crowdMetaInfoDO.doesExpired()) {
            throw new ParamErrorException("不允许引用已过期的人群：" + crowdId);
        }

        if (Objects.isNull(crowdMetaInfoDO.getConditions())) {
            return true;
        }
        List<String> labelCodes = CrowdUtils.getLabelCodesInLabelCrowd(crowdMetaInfoDO.getConditions());
        if (CollectionUtils.isEmpty(labelCodes)) {
            return true;
        }
        List<LabelInfoBackgroundBO> labelInfoBOList = labelBackgroundService.queryByCodes(labelCodes);
        if (CollectionUtils.isEmpty(labelInfoBOList)) {
            return true;
        }
        List<String> invalidLabelNames = labelInfoBOList.stream().filter(label -> Objects.isNull(label.getDeleted()) || label.getDeleted()
                    || label.getStatus().equals(LabelStatusEnum.OFFLINE) || label.getStatus().equals(LabelStatusEnum.WAIT_OFFLINE))
                    .map(LabelInfoBackgroundBO::getName).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(invalidLabelNames)) {
            throw new ParamErrorException("不允许引用待下线或已下线的标签：" +  String.join(",", invalidLabelNames));
        }
        return true;
    }

    private Boolean checkLabelCrowdValidationInOperate(LabelCrowdConditions conditions) {
        if (Objects.isNull(conditions)) {
            return true;
        }
        List<String> labelCodes = CrowdUtils.getLabelCodesInLabelCrowd(conditions);
        if (CollectionUtils.isEmpty(labelCodes)) {
            return true;
        }
        List<LabelInfoBackgroundBO> labelInfoBOList = labelBackgroundService.queryByCodes(labelCodes);
        if (CollectionUtils.isEmpty(labelInfoBOList)) {
            return false;
        }
        boolean anyInvalid =  labelInfoBOList.stream().anyMatch(label -> Objects.isNull(label.getDeleted()) || label.getDeleted()
            || label.getStatus().equals(LabelStatusEnum.OFFLINE) || label.getStatus().equals(LabelStatusEnum.WAIT_OFFLINE));
        return !anyInvalid;
    }

    private void checkOperateCrowdValidation(Long crowdId, List<String> invalidCrowdIdsByLevel, String prefix) {
        CrowdMetaInfoDO crowdMetaInfoDO = crowdDAO.queryById(crowdId);
        if (Objects.isNull(crowdMetaInfoDO) || crowdMetaInfoDO.doesDeleted() || crowdMetaInfoDO.doesExpired()) {
            invalidCrowdIdsByLevel.add(prefix + "-" + crowdId);
        }

        if (Objects.equals(crowdMetaInfoDO.getCrowdType(), CrowdTypeEnum.LABEL_CROWD)) {
            Boolean result = checkLabelCrowdValidationInOperate(crowdMetaInfoDO.getConditions());
            if (!result) {
                invalidCrowdIdsByLevel.add(prefix + "-" + crowdId);
            }
        } else if (Objects.equals(crowdMetaInfoDO.getCrowdType(), CrowdTypeEnum.OPERATE_CROWD)
            || Objects.equals(crowdMetaInfoDO.getCrowdType(), CrowdTypeEnum.OPERATE_EXPRESSION_CROWD)) {
            List<Long> childCrowdIds = CrowdUtils.getChildCrowdIds(crowdMetaInfoDO);
            if (CollectionUtils.isEmpty(childCrowdIds)) {
                return;
            }

            for (Long childCrowdId : childCrowdIds) {
                checkOperateCrowdValidation(childCrowdId, invalidCrowdIdsByLevel,
                    StringUtils.isEmpty(prefix) ? String.valueOf(crowdId) : prefix + "-" + crowdId);
            }
        }
    }

    private String formatInvalidCrowdIdsMessage(List<String> invalidCrowdIdsByLevel) {
        String errInfo = String.join(";", invalidCrowdIdsByLevel);
        return "该引用人群的上游嵌套人群包含已过期、使用待下线和已下线标签的人群(" + errInfo + ")";
    }

    private void insertCrowdUpdateRecord(CrowdMetaInfoDO originCrowd, CrowdMetaInfoDO crowdMetaInfoDO, List<ApprovalInfoVO> approvalInfos, Employee operator) {
        if (Objects.isNull(crowdMetaInfoDO)) {
            throw new ParamErrorException("新增人群时出错，crowdMetaInfoDO为null。");
        }

        PicassoChangeRecordBO picassoChangeRecordBO = new PicassoChangeRecordBO(operator, ChangeRecordSourceTypeEnum.CROWD_UPDATE, String.valueOf(crowdMetaInfoDO.getId()));
        if (Objects.nonNull(originCrowd)) {
            picassoChangeRecordBO.setBeforeData(new ChangeRecordData(originCrowd));
        }
        picassoChangeRecordBO.setAfterData(new ChangeRecordData(crowdMetaInfoDO));
        picassoChangeRecordBO.setApprovalInfos(
            ChangeRecordUtils.handleApprovalInfos(approvalInfos, ChangeRecordSourceTypeEnum.CROWD_UPDATE));
        int insertRecord = picassoRecordService.insert(picassoChangeRecordBO);
        if (insertRecord < 1) {
            throw new RuntimeException("变更记录创建出错");
        }
    }

    public Boolean update(CrowdMetaInfoDO crowdMetaInfoDO) {
        preCheckUpdateCrowd(crowdMetaInfoDO);
        boolean isCorePropertiesChanged = checkCorePropertiesChanged(crowdMetaInfoDO);
        if (!isCorePropertiesChanged) {
            crowdMetaInfoDO.setCrowdStatus(null);
        }
        boolean result = crowdDAO.update(crowdMetaInfoDO);
        updateChildCrowds(crowdMetaInfoDO, isCorePropertiesChanged);

        // 生成构建dag
        if (result && isCorePropertiesChanged) {
            tairService.invalid(crowdMetaInfoDO.getId(), CrowdMetaInfoDO.class);
            Long profileId = ProfileEnum.getIdByCode(crowdMetaInfoDO.getProfileType());
            if (!groupDagGenerator.preDagTaskClear(profileId, crowdMetaInfoDO.getId())) {
                return false;
            }
            Long dagId = groupDagGenerator.generate(profileId, crowdMetaInfoDO.getId(), OdpsService.MIDDLE_PRIORITY);
            // 如果是线上环境，自动执行DAG
            if (EnvUtils.isOnline() && CrowdSwitchConfig.openEditBoostAutoSchedule) {
                crowdBuildDagProcessor.dagAutoRun(dagId);
            }
        }
        return result;
    }

    /**
     * 子人群配置和父人群保持一致
     * @param crowdMetaInfoDO
     */
    @Transactional
    public void updateChildCrowds(CrowdMetaInfoDO crowdMetaInfoDO, boolean isCorePropertiesChanged) {
        CrowdMetaInfoDO oldCrowdMetaInfoDO = crowdDAO.queryById(crowdMetaInfoDO.getId());
        if (Objects.isNull(oldCrowdMetaInfoDO) || Objects.isNull(oldCrowdMetaInfoDO.getCrowdBucketInfo())
            || CollectionUtils.isEmpty(oldCrowdMetaInfoDO.getCrowdBucketInfo().getChildCrowdList())) {
            return;
        }
        oldCrowdMetaInfoDO.getCrowdBucketInfo().getChildCrowdList().forEach(childCrowd -> {
            if (Objects.isNull(childCrowd.getChildCrowdId())) {
                return;
            }
            CrowdMetaInfoDO originCrowdDO = crowdDAO.queryById(childCrowd.getChildCrowdId());
            if (Objects.isNull(originCrowdDO)) {
                return;
            }
            if (!Objects.equals(originCrowdDO.getNeedUpdate(), crowdMetaInfoDO.getNeedUpdate())) {
                CrowdMetaInfoDO updateDO = new CrowdMetaInfoDO();
                updateDO.setId(originCrowdDO.getId());
                updateDO.setNeedUpdate(crowdMetaInfoDO.getNeedUpdate());
                crowdDAO.update(updateDO);
            }

            if (isCorePropertiesChanged) {
                crowdBuildDagFacade.rerun(childCrowd.getChildCrowdId(), OdpsService.MIDDLE_PRIORITY);
            }
        });
    }

    private void preCheckUpdateCrowd(CrowdMetaInfoDO crowdMetaInfoDO) {
        if (Objects.isNull(crowdMetaInfoDO)) {
            throw new ParamErrorException("人群预校验错误：crowdMetaInfoDO is null");
        }

        // 人群名称重复校验
        CrowdMetaInfoDO oldCrowdMetaInfoDO = crowdDAO.queryById(crowdMetaInfoDO.getId());
        if (Objects.nonNull(oldCrowdMetaInfoDO) && !Objects.equals(oldCrowdMetaInfoDO.getCrowdName(), crowdMetaInfoDO.getCrowdName())) {
            CrowdMetaInfoDO oldCrowdByName = crowdDAO.queryByName(crowdMetaInfoDO.getCrowdName());
            if (Objects.nonNull(oldCrowdByName)) {
                throw new ParamErrorException("人群预校验错误：人群名称重复");
            }
        }

        if (CrowdTypeEnum.isOperate(crowdMetaInfoDO.getCrowdType())) {
            List<Long> childCrowdIds = CrowdUtils.getChildCrowdIds(crowdMetaInfoDO);
            for (Long childCrowdId : childCrowdIds) {
                checkCrowdValidation(childCrowdId);
            }
        }

        CrowdUtils.checkDuplicateLabelInOneGroup(crowdMetaInfoDO.getConditions());
        crowdMetaInfoDO.setRealTime(crowdService.getCrowdRealTimeType(crowdMetaInfoDO));
        // 场景&人群匹配方式判断
        checkApplyScenes(crowdMetaInfoDO);

        // 如果人群状态改变触发判定类型改变，需修改判定类型
        groupMatchTypeHandler.changMatchType(oldCrowdMetaInfoDO, crowdMetaInfoDO);

        // 人群组合添加子人群实时类型
        putChildRealTimeOperateCrowd(crowdMetaInfoDO);
    }

    /**
     * 检查核心配置是否变更，如果变更返回true
     * @param crowdMetaInfoDO
     * @return
     */
    private boolean checkCorePropertiesChanged(CrowdMetaInfoDO crowdMetaInfoDO) {
        CrowdMetaInfoDO oldCrowdMetaInfoDO = crowdDAO.queryById(crowdMetaInfoDO.getId());
        if (Objects.nonNull(oldCrowdMetaInfoDO) && Objects.equals(oldCrowdMetaInfoDO.getCrowdStatus(), GroupStatusEnum.DRAFT)
            && Objects.equals(crowdMetaInfoDO.getCrowdStatus(), GroupStatusEnum.INIT)) {
            // 历史人群是草稿状态，不需要校验配置
            return true;
        }

        // 人群类型变更
        if (Objects.nonNull(crowdMetaInfoDO.getCrowdType()) && Objects.nonNull(oldCrowdMetaInfoDO.getCrowdType())
            && !Objects.equals(crowdMetaInfoDO.getCrowdType(), oldCrowdMetaInfoDO.getCrowdType())) {
            return true;
        }

        // 人群实时性变更
        if (Objects.nonNull(crowdMetaInfoDO.getRealTime()) && Objects.nonNull(oldCrowdMetaInfoDO.getRealTime())
            && !Objects.equals(crowdMetaInfoDO.getRealTime(), oldCrowdMetaInfoDO.getRealTime())) {
            return true;
        }

        // 人群应用场景变更
        String newApplyScene = JSON.toJSONString(crowdMetaInfoDO.getCrowdApplyScene());
        String oldApplyScene = JSON.toJSONString(oldCrowdMetaInfoDO.getCrowdApplyScene());
        if (!newApplyScene.equals(oldApplyScene)) {
            return true;
        }

        // 人群删除状态变更：从已删除到未删除
        if (!crowdMetaInfoDO.doesDeleted() && oldCrowdMetaInfoDO.doesDeleted()) {
            return true;
        }

        // 人群标签条件变更
        if (checkConditionsChanged(crowdMetaInfoDO.getConditions(), oldCrowdMetaInfoDO.getConditions())) {
            return true;
        }

        // 人群配置变更
        if (checkExtInfoChanged(crowdMetaInfoDO.getExtInfo(), oldCrowdMetaInfoDO.getExtInfo())) {
            return true;
        }
        return false;
    }

    /**
     * 人群conditions变更
     */
    private boolean checkConditionsChanged(LabelCrowdConditions conditions, LabelCrowdConditions oldConditions) {
        if (Objects.isNull(conditions) && Objects.isNull(oldConditions)) {
            return false;
        }
        if (Objects.isNull(conditions) || Objects.isNull(oldConditions)) {
            return true;
        }
        return !conditions.equals(oldConditions);
    }

    /**
     * 人群配置变更
     */
    private boolean checkExtInfoChanged(String extInfo, String oldExtInfo) {
        if (Objects.isNull(extInfo) && Objects.isNull(oldExtInfo)) {
            return false;
        }
        if (Objects.isNull(extInfo) || Objects.isNull(oldExtInfo)) {
            return true;
        }

        JSONObject extConfigs = JSON.parseObject(extInfo);
        JSONObject oldExtConfigs = JSON.parseObject(oldExtInfo);
        List<String> extInfoWhiteList = new ArrayList<>(Arrays.asList(crowdExtInfoWhiteList.split(",")));
        for (String key : extConfigs.keySet()) {
            if (extInfoWhiteList.contains(key)) {
                continue;
            }

            if (!oldExtConfigs.containsKey(key)) {
                return true;
            }
            if (!Objects.equals(extConfigs.get(key), oldExtConfigs.get(key))) {
                return true;
            }
        }
        return false;
    }

    public Long insert(CrowdMetaInfoDO crowdMetaInfoDO) {
        preCheckInsertCrowd(crowdMetaInfoDO);
        crowdService.insertCrowdAndRecordTransactional(crowdMetaInfoDO, null);
        long dagId = groupDagGenerator.generate(ProfileEnum.getIdByCode(crowdMetaInfoDO.getProfileType()), crowdMetaInfoDO.getId(), OdpsService.MIDDLE_PRIORITY);
        // 如果是线上环境，自动执行DAG
        if (EnvUtils.isOnline() && CrowdSwitchConfig.openEditBoostAutoSchedule) {
            crowdBuildDagProcessor.dagAutoRun(dagId);
        }
        return crowdMetaInfoDO.getId();
    }

    private void preCheckInsertCrowd(CrowdMetaInfoDO crowdMetaInfoDO) {
        if (Objects.isNull(crowdMetaInfoDO)) {
            throw new ParamErrorException("人群预校验错误：crowdMetaInfoDO is null");
        }

        // 人群名称重复校验
        CrowdMetaInfoDO oldCrowd = crowdDAO.queryByName(crowdMetaInfoDO.getCrowdName());
        if (Objects.nonNull(oldCrowd)) {
            throw new ParamErrorException("人群预校验错误：人群名称重复");
        }

        // 场景&人群匹配方式判断
        checkApplyScenes(crowdMetaInfoDO);
        CrowdUtils.checkDuplicateLabelInOneGroup(crowdMetaInfoDO.getConditions());
        crowdMetaInfoDO.setRealTime(crowdService.getCrowdRealTimeType(crowdMetaInfoDO));
        // 人群组合添加子人群实时类型
        putChildRealTimeOperateCrowd(crowdMetaInfoDO);
        groupMatchTypeHandler.initMatchType(crowdMetaInfoDO);
    }

    /**
     * 校验应用场景
     * @param crowdMetaInfoDO
     */
    private void checkApplyScenes(CrowdMetaInfoDO crowdMetaInfoDO) {
        List<String> supportApplyScenes = crowdService.supportCrowdApplyScenes(crowdMetaInfoDO);
        if (CollectionUtils.isEmpty(supportApplyScenes)) {
            if (CollectionUtils.isNotEmpty(crowdMetaInfoDO.getCrowdApplyScene())) {
                throw new ParamErrorException("人群预校验错误：存在不支持的人群场景，" + crowdMetaInfoDO.getCrowdApplyScene());
            }
        } else if (CollectionUtils.isNotEmpty(crowdMetaInfoDO.getCrowdApplyScene())) {
            Set<String> supportApplySceneSet = new HashSet<>(supportApplyScenes);
            if (!supportApplySceneSet.containsAll(crowdMetaInfoDO.getCrowdApplyScene())) {
                List<String> tmpScenes = Lists.newArrayList(crowdMetaInfoDO.getCrowdApplyScene());
                tmpScenes.removeAll(supportApplyScenes);
                throw new ParamErrorException("人群预校验错误：存在不支持的人群场景，" + tmpScenes);
            }
        }
    }

    private void putChildRealTimeOperateCrowd(CrowdMetaInfoDO crowdMetaInfoDO) {
        if (Objects.isNull(crowdMetaInfoDO) || !CrowdTypeEnum.OPERATE_CROWD.equals(crowdMetaInfoDO.getCrowdType())) {
            return;
        }

        long leftCrowdId = crowdMetaInfoDO.fetchExtInfo(OPERATE_CROWD_LEFT, Long.class);
        long rightCrowdId = crowdMetaInfoDO.fetchExtInfo(OPERATE_CROWD_RIGHT, Long.class);
        CrowdMetaInfoDO leftCrowd = crowdService.queryById(leftCrowdId);
        CrowdMetaInfoDO rightCrowd = crowdService.queryById(rightCrowdId);
        if (Objects.isNull(leftCrowd) || Objects.isNull(rightCrowd)) {
            throw new ParamErrorException("获取人群实时类型失败，组合圈人子人群非法");
        }

        if (!leftCrowd.getProfileType().equals(crowdMetaInfoDO.getProfileType())
            || !rightCrowd.getProfileType().equals(crowdMetaInfoDO.getProfileType())) {
            throw new ParamErrorException("子人群类型不一致，请检查");
        }
        crowdMetaInfoDO.putExtInfo(OPERATE_CROWD_LEFT_REALTIME_TYPE, leftCrowd.getRealTime().toString());
        crowdMetaInfoDO.putExtInfo(OPERATE_CROWD_RIGHT_REALTIME_TYPE, rightCrowd.getRealTime().toString());
    }

    /**
     * 补充审批信息
     * @param param
     */
    private void addUpApprovalInfo(CrowdUpsertParam param) {
        if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getApprovalInfos()) ||
            !Objects.equals(param.getCrowdType(), CrowdTypeEnum.LABEL_CROWD) || Objects.isNull(param.getConditions())) {
            return;
        }

        List<ApprovalInfoVO> approvalInfos = param.getApprovalInfos();
        List<LabelInfoDTO> allLabelList = labelInfoService.findByCodes(LabelUtils.getAllLabelNames(param.getConditions()));
        for (ApprovalInfoVO approvalInfoVO : approvalInfos) {
            if (Objects.equals(ApprovalApplySceneEnum.FUND_LOSS, approvalInfoVO.getApplyScene())) {
                continue;
            }

            ApprovalInfoConfig approvalInfo = Objects.isNull(approvalInfoVO.getApprovalInfo()) ? new ApprovalInfoConfig() : approvalInfoVO.getApprovalInfo();
            approvalInfo.setGroupName(param.getCrowdName());
            approvalInfo.setApprovalReason(ApprovalApplySceneEnum.getApprovalReason(approvalInfoVO.getApplyScene()));
            approvalInfo.setLabelName(JSON.toJSONString(ApprovalUtils.getAllAdexLabelList(allLabelList)));
            approvalInfo.setApprovalLevel(ApprovalUtils.getBpmsProcessType(approvalInfoVO, allLabelList));
            approvalInfoVO.setApprovalInfo(approvalInfo);
        }
        param.setApprovalInfos(approvalInfos);
    }

    /**
     * 判断是否有编辑权限
     * @param operator
     * @param crowdInfoBO
     * @return
     */
    private boolean hasEditPermission(Employee operator, CrowdInfoBO crowdInfoBO) {
        if (permissionService.isSuperAdmin()) {
            return true;
        }
        if (Objects.nonNull(crowdInfoBO) && Objects.nonNull(crowdInfoBO.getOwner())
            && Objects.equals(crowdInfoBO.getOwner().getEmpId(), operator.getEmpId())) {
            return true;
        }
        if (CollectionUtils.isEmpty(crowdInfoBO.getManagers())) {
            return false;
        }
        for (Employee manager : crowdInfoBO.getManagers()) {
            if (Objects.equals(manager.getEmpId(), operator.getEmpId())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 补充子人群信息
     * @param crowdList
     * @return
     */
    private List<CrowdInfoBO> fillUpCrowdWithChildInfos(String empId, List<CrowdMetaInfoDO> crowdList) {
        if (CollectionUtils.isEmpty(crowdList)) {
            return Lists.newArrayList();
        }

        List<CrowdInfoBO> result = Lists.newArrayList();
        for (CrowdMetaInfoDO crowd : crowdList) {
            CrowdInfoBO crowdInfoBO = CrowdInfoBoConvertor.convertCrowdInfoBOFromDO(crowd);
            if (Objects.isNull(crowdInfoBO)) {
                continue;
            }
            // 是否七日内过期
            if (Objects.nonNull(crowd.getExpiredDate()) && DateUtils.getIntervalDays(new Date(), crowd.getExpiredDate()) <= 7) {
                crowdInfoBO.setIsExpiredIn7Days(true);
            }

            // 是否已分组
            Boolean grouped = groupInfoService.isGrouped(empId, GroupTypeEnum.CROWD, crowd.getId());
            crowdInfoBO.setIsGrouped(grouped);
            result.add(crowdInfoBO);

            // 是否已导出
            CrowdExportTaskDO taskDO = crowdExportService.queryByCrowdId(crowd.getId());
            if (Objects.nonNull(taskDO)) {
                crowdInfoBO.setExported(true);
            }

            // 补充审批信息
            ApprovalInfoBO approvalInfoBO = approvalInfoService.queryLatestByEntityIdAndApplyScene(crowd.getId(),
                ApprovalApplySceneEnum.CROWD_UPDATE);
            if (Objects.nonNull(approvalInfoBO)) {
                crowdInfoBO.setApprovalInfo(approvalInfoBO);
            }

            // 补充子人群信息
            if (Objects.isNull(crowd.getCrowdBucketInfo())) {
                continue;
            }
            GroupBucketInfoDO crowdBucketInfo = crowd.getCrowdBucketInfo();
            if (CollectionUtils.isEmpty(crowdBucketInfo.getChildCrowdList())) {
                continue;
            }
            List<Long> childCrowdIds = crowdBucketInfo.getChildCrowdList().stream().map(GroupBucketInfoDO.ChildCrowd::getChildCrowdId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(childCrowdIds)) {
                continue;
            }
            crowdInfoBO.setChildCrowds(queryByIds(childCrowdIds));
        }
        return result;
    }

    /**
     * 获取人群列表
     * @param param
     * @return
     */
    private List<CrowdMetaInfoDO> getCrowdList(CrowdSearchParam param) {
        if (Objects.isNull(param)) {
            return Lists.newArrayList();
        }
        CrowdMetaInfoSearch crowdMetaInfoSearch = param.convert2CrowdMetaInfoSearch();
        if (Objects.equals(param.getClassifyTab(), CrowdClassifyTabEnum.MY_CROWDS)) {
            crowdMetaInfoSearch.setEmpId(param.getEmpId());
        }
        return crowdDAO.searchCrowdList(crowdMetaInfoSearch);
    }

    private Map<CrowdClassifyTabEnum, Long> getTabCntMap(CrowdSearchParam param) {
        if (Objects.isNull(param)) {
            return null;
        }
        Map<CrowdClassifyTabEnum, Long> tabCntMap = new HashMap<>();
        Long totalCnt = countCrowdByTab(param, CrowdClassifyTabEnum.ALL);
        if (totalCnt == 0L) {
            tabCntMap.put(CrowdClassifyTabEnum.ALL, 0L);
            tabCntMap.put(CrowdClassifyTabEnum.MY_CROWDS, 0L);
            tabCntMap.put(CrowdClassifyTabEnum.OFFICIAL_CROWDS, 0L);
            return tabCntMap;
        }

        tabCntMap.put(CrowdClassifyTabEnum.ALL, totalCnt);
        tabCntMap.put(CrowdClassifyTabEnum.MY_CROWDS, countCrowdByTab(param, CrowdClassifyTabEnum.MY_CROWDS));
        tabCntMap.put(CrowdClassifyTabEnum.OFFICIAL_CROWDS, countCrowdByTab(param, CrowdClassifyTabEnum.OFFICIAL_CROWDS));
        return tabCntMap;
    }

    /**
     * 根据tab获取人群数量
     * @param param
     * @param tab
     * @return
     */
    private Long countCrowdByTab(CrowdSearchParam param, CrowdClassifyTabEnum tab) {
        if (Objects.isNull(param) || Objects.isNull(tab)) {
            return 0L;
        }
        // 创建新的搜索参数，避免修改原参数
        CrowdSearchParam countParam = new CrowdSearchParam();
        BeanUtils.copyProperties(param, countParam);
        countParam.setClassifyTab(tab);
        
        CrowdMetaInfoSearch crowdMetaInfoSearch = countParam.convert2CrowdMetaInfoSearch();
        if (Objects.equals(tab, CrowdClassifyTabEnum.MY_CROWDS)) {
            crowdMetaInfoSearch.setEmpId(param.getEmpId());
        }
        return crowdDAO.countCrowdList(crowdMetaInfoSearch);
    }

    @AteyeInvoker(description = "更新人群负责人", paraDesc = "opeEmpId&opeNickName&crowdId&empId&nickName")
    public void testUpdateOwner(String opeEmpId, String opeNickName, Long crowdId, String empId, String nickName) {
        Boolean result = updateOwner(new Employee(opeEmpId, opeNickName), crowdId, new Employee(empId, nickName));
        Ateye.out.println(result);
    }

    @AteyeInvoker(description = "更新人群管理员", paraDesc = "opeEmpId&opeNickName&crowdId&managers")
    public void testUpdateManager(String opeEmpId, String opeNickName, Long crowdId, String managers) {
        Boolean result = updateManager(new Employee(opeEmpId, opeNickName), crowdId, JSON.parseArray(managers, Employee.class));
        Ateye.out.println(result);
    }

    @AteyeInvoker(description = "更新人群", paraDesc = "id&name&description&physicalProfileCode&crowdType&conditions&needUpdate&timestamp&applyScenes&groupIds&extInfo")
    public void testUpsertCrowd(Long id, String name, String description, String physicalProfileCode, String crowdType
                                , String conditions, Boolean needUpdate, Long timestamp, String applyScenes, String groupIds, String extInfo) {
        CrowdUpsertParam param = new CrowdUpsertParam();
        param.setOperator(new Employee("395824", "优比"));
        param.setId(id);
        param.setCrowdName(name);
        param.setCrowdDescription(description);
        if (Objects.nonNull(physicalProfileCode)) {
            ProfileEnum profileEnum = ProfileEnum.valueOf(physicalProfileCode);
            param.setPhysicalProfileCode(profileEnum);
            param.setProfileCode(ProfileCodeEnum.getByProfileEnum(profileEnum));
        }
        if (Objects.nonNull(crowdType)) {
            param.setCrowdType(CrowdTypeEnum.valueOf(crowdType));
        }
        if (Objects.nonNull(conditions)) {
            param.setConditions(JSON.parseObject(conditions, LabelCrowdConditions.class));
        }
        param.setNeedUpdate(needUpdate);
        param.setExpiredDate(new Date(timestamp));
        if (Objects.nonNull(applyScenes)) {
            param.setApplyScenes(JSON.parseArray(applyScenes, GroupApplySceneEnum.class));
        }
        if (Objects.nonNull(groupIds)) {
            param.setGroupIds(JSON.parseArray(groupIds, Long.class));
        }
        if (Objects.nonNull(extInfo)) {
            param.setExtInfo(JSON.parseObject(extInfo));
        }
        if (Objects.isNull(id)) {
            Long crowdId = createCrowd(param);
            Ateye.out.println("create: " + crowdId);
        } else {
            Boolean update = updateCrowd(param);
            Ateye.out.println("update: " + update);
        }
    }

    @AteyeInvoker(description = "填充人群编辑时间", paraDesc = "isSingleTest&crowdId")
    public void fillUpCrowdEditTime(boolean isSingleTest, Long crowdId) throws InterruptedException {
        CrowdMetaInfoQuery crowdMetaInfoQuery = new CrowdMetaInfoQuery();
        crowdMetaInfoQuery.setDeleted((byte) 0);
        if (isSingleTest && Objects.nonNull(crowdId)) {
            crowdMetaInfoQuery.setId(crowdId);
        }
        crowdMetaInfoQuery.setBizRegionCode("public_region");
        List<CrowdMetaInfoDO> crowdMetaInfoDOList = crowdDAO.listAll(crowdMetaInfoQuery);
        for (CrowdMetaInfoDO crowdMetaInfoDO : crowdMetaInfoDOList) {
            if (Objects.nonNull(crowdMetaInfoDO.getEditTime())) {
                continue;
            }
            CrowdMetaInfoDO updateDO = new CrowdMetaInfoDO();
            updateDO.setId(crowdMetaInfoDO.getId());
            updateDO.setEditTime(crowdMetaInfoDO.getGmtCreate());
            crowdDAO.update(updateDO);
            Thread.sleep(10);
        }
    }

}
