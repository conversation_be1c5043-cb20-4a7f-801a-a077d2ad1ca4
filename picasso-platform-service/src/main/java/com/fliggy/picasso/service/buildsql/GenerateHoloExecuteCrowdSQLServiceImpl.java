package com.fliggy.picasso.service.buildsql;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONObject;

import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.entity.condition.GroupConditionDTO;
import com.fliggy.picasso.entity.condition.ParseSQLConditionDTO;
import com.fliggy.picasso.label.MultiLabelConditionExpressionProcessor;
import com.fliggy.picasso.service.buildsql.parseCondition.LabelConditionMappingService;
import com.fliggy.picasso.service.buildsql.parseCondition.ParseConditionService;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.TRIPCROWD_BUCKET;

/**
 * 生成Hologres执行的人群相关SQL实现类
 */
@Service
@HSFProvider(serviceInterface = GenerateHoloExecuteCrowdSQLService.class)
public class GenerateHoloExecuteCrowdSQLServiceImpl implements GenerateHoloExecuteCrowdSQLService {

    @Resource
    ParseConditionService parseConditionService;

    @Resource
    MultiLabelConditionExpressionProcessor multiLabelConditionExpressionProcessor;

    @Resource
    LabelConditionMappingService labelConditionMappingService;

    @Value("${odps.sdk.accessId}")
    private String accessId;
    @Value("${odps.sdk.accessKey}")
    private String accessKey;

    @Switch(description = "导出离线数据至OSS时，每批大小，默认1000万。")
    public static long LOAD_TO_OSS_BATCH_SIZE = 1000_0000L;

    @Override
    public String generateCountSQL(LabelCrowdConditions labelCrowdConditions) {
        String conditionSQL = generateConditionSQL(labelCrowdConditions, false);
        return "select count(*) from (" + conditionSQL + ") t";
    }

    @AteyeInvoker(description = "手动生成Hologres count sql", paraDesc = "表达式")
    public String generateSqlManually(String expression) {
        LabelCrowdConditions labelCrowdConditions = JSONObject.parseObject(expression, LabelCrowdConditions.class);
        return generateCountSQL(labelCrowdConditions);
    }

    @Override
    public String generateLabelCrowdDumpSQL(CrowdMetaInfoDO crowdMetaInfoDO) {
        LabelCrowdConditions conditions = crowdMetaInfoDO.getConditions();
        boolean addShardId = crowdMetaInfoDO.getCrowdAmount() >= 5 * LOAD_TO_OSS_BATCH_SIZE;
        String conditionSQL = generateConditionSQL(conditions, addShardId);
        String dumpSql;
        if (addShardId) {
            dumpSql = String.format("SELECT user_id FROM (%s) dumpSql where hg_shard_id = # ", conditionSQL);
        } else {
            dumpSql = String.format("SELECT user_id FROM (%s) dumpSql ", conditionSQL);
        }
        return String.format("COPY (%s)", dumpSql) +
                String.format(" TO PROGRAM 'hg_dump_to_oss --AccessKeyId %s ", accessId) +
                String.format("--AccessKeySecret %s ", accessKey) +
                String.format("--Endpoint %s ", "oss-cn-zhangjiakou-internal.aliyuncs.com") +
                String.format("--BucketName %s ", TRIPCROWD_BUCKET) +
                String.format("--DirName %s ", String.format("crowd/data/%d/file_#", crowdMetaInfoDO.getId())) +
                String.format("--BatchSize %s ' DELIMITER ',';", LOAD_TO_OSS_BATCH_SIZE);
    }

    @Override
    public String generateNonLabelCrowdDumpSQL(CrowdMetaInfoDO crowdMetaInfoDO) {
        return null;
    }

    @Override
    public String generateConditionSQL(LabelCrowdConditions labelCrowdConditions, Boolean addShardId) {
        //1、解析逻辑表达式
        String entireLogicExpression = parseConditionService.generateEntireLogicExpression(labelCrowdConditions);
        //2、解析圈选条件，生成标签和表组装表达式对应的条件
        List<GroupConditionDTO> groupConditionDTOS = labelConditionMappingService.getLabelAndPhysicalProfileInfo(labelCrowdConditions);
        //3、是否只用到了一张表
        if (addShardId) {
            addShardId = !labelConditionMappingService.judgeUseValidTable(groupConditionDTOS);
        }
        //2、生成标签表达式
        Map<String, String> labelExpression = parseConditionService.generateLabelExpression(labelCrowdConditions, groupConditionDTOS);
        //3、生成表对应的表达式
        List<String> tableExpression = parseConditionService.generateTableExpression(labelCrowdConditions, groupConditionDTOS);

        ParseSQLConditionDTO parseSQLConditionDTO = new ParseSQLConditionDTO();
        parseSQLConditionDTO.setEntireLogicExpression(entireLogicExpression);
        parseSQLConditionDTO.setLabelExpression(labelExpression);
        parseSQLConditionDTO.setGroupConditionDTOS(groupConditionDTOS);
        parseSQLConditionDTO.setTableExpression(tableExpression);
        parseSQLConditionDTO.setAddShardId(addShardId);
        //4、根据1和2生成SQL
        return multiLabelConditionExpressionProcessor.parseSQL(parseSQLConditionDTO);
    }
}
