package com.fliggy.picasso.service.statistic.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fliggy.picasso.common.constants.OdpsConstants;
import com.fliggy.picasso.common.enums.statistic.StatisticEntityTypeEnum;
import com.fliggy.picasso.common.enums.statistic.StatisticTimeDimEnum;
import com.fliggy.picasso.common.statistic.OnlineStatisticInfoVO;
import com.fliggy.picasso.common.utils.CollectionUtils;
import com.fliggy.picasso.convertor.OnlineStatisticInfoConvertor;
import com.fliggy.picasso.dao.OnlineStatisticInfoDO;
import com.fliggy.picasso.dao.OnlineStatisticInfoParam;
import com.fliggy.picasso.mapper.picasso.OnlineStatisticInfoDAO;
import com.fliggy.picasso.service.statistic.OnlineStatisticInfoService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

@Service
public class OnlineStatisticInfoServiceImpl implements OnlineStatisticInfoService {

    @Resource
    private OnlineStatisticInfoDAO onlineStatisticInfoDAO;

    @Override
    public int insert(OnlineStatisticInfoVO onlineStatisticInfoVO) {
        return onlineStatisticInfoDAO.insert(OnlineStatisticInfoConvertor.convertToDO(onlineStatisticInfoVO));
    }

    @Override
    public int batchInsert(List<OnlineStatisticInfoVO> onlineStatisticInfoVOList) {
        int cnt = 0;
        for (OnlineStatisticInfoVO onlineStatisticInfoVO : onlineStatisticInfoVOList) {
            cnt += insert(onlineStatisticInfoVO);
        }
        return cnt;
    }

    @Override
    public int update(OnlineStatisticInfoVO onlineStatisticInfoVO) {
        return onlineStatisticInfoDAO.updateByPrimaryKeySelective(OnlineStatisticInfoConvertor.convertToDO(onlineStatisticInfoVO));
    }

    @Override
    public int batchUpdate(List<OnlineStatisticInfoVO> onlineStatisticInfoVOList) {
        int cnt = 0;
        for (OnlineStatisticInfoVO onlineStatisticInfoVO : onlineStatisticInfoVOList) {
            cnt += update(onlineStatisticInfoVO);
        }
        return cnt;
    }

    @Override
    public List<OnlineStatisticInfoVO> queryByEntityIdAndType(Long entityId, StatisticEntityTypeEnum entityType) {
        if (Objects.isNull(entityId) || Objects.isNull(entityType)) {
            return Lists.newArrayList();
        }

        OnlineStatisticInfoParam param = new OnlineStatisticInfoParam();
        OnlineStatisticInfoParam.Criteria criteria = param.createCriteria();
        criteria.andEntityIdEqualTo(entityId);
        criteria.andEntityTypeEqualTo(entityType.getCode());
        List<OnlineStatisticInfoDO> onlineStatisticInfoDOList = onlineStatisticInfoDAO.selectByParam(param);
        if (CollectionUtils.isNullOrEmpty(onlineStatisticInfoDOList)) {
            return Lists.newArrayList();
        }
        return onlineStatisticInfoDOList.stream().map(OnlineStatisticInfoConvertor::convertToVO).collect(Collectors.toList());
    }

    @Override
    public Long queryStatisticValByEntityAndDim(Long entityId, StatisticEntityTypeEnum entityType, StatisticTimeDimEnum statisticDim) {
        if (Objects.isNull(entityId) || Objects.isNull(entityType) || Objects.isNull(statisticDim)) {
            return null;
        }

        OnlineStatisticInfoParam param = new OnlineStatisticInfoParam();
        OnlineStatisticInfoParam.Criteria criteria = param.createCriteria();
        criteria.andEntityIdEqualTo(entityId);
        criteria.andEntityTypeEqualTo(entityType.getCode());
        criteria.andStatisticDimEqualTo(statisticDim.getCode());
        List<OnlineStatisticInfoDO> onlineStatisticInfoDOList = onlineStatisticInfoDAO.selectByParam(param);
        if (CollectionUtils.isNullOrEmpty(onlineStatisticInfoDOList)) {
            return null;
        }
        return onlineStatisticInfoDOList.get(0).getStatisticVal();
    }

    @Override
    public Map<Long, Long> batchQueryStatisticValByEntityAndDim(List<Long> entityIdList,
        StatisticEntityTypeEnum entityType, StatisticTimeDimEnum statisticDim) {
        if (CollectionUtils.isNullOrEmpty(entityIdList) || Objects.isNull(entityType) || Objects.isNull(statisticDim)) {
            return Maps.newHashMap();
        }

        OnlineStatisticInfoParam param = new OnlineStatisticInfoParam();
        OnlineStatisticInfoParam.Criteria criteria = param.createCriteria();
        criteria.andEntityIdIn(entityIdList);
        criteria.andEntityTypeEqualTo(entityType.getCode());
        criteria.andStatisticDimEqualTo(statisticDim.getCode());
        List<OnlineStatisticInfoDO> onlineStatisticInfoDOList = onlineStatisticInfoDAO.selectByParam(param);
        if (CollectionUtils.isNullOrEmpty(onlineStatisticInfoDOList)) {
            return Maps.newHashMap();
        }
        return onlineStatisticInfoDOList.stream().collect(Collectors.toMap(OnlineStatisticInfoDO::getEntityId, OnlineStatisticInfoDO::getStatisticVal));
    }

    @Override
    public List<OnlineStatisticInfoVO> findAllLastDateStatisticInfo() {
        OnlineStatisticInfoParam param = new OnlineStatisticInfoParam();
        OnlineStatisticInfoParam.Criteria criteria = param.createCriteria();
        String ds = DateTime.now().minusDays(1).toString(OdpsConstants.ODPS_PHYSICAL_PROFILE_PARTITION_FORMAT);
        criteria.andStatisticDateLessThan(ds);
        criteria.andStatisticValGreaterThan(0L);
        List<OnlineStatisticInfoDO> onlineStatisticInfoDOList = onlineStatisticInfoDAO.selectByParam(param);
        if (CollectionUtils.isNullOrEmpty(onlineStatisticInfoDOList)) {
            return Lists.newArrayList();
        }
        return onlineStatisticInfoDOList.stream().map(OnlineStatisticInfoConvertor::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<OnlineStatisticInfoVO> findAllValidHeavyStatisticInfo(StatisticEntityTypeEnum type) {
        if (Objects.isNull(type)) {
            return Lists.newArrayList();
        }

        OnlineStatisticInfoParam param = new OnlineStatisticInfoParam();
        OnlineStatisticInfoParam.Criteria criteria = param.createCriteria();
        criteria.andEntityTypeEqualTo(type.getCode());
        criteria.andIsValidEqualTo((byte) 1);
        criteria.andIsHeavyEqualTo((byte) 1);
        criteria.andStatisticDimEqualTo(StatisticTimeDimEnum.MONTH.getCode());
        criteria.andStatisticValEqualTo(0L);
        List<OnlineStatisticInfoDO> onlineStatisticInfoDOList = onlineStatisticInfoDAO.selectByParam(param);
        if (CollectionUtils.isNullOrEmpty(onlineStatisticInfoDOList)) {
            return Lists.newArrayList();
        }
        return onlineStatisticInfoDOList.stream().map(OnlineStatisticInfoConvertor::convertToVO).collect(Collectors.toList());
    }

    @AteyeInvoker(description = "删除", paraDesc = "id")
    @Override
    public Boolean delete(Long id) {
        return onlineStatisticInfoDAO.deleteByPrimaryKey(id) > 0;
    }

    @Override
    public Boolean batchDelete(List<Long> ids) {
        ids.forEach(id -> onlineStatisticInfoDAO.deleteByPrimaryKey(id));
        return true;
    }

}
