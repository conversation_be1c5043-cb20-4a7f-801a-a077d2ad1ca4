package com.fliggy.picasso.service.record;

import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.crowd.record.AsyncTaskRecordVO;
import com.fliggy.picasso.common.enums.AsyncTaskRecordTypeEnum;
import com.fliggy.picasso.common.enums.AsyncTaskStatusEnum;

import java.util.List;

public interface AsyncTaskRecordService {

    /**
     * 插入一条异步任务记录
     * @param asyncTaskRecordVO
     * @return
     */
    int insert(AsyncTaskRecordVO asyncTaskRecordVO);

    /**
     * 更新一条异步任务记录
     * @param asyncTaskRecordVO
     * @return
     */
    int update(AsyncTaskRecordVO asyncTaskRecordVO);

    /**
     * 根据id查询记录
     * @param id
     * @return
     */
    AsyncTaskRecordVO queryById(Long id);

    /**
     * 根据id批量查询记录
     * @param ids
     * @return
     */
    List<AsyncTaskRecordVO> queryByIds(List<Long> ids);

    /**
     * 根据entity查询记录
     * @param entityType
     * @param entity
     * @return
     */
    AsyncTaskRecordVO queryByEntity(AsyncTaskRecordTypeEnum entityType, String entity);

    /**
     * 根据类型和状态查询记录
     * @param type
     * @param status
     * @return
     */
    List<AsyncTaskRecordVO> queryByTypeAndStatus(AsyncTaskRecordTypeEnum type, AsyncTaskStatusEnum status);

    /**
     * 根据类型和状态列表查询记录
     * @param type
     * @param statusList
     * @return
     */
    List<AsyncTaskRecordVO> queryByTypeAndStatusList(AsyncTaskRecordTypeEnum type, List<AsyncTaskStatusEnum> statusList);

    /**
     * 查询最近10条记录
     * @param operator
     * @param type
     * @return
     */
    List<AsyncTaskRecordVO> listLatestTop10(Employee operator, AsyncTaskRecordTypeEnum type);

    /**
     * 是否guid已经校验成功过
     * @param guid
     * @return
     */
    Boolean isGuidSucceed(String guid);

    /**
     * 查询guid最新的未失败的任务状态
     * @param guid
     * @param operator
     * @return
     */
    AsyncTaskStatusEnum queryLatestNotFailedTaskStatus(String guid, Employee operator);

    /**
     * 根据id删除记录
     * @param id
     * @return
     */
    int deleteById(Long id);
}
