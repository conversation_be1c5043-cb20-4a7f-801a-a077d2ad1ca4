package com.fliggy.picasso.service.dingtalk;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.fliggy.crowd.service.TripCrowdCommonService;
import com.fliggy.crowd.service.domain.profile.LabelQueryDTO;
import com.fliggy.crowd.service.domain.profile.ProfileQueryRequest;
import com.fliggy.crowd.service.domain.profile.ProfileValue;
import com.fliggy.crowd.service.open.api.PicassoCommonService;
import com.fliggy.crowd.service.result.TripCrowdCommonResult;
import com.fliggy.picasso.client.service.DingTalkRobotService;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions.LabelGroup;
import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions.LabelValue;
import com.fliggy.picasso.common.domain.crowd.build.CrowdBuildDagConfigDO;
import com.fliggy.picasso.common.domain.crowd.build.GroupBuildDagNode;
import com.fliggy.picasso.common.domain.label.enumvalue.EnumDimMetaInfoDO;
import com.fliggy.picasso.common.domain.label.enumvalue.vo.LabelEnumValueCreateVO;
import com.fliggy.picasso.common.enums.crowd.CrowdBuildErrorEnum;
import com.fliggy.picasso.common.enums.group.GroupApplySceneEnum;
import com.fliggy.picasso.common.enums.group.GroupBuildDagStatusEnum;
import com.fliggy.picasso.common.enums.group.GroupStatusEnum;
import com.fliggy.picasso.common.odps.domain.OdpsInstanceTask;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.common.utils.JSONUtils;
import com.fliggy.picasso.common.utils.NumberUtils;
import com.fliggy.picasso.client.entity.label.LabelInfoDTO;
import com.fliggy.picasso.group.crowd.dag.CrowdBuildDagConfigService;
import com.fliggy.picasso.offline.OdpsService;
import com.fliggy.picasso.service.crowd.CrowdService;
import com.fliggy.picasso.service.enumvalue.EnumDimMetaInfoDTO;
import com.fliggy.picasso.service.enumvalue.EnumDimMetaInfoService;
import com.fliggy.picasso.service.enumvalue.LabelEnumValueService;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.Switch;
import com.taobao.trip.platform.protocol.TripCommonPlatformResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fliggy.picasso.common.Constant.ODPS_PROJECT_TRIP_PROFILE;
import static com.fliggy.picasso.common.constants.OdpsConstants.ODPS_EXECUTE_PROJECT;

@Service
@HSFProvider(serviceInterface = DingTalkRobotService.class,
        serviceVersion = "1.0.0",
        clientTimeout = 60000,
        serviceName = "DingTalkRobotService")
public class DingTalkRobotServiceImpl implements DingTalkRobotService {

    @Switch(description = "支持手动添加的枚举id列表")
    public String enumMetaIdsSupportAddEnumData = "";

    private final TripCrowdCommonService tripCrowdCommonService;
    private final PicassoCommonService picassoCommonService;
    private final CrowdService crowdService;
    private final CrowdBuildDagConfigService crowdBuildDagConfigService;
    private final LabelInfoService labelInfoService;
    private final EnumDimMetaInfoService enumDimMetaInfoService;
    private final LabelEnumValueService labelEnumValueService;
    private final OdpsService odpsService;

    /**
     * 在机器人工厂https://robot.alibaba-inc.com/robot/servicesList.htm?appId=47978，服务的输出参数
     * 要求返回结果中必须有这个key
     */
    private final static List<String> CROWD_INFO_KEYS = Lists.newArrayList("crowdName", "crowdStatus", "needUpdateDesc", "creator"
            , "crowdTypeDesc", "dagGmtModified", "crowdAmount", "realTimeDesc", "expiredDateFormat", "crowdDescription"
            , "dagStatus", "applySceneDescs", "deleted", "errorDetailMsg", "errorCodeDesc", "dagNodes", "dagGmtCreate");
    private final static List<String> LABEL_INFO_KEYS = Lists.newArrayList("status", "name", "realTimeLabel", "dimEnumMetaId"
            , "deleted", "sourceConfig", "enumDimCode", "dataType", "enumDimName", "source", "description", "creator", "dataOwner");
    private final static List<String> USER_MATCH_KEYS = Lists.newArrayList("matchResult", "labelResult");

    @Autowired
    public DingTalkRobotServiceImpl(TripCrowdCommonService tripCrowdCommonService
            , PicassoCommonService picassoCommonService
            , CrowdService crowdService
            , CrowdBuildDagConfigService crowdBuildDagConfigService
            , LabelInfoService labelInfoService
            , LabelEnumValueService labelEnumValueService
            , EnumDimMetaInfoService enumDimMetaInfoService
            , OdpsService odpsService
    ) {
        this.tripCrowdCommonService = tripCrowdCommonService;
        this.picassoCommonService = picassoCommonService;
        this.crowdService = crowdService;
        this.crowdBuildDagConfigService = crowdBuildDagConfigService;
        this.labelInfoService = labelInfoService;
        this.labelEnumValueService = labelEnumValueService;
        this.enumDimMetaInfoService = enumDimMetaInfoService;
        this.odpsService = odpsService;
    }

    @Override
    public JSONObject userMatch(Long userId, Long crowdId) {
        if (!NumberUtils.validLong(userId) || !NumberUtils.validLong(crowdId)) {
            return fail("参数错误：userId=" + userId + ", crowdId=" + crowdId);
        }

        JSONObject result = new JSONObject();
        try {
            CrowdMetaInfoDO crowdMetaInfoDO = crowdService.queryById(crowdId);
            if (Objects.isNull(crowdMetaInfoDO)) {
                return fail("人群不存在，请检查人群id是否正确，id=" + crowdId);
            }

            // 匹配结果
            TripCommonPlatformResult<Boolean> matchResult = tripCrowdCommonService.isUidInCrowd(userId, crowdId);
            if (!matchResult.isSuccess()) {
                return fail("匹配判定失败：" + matchResult.getMsg());
            }
            result.put("matchResult", String.valueOf(matchResult.getData()));

            // 人群标签
            result.put("labelResult", queryUserCrowdLabelValue(userId, crowdMetaInfoDO));

            // key值补齐
            JSONUtils.putEmptyStrIfNull(result, USER_MATCH_KEYS);

            return result;
        } catch (Exception e) {
            return fail("服务异常：" + e.getClass() + ", " + e.getMessage());
        }
    }

    @Override
    public JSONObject crowdInfo(Long crowdId) {
        if (!NumberUtils.validLong(crowdId)) {
            return fail("参数错误：crowdId=" + crowdId);
        }

        try {
            CrowdMetaInfoDO crowdMetaInfoDO = crowdService.queryById(crowdId);
            if (Objects.isNull(crowdMetaInfoDO)) {
                return fail("人群不存在，请检查人群id是否正确，id=" + crowdId);
            }

            // 人群基础信息：人群名称、人群描述、人群负责人、圈选类型、实时/离线、使用场景
            JSONObject result = JSON.parseObject(JSON.toJSONString(crowdMetaInfoDO));
            putCrowdDetail(result, crowdMetaInfoDO);

            // 构建信息：开始时间、完成时间、状态
            CrowdBuildDagConfigDO query = new CrowdBuildDagConfigDO();
            query.setCrowdId(crowdId);
            List<CrowdBuildDagConfigDO> crowdBuildDags = crowdBuildDagConfigService.pageQuery(query, 1, 1);
            CrowdBuildDagConfigDO configDO = crowdBuildDags.get(0);
            if (CollectionUtils.isNotEmpty(crowdBuildDags)) {
                result.put("dagGmtCreate", DateUtils.formatDateToYYYYMMDDHHMMSS(configDO.getGmtCreate()));
                result.put("dagGmtModified", DateUtils.formatDateToYYYYMMDDHHMMSS(configDO.getGmtModified()));
                result.put("dagStatus", configDO.getDagStatus());
                result.put("dagNodes", configDO.getDagNodes());
            }

            // 错误信息：错误码-CrowdBuildErrorEnum、详情
            if (GroupStatusEnum.ERROR.equals(crowdMetaInfoDO.getCrowdStatus())) {
                CrowdBuildErrorEnum errorCode = CrowdBuildErrorEnum.find(crowdMetaInfoDO.getErrorCode());
                if (Objects.nonNull(errorCode) && !CrowdBuildErrorEnum.SUCCESS.equals(errorCode)) {
                    result.put("errorCodeDesc", errorCode.getDesc());
                }

                String extInfo = crowdMetaInfoDO.getExtInfo();
                JSONObject extJson = JSONObject.parseObject(extInfo);
                if (Objects.nonNull(extJson) && extJson.containsKey("crowdBuildRecord") && extJson.getJSONObject("crowdBuildRecord").containsKey("errMsg")) {
                    result.put("errorDetailMsg", extJson.getJSONObject("crowdBuildRecord").getString("errMsg"));
                }
                // 拿到失败的node，返回详细的odps任务异常信息
                if (CollectionUtils.isNotEmpty(configDO.getDagNodes())) {
                    List<GroupBuildDagNode> dagNodes = configDO.getDagNodes();
                    GroupBuildDagNode crowdBuildDagNode = dagNodes.stream().filter(p -> p.getNodeStatus() == GroupBuildDagStatusEnum.FAILED).findFirst().get();
                    OdpsInstanceTask odpsInstanceTask = crowdBuildDagNode.getOdpsInstanceTask();
                    result.put("odpsTaskStatus", odpsInstanceTask.getInstanceStatus());
                    result.put("odpsTaskMsg", odpsService.getInstanceFirstResultText(ODPS_EXECUTE_PROJECT, odpsInstanceTask.getInstanceId()));
                }
            }

            // key值补齐
            JSONUtils.putEmptyStrIfNull(result, CROWD_INFO_KEYS);

            return result;
        } catch (Exception e) {
            return fail("服务异常：" + e.getClass() + ", " + e.getMessage());
        }
    }

    @Override
    public JSONObject labelInfo(String labelCode) {
        if (StringUtils.isBlank(labelCode)) {
            return fail("参数错误：labelCode为空");
        }

        try {
            LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(labelCode);
            if (Objects.isNull(labelInfoDTO)) {
                return fail("标签不存在，请检查标签code是否正确，code=" + labelCode);
            }

            JSONObject result = JSON.parseObject(JSON.toJSONString(labelInfoDTO));

            // 枚举信息
            if (Objects.nonNull(labelInfoDTO.getDimEnumMetaId())) {
                EnumDimMetaInfoDTO enumDimMetaInfoDO = enumDimMetaInfoService.findById(labelInfoDTO.getDimEnumMetaId());
                if (Objects.nonNull(enumDimMetaInfoDO)) {
                    result.put("enumDimCode", enumDimMetaInfoDO.getCode());
                    result.put("enumDimName", enumDimMetaInfoDO.getName());
                    result.put("enumDimType", enumDimMetaInfoDO.getType());
                    result.put("enumDimConfig", enumDimMetaInfoDO.getExtInfo());
                    result.put("enumDimStatus", enumDimMetaInfoDO.getDeleted().equals(new Byte("1")) ? "下线" : "在线");
                }
            }
            // key值补齐
            JSONUtils.putEmptyStrIfNull(result, LABEL_INFO_KEYS);

            return result;
        } catch (Exception e) {
            return fail("服务异常：" + e.getClass() + ", " + e.getMessage());
        }
    }

    @Override
    public JSONObject addPageScene(String crowdIds, Boolean add) {
        JSONObject jsonObject = new JSONObject();
        JSONObject detail = new JSONObject();
        if (StringUtils.isBlank(crowdIds)) {
            jsonObject.put("result", "人群为空!");
            return jsonObject;
        }
        doPage(crowdIds, add, detail);
        jsonObject.put("result", "success");
        jsonObject.put("detail", detail.toJSONString());
        return jsonObject;
    }


    @Override
    public JSONObject sqlExecute(String sql) {
        JSONObject jsonObject = new JSONObject();
        try {
            Pair<String, String> sqlExecute = odpsService.asyncExecute(ODPS_PROJECT_TRIP_PROFILE, sql, "sqlExecute", 5);
            jsonObject.put("result", "success");
            jsonObject.put("logview", sqlExecute.getRight());
            jsonObject.put("instanceId", sqlExecute.getLeft());
        } catch (Exception e) {
            jsonObject.put("result", "fail");
            jsonObject.put("detail", e.getMessage());
        }
        return jsonObject;
    }

    /**
     * 人群转交
     *
     * @param crowdIds
     * @param empId
     * @param empName
     */
    @Override
    public JSONObject transferOwner(String crowdIds, String empId, String empName) {
        List<Long> crowdLongIds = NumberUtils.split(crowdIds);
        if (com.taobao.ateye.util.CollectionUtils.isEmpty(crowdLongIds)) {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        JSONObject detail = new JSONObject();

        for (Long crowdLongId : crowdLongIds) {
            CrowdMetaInfoDO crowdMetaInfoDO = crowdService.queryById(crowdLongId);
            if (Objects.isNull(crowdMetaInfoDO)) {
                detail.put(String.valueOf(crowdLongId), "人群不存在.");
                continue;
            }

            CrowdMetaInfoDO updateCrowd = new CrowdMetaInfoDO();
            updateCrowd.setId(crowdLongId);
            Employee employee = new Employee(empId, empName);
            updateCrowd.setCreator(employee);
            Boolean singleResult = crowdService.updateAcrossNonFront(updateCrowd);
            detail.put(String.valueOf(crowdLongId), String.valueOf(singleResult));
        }
        jsonObject.put("result", "success");
        jsonObject.put("detail", detail.toJSONString());
        return jsonObject;
    }

    @Override
    public JSONObject addEnumDataByEnumCode(String enumCode, String code, String desc) {
        EnumDimMetaInfoDO enumDimMetaInfoDO = enumDimMetaInfoService.findByCode(enumCode);
        if (Objects.isNull(enumDimMetaInfoDO)) {
            return fail(enumCode + " 枚举不存在！");
        }

        if (!enumMetaIdsSupportAddEnumData.contains(enumDimMetaInfoDO.getId().toString())) {
            return fail(enumCode + " 枚举不支持自动添加枚举值，请@技术支持！");
        }

        try {
            LabelEnumValueCreateVO labelEnumValueCreateVO = generateLabelEnumValueCreateVO(enumDimMetaInfoDO.getId(), code, desc);
            Boolean res = Objects.nonNull(labelEnumValueService.addLabelEnumDimData(labelEnumValueCreateVO));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("result", res);
            return jsonObject;
        } catch (Exception e) {
            return fail("枚举值添加失败！" + e.getMessage());
        }
    }

    @Override
    public JSONObject addEnumDataByLabelCode(String labelCode, String code, String desc) {
        LabelInfoDTO labelInfoDTO = labelInfoService.findByLabelCode(labelCode);
        if (Objects.isNull(labelInfoDTO)) {
            return fail("标签不存在！");
        }

        if (!enumMetaIdsSupportAddEnumData.contains(labelInfoDTO.getDimEnumMetaId().toString())) {
            return fail(labelCode + " 标签不支持自动添加枚举值，请@技术支持！");
        }

        try {
            LabelEnumValueCreateVO labelEnumValueCreateVO = generateLabelEnumValueCreateVO(labelInfoDTO.getDimEnumMetaId(), code, desc);
            Boolean res = Objects.nonNull(labelEnumValueService.addLabelEnumDimData(labelEnumValueCreateVO));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("result", res);
            return jsonObject;
        } catch (Exception e) {
            return fail("标签枚举值添加失败！" + e.getMessage());
        }
    }

    private LabelEnumValueCreateVO generateLabelEnumValueCreateVO(Long enumMetaId, String code, String desc) {
        LabelEnumValueCreateVO labelEnumValueCreateVO = new LabelEnumValueCreateVO();
        labelEnumValueCreateVO.setParentIndex(enumMetaId + "_0");
        labelEnumValueCreateVO.setEnumCode(code);
        labelEnumValueCreateVO.setEnumDesc(Objects.isNull(desc) ? code : desc);
        return labelEnumValueCreateVO;
    }


    private void doPage(String crowdIds, Boolean add, JSONObject detail) {
        String[] crowdIdList = crowdIds.split(",");
        for (String crowdIdStr : crowdIdList) {
            long crowdId = Long.parseLong(crowdIdStr);
            CrowdMetaInfoDO crowdMetaInfoDO = crowdService.queryById(crowdId);
            if (crowdMetaInfoDO == null) {
                detail.put(String.valueOf(crowdId), "获取人群信息失败，人群不存在！");
                continue;
            }

            List<String> currentApplyScene = crowdMetaInfoDO.getCrowdApplyScene();
            String CROWD_APPLY_SCENE_PAGE = GroupApplySceneEnum.PAGE.getName();
            if (add) {
                if (!currentApplyScene.contains(CROWD_APPLY_SCENE_PAGE)) {
                    currentApplyScene.add(CROWD_APPLY_SCENE_PAGE);
                    crowdMetaInfoDO.setCrowdApplyScene(currentApplyScene);
                    crowdMetaInfoDO.setCrowdStatus(GroupStatusEnum.INIT);
                    crowdService.updateAcrossNonFront(crowdMetaInfoDO);
                    detail.put(String.valueOf(crowdId), "添加分页查询子任务成功！");
                } else {
                    detail.put(String.valueOf(crowdId), "已包含分页查询子任务！");
                }
            } else {
                if (!currentApplyScene.contains(CROWD_APPLY_SCENE_PAGE)) {
                    detail.put(String.valueOf(crowdId), "不包含分页查询子任务！");
                } else {
                    currentApplyScene.remove(CROWD_APPLY_SCENE_PAGE);
                    crowdMetaInfoDO.setCrowdApplyScene(currentApplyScene);
                    crowdService.updateAcrossNonFront(crowdMetaInfoDO);
                    detail.put(String.valueOf(crowdId), "删除分页查询子任务成功！");
                }
            }
        }
    }

    private JSONObject fail(String msg) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msg", msg);
        return jsonObject;
    }

    private Map<String, String> queryUserCrowdLabelValue(Long userId, CrowdMetaInfoDO crowdMetaInfoDO) {
        Map<String, String> result = Maps.newConcurrentMap();

        LabelCrowdConditions labelCrowdConditions = crowdMetaInfoDO.getConditions();
        ProfileQueryRequest profileQueryRequest = new ProfileQueryRequest();
        profileQueryRequest.setEntityId(userId.toString());
        List<LabelQueryDTO> labelQueryDTOS = Lists.newArrayList();
        Map<String, String> labelMap = Maps.newHashMap();
        if (Objects.nonNull(labelCrowdConditions) && CollectionUtils.isNotEmpty(labelCrowdConditions.getGroup())) {
            for (LabelGroup labelGroup : labelCrowdConditions.getGroup()) {
                if (CollectionUtils.isNotEmpty(labelGroup.getLabel())) {
                    for (LabelValue labelValue : labelGroup.getLabel()) {
                        if (StringUtils.isNotBlank(labelValue.getName()) && !labelMap.containsKey(labelValue.getName())) {
                            labelMap.put(labelValue.getName(), labelValue.getDesc());
                            LabelQueryDTO labelQueryDTO = new LabelQueryDTO();
                            labelQueryDTO.setLabelName(labelValue.getName());
                            labelQueryDTOS.add(labelQueryDTO);
                        }
                    }
                }
            }
        }
        profileQueryRequest.setLabelQueryDTOS(labelQueryDTOS);

        if (CollectionUtils.isEmpty(labelQueryDTOS)) {
            return result;
        }


        TripCrowdCommonResult<Map<String, ProfileValue>> profileResult = picassoCommonService.queryProfiles(profileQueryRequest);
        if (!profileResult.isSuccess() || MapUtils.isEmpty(profileResult.getData())) {
            return result;
        }

        for (Entry<String, ProfileValue> entry : profileResult.getData().entrySet()) {
            String value = Objects.nonNull(entry.getValue()) ? entry.getValue().getValue() : StringUtils.EMPTY;
            String key = labelMap.containsKey(entry.getKey()) && StringUtils.isNotBlank(labelMap.get(entry.getKey()))
                    ? labelMap.get(entry.getKey()) : entry.getKey();
            result.put(key, String.valueOf(value));
        }

        return result;
    }

    /**
     * 人群语义化
     *
     * @param jsonObject      json
     * @param crowdMetaInfoDO 人群信息
     */
    private void putCrowdDetail(JSONObject jsonObject, CrowdMetaInfoDO crowdMetaInfoDO) {
        if (Objects.isNull(jsonObject) || Objects.isNull(crowdMetaInfoDO)) {
            return;
        }

        // 人群信息语义化
        if (Objects.nonNull(crowdMetaInfoDO.getNeedUpdate())) {
            String updateDesc = crowdMetaInfoDO.getNeedUpdate().equals(new Byte("1")) ? "日更新" : "静态";
            jsonObject.put("needUpdateDesc", updateDesc);
        }
        if (CollectionUtils.isNotEmpty(crowdMetaInfoDO.getCrowdApplyScene())) {
            List<String> applySceneDescs = Lists.newArrayList();
            for (String s : crowdMetaInfoDO.getCrowdApplyScene()) {
                try {
                    GroupApplySceneEnum sceneEnum = GroupApplySceneEnum.fromName(s);
                    applySceneDescs.add(sceneEnum.getDesc());
                } catch (Exception e) {
                    applySceneDescs.add(s);
                }

            }
            jsonObject.put("applySceneDescs", applySceneDescs);
        }
        if (Objects.nonNull(crowdMetaInfoDO.getCrowdType())) {
            jsonObject.put("crowdTypeDesc", crowdMetaInfoDO.getCrowdType().getDesc());
        }
        if (Objects.nonNull(crowdMetaInfoDO.getExpiredDate())) {
            jsonObject.put("expiredDateFormat", DateUtils.formatDateToYYYYMMDDHHMMSS(crowdMetaInfoDO.getExpiredDate()));
        }
        if (Objects.nonNull(crowdMetaInfoDO.getRealTime())) {
            String realTimeDesc = crowdMetaInfoDO.doesRealTime() ? "实时" : "离线";
            jsonObject.put("realTimeDesc", realTimeDesc);
        }
    }
}
