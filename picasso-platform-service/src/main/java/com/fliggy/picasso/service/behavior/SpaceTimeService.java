package com.fliggy.picasso.service.behavior;

import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.common.enums.spacetime.SpaceTimeBehaviorType;
import com.fliggy.picasso.common.enums.spacetime.SpaceTimeLabelTimeType;

import java.util.List;
import java.util.Map;

public interface SpaceTimeService {

    /**
     * 查询时间类型
     * @return
     */
    Map<String, String> queryTimeType();

    /**
     * 查询行为类型
     * @return
     */
    Map<String, String> queryBehaviorType(Boolean filterNoDest);

    /**
     * 查询行为类型
     * @param filterNoDest
     * @param timeType
     * @return
     */
    Map<String, String> queryBehaviorTypeWithTimeType(Boolean filterNoDest, SpaceTimeLabelTimeType timeType);

    /**
     * 查询行为类型对应的附加条件
     * @param behaviorType
     * @return
     */
    List<LabelCrowdConditions.LabelParams> queryConditionType(String behaviorType);
}
