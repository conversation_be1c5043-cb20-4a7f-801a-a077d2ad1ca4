package com.fliggy.picasso.service.analysis;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.analysis.AnalysisV2StatusEnum;
import com.fliggy.picasso.client.entity.analysis.AnalysisDTO;
import com.fliggy.picasso.client.entity.analysis.AnalysisMetaDTO;
import com.fliggy.picasso.client.entity.analysis.AnalysisStatusDTO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class AnalysisV2DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     *   创建时间
     */
    @Getter
    @Setter
    private Date gmtCreate;

    /**
     *   修改时间
     */
    @Getter
    @Setter
    private Date gmtModified;

    /**
     *   父id
     */
    @Getter
    @Setter
    private Long parentId;

    /**
     *   分析名称
     */
    @Getter
    @Setter
    private String name;

    /**
     *   人群名称
     */
    @Getter
    @Setter
    private String crowdName;

    /**
     *   分析描述
     */
    @Getter
    @Setter
    private String description;

    /**
     *   分析类型，预留字段，默认为1洞察分析
     */
    @Getter
    @Setter
    private String type;

    /**
     *   分析任务进度
     */
    @Getter
    @Setter
    private String status;

    /**
     *   创建人
     */
    @Getter
    @Setter
    private String creator;

    /**
     *   是否删除
     */
    @Getter
    @Setter
    private Byte isDeleted;

    /**
     *   人群id
     */
    @Getter
    @Setter
    private Long crowdId;

    /**
     *   错误/重试次数
     */
    @Getter
    @Setter
    private Byte failTimes;

    /**
     *   分析元数据
     */
    @Getter
    @Setter
    private String meta;

    /**
     *   分析结果数据
     */
    @Getter
    @Setter
    private String result;

    /**
     *   扩展信息
     */
    @Getter
    @Setter
    private String extInfo;

    /**
     *   父分析元数据
     */
    @Getter
    @Setter
    private String parentMeta;

    /**
     *   父分析元数据
     */
    @Getter
    @Setter
    private Boolean isAdmin;

    public static AnalysisDTO convert(AnalysisV2DTO analysisV2DTO) {
        AnalysisDTO result = new AnalysisDTO();
        BeanUtils.copyProperties(analysisV2DTO, result);
        result.setStatus(AnalysisStatusDTO.getByStatus(analysisV2DTO.getStatus()));
        result.setIsDeleted(Objects.nonNull(analysisV2DTO.getIsDeleted()) && analysisV2DTO.getIsDeleted() == 1);
        if (StringUtils.isNotBlank(analysisV2DTO.getMeta())) {
            result.setMeta(JSON.parseObject(analysisV2DTO.getMeta(), AnalysisMetaDTO.class));
        }
        if (StringUtils.isNotBlank(analysisV2DTO.getParentMeta())) {
            result.setParentMeta(JSON.parseObject(analysisV2DTO.getParentMeta(), AnalysisMetaDTO.class));
        }
        return result;
    }

    public Boolean doesDeleted() {
        return Objects.nonNull(this.isDeleted) && this.isDeleted == 1;
    }

}