package com.fliggy.picasso.service.analysis;

import com.alibaba.schedulerx.shade.scala.annotation.meta.param;
import com.fliggy.picasso.analysis.AnalysisV2HandleParam;
import com.fliggy.picasso.analysis.AnalysisV2HandleResult;
import com.fliggy.picasso.analysis.AnalysisV2Meta;
import com.fliggy.picasso.dao.AnalysisV2DO;

import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public interface AnalysisV2Service {
    /**
     * 根据参数统计总数
     * @param param
     */
    long count(AnalysisV2Parameter param);

    /**
     * 根据参数查询
     * @param param
     */
    AnalysisV2DTO find(AnalysisV2Parameter param);

    /**
     * 列表查询
     * @param param
     */
    List<AnalysisV2DTO> list(AnalysisV2Parameter param);

    /**
     * 列表摘要查询
     * @param param
     */
    List<AnalysisV2DTO> listBrief(AnalysisV2Parameter param);

    /**
     * 创建
     * @param analysisV2CreateRequest
     * @return
     */
    Long create(AnalysisV2CreateRequest analysisV2CreateRequest);

    /**
     * 常规插入
     *
     * @param record 数据
     * @return 主键
     */
    Long insert(AnalysisV2DO record);

    /**
     * 选择性修改
     * @param dto
     * @param param
     */
    void updateSelective(AnalysisV2DTO dto, AnalysisV2Parameter param);

    int updateSelectiveById(AnalysisV2CreateRequest analysisV2CreateRequest);

    int updateSelective(AnalysisV2DO analysisV2DO, String oldStatus);

    AnalysisV2DO findById(Long id);

    AnalysisV2DTO findDtoById(Long id);

    List<AnalysisV2DTO> findByIds(List<Long> ids);

    List<AnalysisV2DO> findSelfAndChildById(Long id);

    List<AnalysisV2DTO> findByPoolId(Long id);

        /**
         * 初始化分析任务
         * @param analysisV2DO
         * @return
         */
    AnalysisV2HandleParam initAnalysis(AnalysisV2DO analysisV2DO);


    /**
     * 执行分析
     * @param analysisV2DO
     * @return
     */
    AnalysisV2HandleResult handleAnalysisV2(AnalysisV2DO analysisV2DO);

    List<AnalysisV2DO> selectRecordByStatusAndFailTimes(String status, Integer failTimes);

    Boolean reRun(Long param);

    Boolean delById(Long id);
}