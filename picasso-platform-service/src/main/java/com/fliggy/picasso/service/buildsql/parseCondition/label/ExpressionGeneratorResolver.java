package com.fliggy.picasso.service.buildsql.parseCondition.label;

import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 判断用哪种标签逻辑表达式生成器的解析器
 */
@Component
public class ExpressionGeneratorResolver {
    Map<LabelBizDataTypeEnum, BaseLabelExpressionGenerator> map = new HashMap<>();

    public void register(LabelBizDataTypeEnum labelType, BaseLabelExpressionGenerator labelExpressionGenerator) {
        this.map.put(labelType, labelExpressionGenerator);
    }

    public BaseLabelExpressionGenerator resolve(LabelBizDataTypeEnum labelType) {
        return this.map.get(labelType);
    }
}
