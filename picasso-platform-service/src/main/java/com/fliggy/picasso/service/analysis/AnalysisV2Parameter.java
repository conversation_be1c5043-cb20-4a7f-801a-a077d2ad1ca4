package com.fliggy.picasso.service.analysis;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class AnalysisV2Parameter implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     *   父id
     */
    @Getter
    @Setter
    private Long parentId;

    /**
     *   分析名称
     */
    @Getter
    @Setter
    private String name;

    /**
     *   人群名称
     */
    @Getter
    @Setter
    private String crowdName;

    /**
     *   分析描述
     */
    @Getter
    @Setter
    private String description;

    /**
     *   分析类型，预留字段，默认为1洞察分析
     */
    @Getter
    @Setter
    private String type;

    /**
     *   分析任务进度
     */
    @Getter
    @Setter
    private String status;

    /**
     *   创建人
     */
    @Getter
    @Setter
    private String creator;

    /**
     *   是否删除
     */
    @Getter
    @Setter
    private Byte isDeleted;

    /**
     *   人群id
     */
    @Getter
    @Setter
    private Long crowdId;

    /**
     *   错误/重试次数
     */
    @Getter
    @Setter
    private Byte failTimes;

    /**
     *   分析元数据
     */
    @Getter
    @Setter
    private String meta;

    /**
     *   分析结果数据
     */
    @Getter
    @Setter
    private String result;

    /**
     *   扩展信息
     */
    @Getter
    @Setter
    private String extInfo;

    /**
     *   父分析元数据
     */
    @Getter
    @Setter
    private String parentMeta;

    @Getter
    @Setter
    private int pageNum = 1;

    @Getter
    @Setter
    private int pageSize = 20;
}