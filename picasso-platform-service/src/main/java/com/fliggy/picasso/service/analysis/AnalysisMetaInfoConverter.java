package com.fliggy.picasso.service.analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;

import com.fliggy.picasso.analysis.AnalysisConfig;
import com.fliggy.picasso.analysis.AnalysisType;
import com.fliggy.picasso.dao.AnalysisMetaInfoDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class AnalysisMetaInfoConverter {

    /**
     * DTO模型转换成DO模型
     *
     * @param analysisMetaInfoDTO
     */
    public AnalysisMetaInfoDO convertFromDTO(AnalysisMetaInfoDTO analysisMetaInfoDTO) {
        AnalysisMetaInfoDO analysisMetaInfoDO = new AnalysisMetaInfoDO();
        BeanUtils.copyProperties(analysisMetaInfoDTO, analysisMetaInfoDO);

        if (analysisMetaInfoDTO.getAnalysisConfig() != null) {
            analysisMetaInfoDO.setAnalysisConfig(JSON.toJSONString(analysisMetaInfoDTO.getAnalysisConfig()));
        }

        if (analysisMetaInfoDTO.getAnalysisType() != null) {
            analysisMetaInfoDO.setAnalysisType(analysisMetaInfoDTO.getAnalysisType().getName());
        }

        if (analysisMetaInfoDTO.getStatus() != null) {
            analysisMetaInfoDO.setStatus(analysisMetaInfoDTO.getStatus().getName());
        }

        if (analysisMetaInfoDTO.getOperator() != null) {
            analysisMetaInfoDO.setOperator(
                JSON.toJSONString(analysisMetaInfoDTO.getOperator(), SerializerFeature.WriteMapNullValue));
        }

        return analysisMetaInfoDO;
    }

    /**
     * DO模型转换成DTO模型
     *
     * @param analysisMetaInfoDO
     */
    public AnalysisMetaInfoDTO convertFromDO(AnalysisMetaInfoDO analysisMetaInfoDO) {
        AnalysisMetaInfoDTO analysisMetaInfoDTO = new AnalysisMetaInfoDTO();
        BeanUtils.copyProperties(analysisMetaInfoDO, analysisMetaInfoDTO);

        if (analysisMetaInfoDO.getAnalysisConfig() != null) {
            analysisMetaInfoDTO.setAnalysisConfig(JSON.parseObject(
                analysisMetaInfoDO.getAnalysisConfig(),
                AnalysisConfig.class));
        }

        if (analysisMetaInfoDO.getAnalysisType() != null) {
            analysisMetaInfoDTO.setAnalysisType(AnalysisType.fromCode(analysisMetaInfoDO.getAnalysisType()));
        }

        if (analysisMetaInfoDO.getStatus() != null) {
            analysisMetaInfoDTO.setStatus(AnalysisStatus.fromCode(analysisMetaInfoDO.getStatus()));
        }

        if (analysisMetaInfoDO.getOperator() != null) {
            analysisMetaInfoDTO.setOperator(JSON.parseArray(analysisMetaInfoDO.getOperator(), String.class));
        }

        return analysisMetaInfoDTO;
    }
}