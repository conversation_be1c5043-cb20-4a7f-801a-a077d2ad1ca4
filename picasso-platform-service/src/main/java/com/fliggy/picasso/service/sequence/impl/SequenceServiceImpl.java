package com.fliggy.picasso.service.sequence.impl;

import com.fliggy.picasso.dao.SequenceDO;
import com.fliggy.picasso.mapper.picasso.SequenceDAO;
import com.fliggy.picasso.service.sequence.SequenceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class SequenceServiceImpl implements SequenceService {

    @Resource
    private SequenceDAO sequenceDAO;

    @Override
    public int resetSequenceValue(Long id, Long value) {
        SequenceDO sequenceDO = new SequenceDO();
        sequenceDO.setId(id);
        sequenceDO.setValue(value);
        return sequenceDAO.updateByPrimaryKeySelective(sequenceDO);
    }
}
