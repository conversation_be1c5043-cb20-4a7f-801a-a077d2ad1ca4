package com.fliggy.picasso.service.lindorm.impl;

import com.alibaba.lindorm.client.exception.LindormException;
import com.aliyun.odps.OdpsException;
import com.fliggy.picasso.common.enums.crowd.CrowdIdMappingTaskStatusEnum;
import com.fliggy.picasso.offline.OdpsService;
import com.fliggy.picasso.service.common.lindorm.LindormCommonService;
import com.fliggy.picasso.service.lindorm.OneIdMappingService;
import com.fliggy.picasso.service.lindorm.domain.OneIdMappingDO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

import static com.fliggy.picasso.service.lindorm.column.OneIdMappingColumnEnum.*;

/**
 * <AUTHOR>
 */
@Data
public abstract class AbstractOneIdMappingServiceImpl implements OneIdMappingService {

    private String tableName;
    private String oneIdReverseLindormColumn;

    @Resource
    protected OdpsService odpsService;
    @Resource
    protected LindormCommonService tripUgdLindormServiceImpl;

    @Override
    public int batchUpsert(List<OneIdMappingDO> records) {
        return 0;
    }

    @Override
    public int delete(OneIdMappingDO record) {
        return 0;
    }

    @Override
    public int batchDelete(List<OneIdMappingDO> records) {
        return 0;
    }

    @Override
    public Set<String> idMappingToOneId(String sourceIdType, String sourceId) throws LindormException {
        Map<String, String> queryOneIdConditionMap = generateOneIdConditionMap(sourceId, sourceIdType);
        List<String> oneIdReverseList = tripUgdLindormServiceImpl.queryList(tableName, oneIdReverseLindormColumn, queryOneIdConditionMap);
        return oneIdReverseList.stream().map(StringUtils::reverse).collect(Collectors.toSet());
    }

    @Override
    public String idMappingFromOneId(String oneId, String targetIdType) throws LindormException {
        Map<String, String> queryUnionIdConditionMap = generateKeyValueConditionMap(oneId, targetIdType);
        return tripUgdLindormServiceImpl.query(tableName, KEY_VALUE.getCode(), queryUnionIdConditionMap);
    }

    @Override
    public Set<String> idMapping(String sourceIdType, String sourceId, String targetIdType) throws LindormException {
        Set<String> oneIdList = idMappingToOneId(sourceIdType, sourceId);
        if (Objects.isNull(oneIdList) || oneIdList.isEmpty()) {
            return new HashSet<>();
        }

        Set<String> targetIdList = new HashSet<>();
        for (String oneId: oneIdList) {
            targetIdList.add(idMappingFromOneId(oneId, targetIdType));
        }
        return targetIdList;
    }

    @Override
    public Pair<String, String> batchIdMapping(String crowdId) throws OdpsException {
        return null;
    }

    @Override
    public CrowdIdMappingTaskStatusEnum queryIdMappingTaskStatus(String instanceId) {
        return null;
    }

    private Map<String, String> generateOneIdConditionMap(String sourceId, String keyType) {
        Map<String, String> queryOneIdConditionMap = new HashMap<>();
        queryOneIdConditionMap.put(KEY_TYPE.getCode(), keyType);
        queryOneIdConditionMap.put(KEY_VALUE.getCode(), sourceId);
        return queryOneIdConditionMap;
    }

    private Map<String, String> generateKeyValueConditionMap(String oneId, String keyType) {
        Map<String, String> queryOneIdConditionMap = new HashMap<>();
        queryOneIdConditionMap.put(oneIdReverseLindormColumn, StringUtils.reverse(oneId));
        queryOneIdConditionMap.put(KEY_TYPE.getCode(), keyType);
        return queryOneIdConditionMap;
    }

}
