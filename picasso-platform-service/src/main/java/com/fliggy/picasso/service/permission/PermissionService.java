package com.fliggy.picasso.service.permission;

import com.fliggy.picasso.employee.UserContextDTO;

public interface PermissionService<T> {

    /**
     * 获取用户登录信息，限制在主线程使用
     */
    UserContextDTO getUserContextDTO();

    /**
     * 是否为超级管理员，限制在主线程使用
     *
     * @return
     */
    boolean isSuperAdmin();

    /**
     * 是否为超级管理员
     *
     * @param userContextDTO 用户登录信息
     * @return 是否为超级管理员
     */
    boolean isSuperAdmin(UserContextDTO userContextDTO);

    /**
     * 是否拥有数据权限，限制在主线程使用
     *
     * @param data 数据
     * @param permissionLevelEnum 权限级别
     * @return 是否拥有权限
     */
    boolean hasDataPermission(T data, PermissionLevelEnum permissionLevelEnum);

    /**
     * 是否拥有数据权限
     *
     * @param data 数据
     * @param permissionLevelEnum 权限级别
     * @return 是否拥有权限
     */
    boolean hasDataPermission(T data, PermissionLevelEnum permissionLevelEnum, UserContextDTO userContextDTO);
}
