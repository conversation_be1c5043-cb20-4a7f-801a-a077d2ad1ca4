package com.fliggy.picasso.service.buildsql.parseCondition.label;

import com.fliggy.picasso.common.enums.label.LabelBizDataTypeEnum;
import com.fliggy.picasso.common.enums.label.LabelOperatorEnum;
import com.fliggy.picasso.utils.DateUtils;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.fliggy.picasso.common.Constant.SINGLE_QUOTE;
import static com.fliggy.picasso.common.constants.crowd.CrowdConstants.*;

/**
 * 时间类型标签表达式生成器
 */
@Component
public class DateLabelExpressionGenerator extends BaseLabelExpressionGenerator {

    public DateLabelExpressionGenerator(ExpressionGeneratorResolver expressionGeneratorResolver) {
        super(expressionGeneratorResolver);
    }

    @Override
    public String buildExpression(String labelName, List<String> enumValues, LabelOperatorEnum operatorType) {
        StringBuilder result = new StringBuilder();
        result.append(labelName);
        if (enumValues.size() == 1) {
            int days = Integer.parseInt(enumValues.get(0));
            String startDays;
            String stopDays = DateUtils.getDateStr(days);
            if (days >= 0) {
                startDays = DateUtils.getDateStr(0);
                result.append(handleDateCondition(labelName, startDays, stopDays, operatorType));
            } else {
                startDays = DateUtils.getDateStr(-1);
                result.append(handleDateCondition(labelName, stopDays, startDays, operatorType));
            }
        } else if (enumValues.size() == 2) {
            result.append(handleDateCondition(labelName, enumValues.get(0), enumValues.get(1), operatorType));
        }
        return result.toString();

    }


    /**
     * 处理日期型标签的筛选条件
     *
     * @param label       标签名称
     * @param start       开始时间
     * @param end         结束时间
     * @param preOperator 前一个运算操作符号
     * @return 处理后的标签筛选条件
     */
    private String handleDateCondition(String label, String start, String end, LabelOperatorEnum preOperator) {
        StringBuilder result = new StringBuilder();
        if (preOperator != LabelOperatorEnum.NOTIN) {
            result.append(BETWEEN)
                    .append(SINGLE_QUOTE)
                    .append(DateUtils.getStartDay(start))
                    .append(SINGLE_QUOTE)
                    .append(AND)
                    .append(SINGLE_QUOTE)
                    .append(DateUtils.getEndOfDay(end))
                    .append(SINGLE_QUOTE);
        } else {
            result.append(" < ")
                    .append(SINGLE_QUOTE)
                    .append(DateUtils.getStartDay(start))
                    .append(SINGLE_QUOTE)
                    .append(OR)
                    .append(label)
                    .append(" > ")
                    .append(SINGLE_QUOTE)
                    .append(DateUtils.getEndOfDay(end))
                    .append(SINGLE_QUOTE)
                    .append(OR).append(label).append(" is null");
        }

        result.append(")");
        return result.toString();
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        expressionGeneratorResolver.register(LabelBizDataTypeEnum.DATE, this);
    }
}
