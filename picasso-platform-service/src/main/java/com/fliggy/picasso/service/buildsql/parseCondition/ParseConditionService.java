package com.fliggy.picasso.service.buildsql.parseCondition;

import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.entity.condition.GroupConditionDTO;

import java.util.List;
import java.util.Map;

/**
 * 解析圈选条件接口
 */
public interface ParseConditionService {
    /**
     * 解析圈选条件，生成逻辑表达式
     *
     * @param labelCrowdConditions
     * @return
     */
    String generateEntireLogicExpression(LabelCrowdConditions labelCrowdConditions);

    /**
     * 生成标签组对应的表达式
     *
     * @param groupExpression
     * @return
     */
    String generateGroupLogicExpression(String groupExpression);

    /**
     * 解析圈选条件，生成标签对应的表达式
     *
     * @param labelCrowdConditions
     * @return
     * @Param groupConditionDTOS
     */
    Map<String, String> generateLabelExpression(LabelCrowdConditions labelCrowdConditions, List<GroupConditionDTO> groupConditionDTOS);


    /**
     * 生成表对应的表达式
     *
     * @param labelCrowdConditions
     * @return
     * @Param groupConditionDTOS
     */
    List<String> generateTableExpression(LabelCrowdConditions labelCrowdConditions, List<GroupConditionDTO> groupConditionDTOS);
}
