package com.fliggy.picasso.service.lindorm.domain;

import com.fliggy.picasso.service.lindorm.column.OneIdTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class OneIdMappingDO {

    private OneIdTypeEnum oneIdType;

    private String oneId;

    private String oneIdReverse;

    private Integer index;

    private String keyType;

    private String keyValue;

    public OneIdMappingDO() {
    }

    public OneIdMappingDO(OneIdTypeEnum oneIdType, String oneId, Integer index, String keyType, String keyValue) {
        this.oneIdType = oneIdType;
        this.oneId = oneId;
        this.oneIdReverse = StringUtils.reverse(oneId);
        this.index = index;
        this.keyType = keyType;
        this.keyValue = keyValue;
    }
}
