package com.fliggy.picasso.service.enumvalue;

import com.alibaba.fastjson.JSON;
import com.fliggy.picasso.common.constants.label.LabelEnumValueSourceTypeEnum;
import com.fliggy.picasso.common.domain.Employee;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
public class EnumDimMetaInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @Getter
    @Setter
    private Date gmtModified;

    /**
     * 维度描述
     */
    @Getter
    @Setter
    private String name;

    /**
     * 类型：CUSTOMIZE（自定义）、ODPS（odps维表）
     */
    @Getter
    @Setter
    private LabelEnumValueSourceTypeEnum type;

    /**
     * 维度代码
     */
    @Getter
    @Setter
    private String code;

    /**
     * 逻辑删除（0：否，1：是）
     */
    @Getter
    @Setter
    private Byte deleted;

    /**
     * 创建人
     */
    @Getter
    @Setter
    private Employee creator;

    /**
     * 管理员
     */
    @Getter
    @Setter
    private List<Employee> operator;

    /**
     * 存储odps表、列、枚举值列等信息
     */
    @Getter
    @Setter
    private String extInfo;

    private Map<String, String> extInfoMap = new HashMap<>();

    public void putExtInfo(String key, String value) {
        if (MapUtils.isEmpty(this.extInfoMap) && StringUtils.isNotBlank(extInfo)) {
            this.extInfoMap = JSON.parseObject(extInfo, Map.class);
        }
        extInfoMap.put(key, value);
        extInfo = JSON.toJSONString(this.extInfoMap);
    }

    public String fetchExtInfo(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        this.extInfoMap = JSON.parseObject(extInfo, Map.class);
        return extInfoMap.get(key);
    }
}