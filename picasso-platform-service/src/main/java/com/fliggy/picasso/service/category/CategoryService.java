package com.fliggy.picasso.service.category;

import com.fliggy.picasso.common.domain.category.CategoryDO;
import com.fliggy.picasso.common.domain.category.CategoryTree;
import com.fliggy.picasso.common.domain.category.FlatCategory;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-12-06 12:21
 */
public interface CategoryService {

    /**
     * 通过ID查询
     *
     * @param id 标签ID
     * @return
     */
    CategoryDO queryById(Long id);

    /**
     * 通过名称查询目录信息
     *
     * @param name 名称
     * @return
     */
    CategoryDO queryByName(String name);

    /**
     * 所有的目录信息
     *
     * @return
     */
    List<CategoryDO> listAll();

    /**
     * 查询业务实体对应的扁平化目录结构
     *
     * @param bizEntityName 业务实体名称
     * @return
     */
    List<FlatCategory> queryAllFlatCategory(String bizEntityName);

    /**
     * 生成对应业务实体的目录树
     *
     * @param bizEntityName 业务实体名称
     * @return
     */
    CategoryTree queryCategoryTree(String bizEntityName);

    /**
     * @param categoryDO
     * @return
     */
    Boolean update(CategoryDO categoryDO);

    /**
     * 插入信息
     *
     * @param categoryDO 目录元数据
     * @return
     */
    Long insert(CategoryDO categoryDO);

    /**
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 根据ID查询目录名称
     * @param id
     * @return
     */
    String queryCategoryName(Long id);

}
