package com.fliggy.picasso.service.profile;

import com.fliggy.picasso.common.domain.profile.PhysicalProfileSceneConfig;
import com.fliggy.picasso.entity.odps.OdpsMasterConfig;
import com.google.common.base.Objects;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Data
public class PhysicalProfileInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 画像ID
     */
    private Long profileId;

    /**
     * 画像Code
     */
    private String profileCode;

    /**
     * 画像名称
     */
    private String profileName;

    /**
     * 主键列
     */
    private String primaryKey;

    /**
     * 物理画像标识
     */
    private String physicalProfileCode;

    /**
     * 物理画像名称
     */
    private String physicalProfileName;

    /**
     * 物理画像表类型
     */
    private PhysicalProfileType type;

    /**
     * 物理画像表状态
     */
    private PhysicalProfileStatus status;

    /**
     * ODPS项目空间名称
     */
    private String odpsProject;

    /**
     * ODPS表名
     */
    private String odpsTable;

    /**
     * ODPS表注释
     */
    private String odpsTableComment;

    /**
     * ODPS表生命周期
     */
    private Integer odpsTableLifecycle;

    /**
     * OLAP引擎表名
     */
    private String olapTable;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 删除状态
     */
    private Byte deleted;

    /**
     * 底表odps
     */
    private OdpsMasterConfig odpsMasterConfig;

    /**
     * 底表分区
     */
    private String odpsTablePtKey;

    private Date odpsTableUpdateTime;

    private Date olapTableUpdateTime;

    private Date kvstoreUpdateTime;

    /**
     * 应用场景
     */
    private List<PhysicalProfileSceneConfig> physicalProfileSceneConfigList;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PhysicalProfileInfoDTO that = (PhysicalProfileInfoDTO) o;
        return Objects.equal(physicalProfileCode, that.physicalProfileCode);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(physicalProfileCode);
    }

    public enum PhysicalProfileType {

        /**
         * 离线
         */
        OFFLINE((byte) 0),

        REALTIME((byte) 1),

        USER_DEFINDED((byte) 3);

        private static final Map<Byte, PhysicalProfileType> CODE_MAP = Maps.newHashMap();

        static {
            for (PhysicalProfileType value : values()) {
                CODE_MAP.put(value.getCode(), value);
            }
        }

        public static PhysicalProfileType fromCode(byte code) {
            return CODE_MAP.getOrDefault(code, null);
        }

        @Getter
        private byte code;

        PhysicalProfileType(byte code) {
            this.code = code;
        }
    }

    public enum PhysicalProfileStatus {

        /**
         *
         */
        REFLUX_PREPARATION((byte) 0, "待回流"),

        /**
         * 回流中
         */
        REFLUXING((byte) 1, "回流中"),

        /**
         * 已回流
         */
        REFLUXED((byte) 2, "已回流"),

        /**
         * 回流异常
         */
        REFLUX_EXCEPTION((byte) 3, "回流异常");

        private static final Map<Byte, PhysicalProfileStatus> CODE_MAP = Maps.newHashMap();

        static {
            for (PhysicalProfileStatus value : values()) {
                CODE_MAP.put(value.getCode(), value);
            }
        }

        public static PhysicalProfileStatus fromCode(byte code) {
            return CODE_MAP.getOrDefault(code, null);
        }

        @Getter
        private byte code;
        @Getter
        private String msg;

        PhysicalProfileStatus(byte code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }
}