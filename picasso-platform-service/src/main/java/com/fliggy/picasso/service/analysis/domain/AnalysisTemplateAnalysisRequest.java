package com.fliggy.picasso.service.analysis.domain;

import com.fliggy.picasso.common.utils.NumberUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * 分析模板分析请求
 */
@Data
public class AnalysisTemplateAnalysisRequest {

    /**
     * 父分析id
     */
    private Long parentId;

    /**
     * 模板id
     */
    private Long templateId;

    public boolean invalid() {
        return !NumberUtils.validLong(parentId) || !NumberUtils.validLong(templateId);
    }
}
