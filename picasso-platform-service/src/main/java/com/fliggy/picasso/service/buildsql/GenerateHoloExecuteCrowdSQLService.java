package com.fliggy.picasso.service.buildsql;

import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;

/**
 * 生成Hologres执行的人群相关SQL
 */
public interface GenerateHoloExecuteCrowdSQLService extends GenerateCrowdSQLService {

    /**
     * 创建标签圈人的dumpSQL
     *
     * @param crowdMetaInfoDO 人群元数据
     * @return dumpSQL
     */
    String generateLabelCrowdDumpSQL(CrowdMetaInfoDO crowdMetaInfoDO);


    /**
     * 创建非标签圈人的dumpSQL
     *
     * @param crowdMetaInfoDO 人群元数据
     * @return 人群元数据
     */
    String generateNonLabelCrowdDumpSQL(CrowdMetaInfoDO crowdMetaInfoDO);

    /**
     * 生成sql
     *
     * @param labelCrowdConditions 人群元数据
     * @param addShardId           addShardId
     * @return sql
     */
    String generateConditionSQL(LabelCrowdConditions labelCrowdConditions, Boolean addShardId);
}
