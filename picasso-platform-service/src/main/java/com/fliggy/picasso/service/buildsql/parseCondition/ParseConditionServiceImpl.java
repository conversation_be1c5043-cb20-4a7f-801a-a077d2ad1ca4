package com.fliggy.picasso.service.buildsql.parseCondition;

import com.fliggy.picasso.common.domain.crowd.LabelCrowdConditions;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.entity.condition.GroupConditionDTO;
import com.fliggy.picasso.service.buildsql.parseCondition.label.LabelExpressionService;
import com.fliggy.picasso.service.buildsql.parseCondition.table.TableExpressionGenerateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.fliggy.picasso.common.Constant.BLANK;

/**
 * 解析圈选条件接口实现类
 */
@Component
public class ParseConditionServiceImpl implements ParseConditionService {

    @Resource
    LabelExpressionService labelExpressionService;

    @Resource
    TableExpressionGenerateService tableExpressionGenerateService;

    @Override
    public String generateEntireLogicExpression(LabelCrowdConditions labelCrowdConditions) {
        String expression = labelCrowdConditions.getExpression();
        List<String> logicJoiner = getLogicJoiners(expression);

        List<LabelCrowdConditions.LabelGroup> groups = labelCrowdConditions.getGroup();
        StringBuilder entireLogicExpression = new StringBuilder();
        for (int i = 0; i < groups.size(); i++) {
            String groupLogicExpression = generateGroupLogicExpression(groups.get(i).getExpression());
            if (groupLogicExpression == null) {
                return null;
            }
            groupLogicExpression = "(" + groupLogicExpression + ")";
            entireLogicExpression.append(groupLogicExpression);
            if (i < groups.size() - 1) {
                entireLogicExpression.append(BLANK).append(logicJoiner.get(i)).append(BLANK);
            }
        }
        return entireLogicExpression.toString();
    }

    private List<String> getLogicJoiners(String expression) {
        if (StringUtils.isBlank(expression)) {
            return null;
        }

        String[] exprs = expression.split(BLANK);
        if (exprs.length % 2 == 0) {
            throw new ParamErrorException(String.format("解析标签圈人逻辑表达式异常，表达式为：%s", expression));
        }
        List<String> logicJoiners = new ArrayList<>();
        for (int i = 0; i < exprs.length; i++) {
            if ("and".equalsIgnoreCase(exprs[i])) {
                logicJoiners.add(" AND ");
                continue;
            }
            if ("or".equalsIgnoreCase(exprs[i])) {
                logicJoiners.add(" OR ");
                continue;
            }

            if ("notin".equalsIgnoreCase(exprs[i])) {
                logicJoiners.add(" NOTIN ");
                continue;
            }
        }
        return logicJoiners;
    }


    @Override
    public String generateGroupLogicExpression(String groupExpression) {
        if (StringUtils.isBlank(groupExpression)) {
            return null;
        }

        String[] exprs = groupExpression.split(BLANK);
        if (exprs.length % 2 == 0) {
            throw new ParamErrorException(String.format("解析标签圈人逻辑表达式异常，表达式为：%s", groupExpression));
        }

        StringBuilder result = new StringBuilder();

        for (int i = 0; i < exprs.length; i++) {
            if (i % 2 == 0) {
                result.append(exprs[i]);
            } else {
                if ("and".equalsIgnoreCase(exprs[i])) {
                    result.append(" AND ");
                } else if ("or".equalsIgnoreCase(exprs[i])) {
                    result.append(" OR ");
                } else if ("notin".equalsIgnoreCase(exprs[i])) {
                    result.append(" NOTIN ");
                } else {
                    throw new ParamErrorException(String.format("解析标签圈人逻辑表达式异常，表达式为：%s", groupExpression));
                }
            }
        }
        return result.toString();
    }

    @Override
    public Map<String, String> generateLabelExpression(LabelCrowdConditions labelCrowdConditions, List<GroupConditionDTO> groupConditionDTOS) {
        return labelExpressionService.generateLabelExpression(labelCrowdConditions, groupConditionDTOS);
    }

    @Override
    public List<String> generateTableExpression(LabelCrowdConditions labelCrowdConditions, List<GroupConditionDTO> groupConditionDTOS) {
        return tableExpressionGenerateService.generateTableExpression(labelCrowdConditions, groupConditionDTOS);
    }
}
