package com.fliggy.picasso.service.profile;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProfileEnumType {

    /**
     * 画像id
     */
    private Long profileId;

    /**
     * 一级画像名称
     */
    private String profileName;

    /**
     * 一级画像code
     */
    private String profileCode;

    /**
     * 二级画像名称
     */
    private String physicalProfileName;

    /**
     * 二级画像code
     */
    private String physicalProfileCode;

    public static ProfileEnumType convertToProfileEnumType(PhysicalProfileInfoDTO profile) {
        ProfileEnumType profileEnumType = new ProfileEnumType();
        profileEnumType.setProfileName(profile.getProfileName());
        profileEnumType.setProfileCode(profile.getProfileCode());
        profileEnumType.setPhysicalProfileName(profile.getPhysicalProfileName());
        profileEnumType.setPhysicalProfileCode(profile.getPhysicalProfileCode());
        return profileEnumType;
    }
}
