package com.fliggy.picasso.service.feature;

import com.fliggy.picasso.dao.FeatureInfoDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * This class was generated by Ali<PERSON>Generator
 * <AUTHOR>
 */
@Service
public class FeatureInfoConverter {

    /**
     * DTO模型转换成DO模型
     * @param featureInfoDTO
     */
    public FeatureInfoDO convertFromDTO(FeatureInfoDTO featureInfoDTO) {
        FeatureInfoDO featureInfoDO = new FeatureInfoDO();
        BeanUtils.copyProperties(featureInfoDTO,featureInfoDO);
        return featureInfoDO;
    }

    /**
     * DO模型转换成DTO模型
     * @param featureInfoDO
     */
    public FeatureInfoDTO convertFromDO(FeatureInfoDO featureInfoDO) {
        FeatureInfoDTO featureInfoDTO = new FeatureInfoDTO();
        BeanUtils.copyProperties(featureInfoDO,featureInfoDTO);
        return featureInfoDTO;
    }
}