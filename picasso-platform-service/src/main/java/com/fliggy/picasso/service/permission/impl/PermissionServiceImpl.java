package com.fliggy.picasso.service.permission.impl;

import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

import com.fliggy.picasso.acl.AclCommonService;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.label.LabelInfoDO;
import com.fliggy.picasso.common.domain.label.bizentity.BizEntityDO;
import com.fliggy.picasso.common.domain.label.enumvalue.EnumDimMetaInfoDO;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.employee.UserContext;
import com.fliggy.picasso.employee.UserContextDTO;
import com.fliggy.picasso.entity.bo.CrowdInfoBO;
import com.fliggy.picasso.service.permission.PermissionLevelEnum;
import com.fliggy.picasso.service.permission.PermissionService;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class PermissionServiceImpl<T> implements PermissionService<T> {

    private final AclCommonService aclCommonService;

    @Switch(description = "超级管理员")
    public static String ACL_ROLE_SUPPER_ADMIN = "优比,牧熵,凤栖,寒四";

    @Autowired
    public PermissionServiceImpl(AclCommonService aclCommonService) {
        this.aclCommonService = aclCommonService;
    }

    @Override
    public UserContextDTO getUserContextDTO() {
        return UserContext.getBucUser().orElse(null);
    }

    @Override
    public boolean isSuperAdmin() {
        return isSuperAdmin(getUserContextDTO());
    }

    @Override
    public boolean isSuperAdmin(UserContextDTO userContextDTO) {
        if (Objects.isNull(userContextDTO) || Objects.isNull(userContextDTO.getBucId())) {
            return false;
        }
        return ACL_ROLE_SUPPER_ADMIN.contains(userContextDTO.getNickNameCN());
    }

    @Override
    public boolean hasDataPermission(T data, PermissionLevelEnum permissionLevelEnum) {
        return hasDataPermission(data, permissionLevelEnum, getUserContextDTO());
    }

    @Override
    public boolean hasDataPermission(T data, PermissionLevelEnum permissionLevelEnum, UserContextDTO userContextDTO) {
        if (Objects.isNull(data) || Objects.isNull(permissionLevelEnum) || Objects.isNull(userContextDTO)) {
            return false;
        }

        boolean isSuperAdmin = isSuperAdmin(userContextDTO);
        if (PermissionLevelEnum.SUPER_ADMIN.equals(permissionLevelEnum)) {
            return isSuperAdmin;
        }

        List<Employee> creators = Lists.newArrayList();
        List<Employee> operators = Lists.newArrayList();
        buildCreatorAndOperators(data, creators, operators);

        // 权限判断
        boolean isCreator = false;
        if (CollectionUtils.isNotEmpty(creators)) {
            isCreator = UserContext.empIdEquals(creators.get(0).getEmpId(), userContextDTO.getEmpId());
        }

        boolean isOperator = false;
        if (CollectionUtils.isNotEmpty(operators)) {
            for (Employee operator : operators) {
                if (UserContext.empIdEquals(operator.getEmpId(), userContextDTO.getEmpId())) {
                    isOperator = true;
                    break;
                }
            }
        }

        if (PermissionLevelEnum.CREATOR.equals(permissionLevelEnum)) {
            return isSuperAdmin || isCreator;
        }

        if (PermissionLevelEnum.OPERATOR.equals(permissionLevelEnum)) {
            return isSuperAdmin || isCreator || isOperator;
        }

        return false;
    }

    private void buildCreatorAndOperators(T data, List<Employee> creators, List<Employee> operators) {
        Employee creator = null;
        List<Employee> tmpOperators = Lists.newArrayList();
        if (data instanceof CrowdMetaInfoDO) {
            // 人群
            CrowdMetaInfoDO crowdMetaInfoDO = (CrowdMetaInfoDO)data;
            creator = crowdMetaInfoDO.getCreator();
            tmpOperators = crowdMetaInfoDO.getOperator();
        } else if (data instanceof CrowdInfoBO) {
            CrowdInfoBO crowdInfoBO = (CrowdInfoBO)data;
            creator = crowdInfoBO.getOwner();
            tmpOperators = crowdInfoBO.getManagers();
        } else if (data instanceof EnumDimMetaInfoDO) {
            // 枚举
            EnumDimMetaInfoDO enumDimMetaInfoDO = (EnumDimMetaInfoDO)data;
            creator = convertEmployee(enumDimMetaInfoDO.getCreator());
            tmpOperators = convertEmployees(enumDimMetaInfoDO.getOperator());
        } else if (data instanceof LabelInfoDO) {
            // 标签
            LabelInfoDO labelInfoDO = (LabelInfoDO)data;
            creator = convertEmployee(labelInfoDO.getCreator());
            Employee dataOwner = convertEmployee(labelInfoDO.getBizOwner());
            if (Objects.nonNull(dataOwner)) {
                tmpOperators.add(dataOwner);
            }
            Employee qualityOwner = convertEmployee(labelInfoDO.getQualityOwner());
            if (Objects.nonNull(qualityOwner)) {
                tmpOperators.add(qualityOwner);
            }
            Employee bizOwner = convertEmployee(labelInfoDO.getBizOwner());
            if (Objects.nonNull(bizOwner)) {
                tmpOperators.add(bizOwner);
            }
        } else if (data instanceof BizEntityDO) {
            BizEntityDO bizEntityDO = (BizEntityDO)data;
            creator = bizEntityDO.getCreator();
            tmpOperators = bizEntityDO.getOperator();
        } else {
            throw new ParamErrorException("unsupporeted permission data: " + data);
        }

        if (Objects.nonNull(creator)) {
            creators.add(creator);
        }
        if (CollectionUtils.isNotEmpty(tmpOperators)) {
            operators.addAll(tmpOperators);
        }
    }

    private Employee convertEmployee(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }

        try {
            return JSON.parseObject(str, Employee.class);
        } catch (Exception e) {
            return null;
        }
    }

    private List<Employee> convertEmployees(String str) {
        if (StringUtils.isBlank(str)) {
            return Lists.newArrayList();
        }

        try {
            return JSON.parseObject(str, new TypeReference<List<Employee>>(){});
        } catch (Exception e) {
            return Lists.newArrayList();
        }

    }
}
