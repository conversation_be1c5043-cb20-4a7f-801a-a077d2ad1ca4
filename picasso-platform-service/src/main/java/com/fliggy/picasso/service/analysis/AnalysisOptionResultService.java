package com.fliggy.picasso.service.analysis;

import java.util.List;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
public interface AnalysisOptionResultService {

    /**
     * @param id
     * @return
     */
    List<AnalysisOptionResultDTO> listByAnalysisId(Long id);

    /**
     * @param analysisOptionResultDTO
     */
    void create(AnalysisOptionResultDTO analysisOptionResultDTO);

    /**
     * @param id
     * @return
     */
    AnalysisOptionResultDTO findById(Long id);

    /**
     * @param analysisOptionResultDTO
     */
    void updateSelective(AnalysisOptionResultDTO analysisOptionResultDTO);

    void update(AnalysisOptionResultDTO analysisOptionResultDTO);

    /**
     * @param id
     */
    void delete(Long id);

    /**
     * 增加画像分析子项
     *
     * @param param
     * @return
     */
    List<AnalysisOptionResultDTO> previewAndSaveProfileAnalysisOpts(AnalysisProfileOptParam param);

    /**
     * 预览画像分析子项
     *
     * @param param
     * @return
     */
    List<AnalysisOptionResultDTO> previewProfileAnalysisOpts(AnalysisProfileOptParam param);

    AnalysisOptionResultDTO addMeasureAnalysisOpt(AnalysisMeasureOptAddParam param,
                                                  AnalysisMetaInfoDTO analysisMetaInfoDTO);

    /**
     * 增加指标分析子项
     *
     * @param param
     * @return
     */
    List<AnalysisOptionResultDTO> previewAndSaveMeasureAnalysisOpt(AnalysisMeasureOptAddParam param);

    /**
     * @param id
     * @return
     */
    List<AnalysisOptionResultDTO> reload(Long id);
}