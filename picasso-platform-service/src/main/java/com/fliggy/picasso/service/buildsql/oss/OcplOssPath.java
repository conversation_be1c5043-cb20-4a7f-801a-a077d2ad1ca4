package com.fliggy.picasso.service.buildsql.oss;

import static com.fliggy.picasso.common.Constant.*;

/**
 * oss人群持久层人群数据路径
 *
 * <AUTHOR>
 */
public class OcplOssPath {
    /**
     * 通过oss路径创建odps外表时，根据人群id获取odps外表中使用的oss路径
     *
     * @param crowdId 人群id
     * @return oss路径创建odps外表中使用的oss路径
     */
    public static String getOssOdpsExternalTableLocation(String crowdId) {
        return String.format("%s/%s/crowd/data/%s/", OSS_ODPS_EXTERNAL_TABLE_LOCATION_PREFIX, OCPL_BUCKET, crowdId);
    }

    /**
     * mysql中存储的人群oss数据路径
     * 获取保存odps表中的人群数据到oss时，使用的oss路径
     *
     * @param crowdId 人群id
     * @return 保存odps表中的人群数据到oss时，使用的oss路径
     */
    public static String getOssLocation(String crowdId) {
        return String.format("%s/%s/crowd/data/%s/", OSS_LOCATION_PREFIX, OCPL_BUCKET, crowdId);
    }

    /**
     * 获取ads 3.0 dump 人群数据到oss时，使用的oss路径
     *
     * @param crowdId 人群id
     * @return ads 3.0 dump 人群数据到oss时，使用的oss路径
     */
    public static String getAdsDumpOssLocation(String crowdId) {
        return String.format("oss://%s/crowd/data/%s/", OCPL_BUCKET, crowdId);
    }
}
