package com.fliggy.picasso.service.lindorm.column;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public enum OneIdKeyTypeEnum {

    /**
     * one id key type
     */
    SCRM_ONE_ID("scrm_one_id", "scrm one id"),
    DEVICE_ONE_ID("device_one_id", "device one id"),
    USER_ID("user_id", "淘宝用户id"),
    UMID("umid", "淘宝用户id"),
    UNION_ID("union_id", "微信id"),
    UTDID("utdid", "设备统一id"),
    DEVICE_ID("device_id", "设备号id"),
    IMEI("imei", "移动设备国际身份码的缩写"),
    IMSI("imsi", "国际移动用户识别码，是区别移动用户的标志，储存在SIM卡中"),
    IMEISI("imeisi", "经过不同的设备信息，计算出来设备唯一id"),
    OAID("oaid", "工信部推广的设备id"),
    IDFA("idfa", "Apple的设备id"),
    CAID("caid", "20220111_版本"),
    CAID2("caid2", "20211207_版本"),
    OFFICIAL_ACCOUNT_OPEN_ID("official_account_open_id", "微信公众号openid"),
    MINI_APP_OPEN_ID("mini_app_open_id", "微信小程序openid"),
    WE_WORK_OPEN_ID("we_work_open_id", "企业微信openid"),
    ;

    private String code;
    private String desc;

    OneIdKeyTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OneIdKeyTypeEnum fromCode(String code) {
        for (OneIdKeyTypeEnum oneIdKeyTypeEnum : OneIdKeyTypeEnum.values()) {
            if (oneIdKeyTypeEnum.getCode().equals(code)) {
                return oneIdKeyTypeEnum;
            }
        }
        return null;
    }

    public static List<String> getDeviceKeyTypes() {
        List<OneIdKeyTypeEnum> keyTypeEnums = Lists.newArrayList(USER_ID, UTDID, UMID, IMEI, OAID, IDFA, CAID, CAID2);
        return keyTypeEnums.stream().map(OneIdKeyTypeEnum::getCode).collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
}
