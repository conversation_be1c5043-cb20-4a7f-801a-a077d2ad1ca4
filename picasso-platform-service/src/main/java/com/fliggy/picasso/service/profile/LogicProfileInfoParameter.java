package com.fliggy.picasso.service.profile;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class LogicProfileInfoParameter implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     *   画像标识
     */
    @Getter
    @Setter
    private String code;

    /**
     *   画像名称
     */
    @Getter
    @Setter
    private String name;

    /**
     *   画像描述
     */
    @Getter
    @Setter
    private String description;

    /**
     *   主键列
     */
    @Getter
    @Setter
    private String primaryKey;

    /**
     *   创建人
     */
    @Getter
    @Setter
    private String creator;

    /**
     *   修改人
     */
    @Getter
    @Setter
    private String modifier;

    /**
     *   删除状态
     */
    @Getter
    @Setter
    private Byte deleted;
}