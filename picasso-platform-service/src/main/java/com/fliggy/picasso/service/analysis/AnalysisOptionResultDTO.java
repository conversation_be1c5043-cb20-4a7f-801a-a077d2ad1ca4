package com.fliggy.picasso.service.analysis;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.JSONObject;

import com.fliggy.picasso.analysis.AnalysisType;
import lombok.Getter;
import lombok.Setter;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
public class AnalysisOptionResultDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @Getter
    @Setter
    private Date gmtModified;

    /**
     * 分析ID
     */
    @Getter
    @Setter
    private Long analysisId;

    /**
     * 分析名称
     */
    @Getter
    @Setter
    private String analysisName;

    /**
     * 分析类型
     */
    @Getter
    @Setter
    private AnalysisType analysisType;

    /**
     * 分析子项状态
     */
    @Getter
    @Setter
    private AnalysisOptStatus analysisOptStatus;

    /**
     * 最近更新时间
     */
    @Getter
    @Setter
    private Date lastUpdateTime;

    /**
     * 分析执行时的各标签数据时间
     */
    @Getter
    @Setter
    private String labelDataTime;

    /**
     * 分析子项图表类型
     */
    @Getter
    @Setter
    private String analysisOptGraghType;

    /**
     * 分析子项配置
     */
    @Getter
    @Setter
    private JSONObject analysisOptConfig;

    /**
     * 分析子项结果
     */
    @Getter
    @Setter
    private JSONObject analysisOptResult;

    /**
     * 分析子项扩展信息
     */
    @Getter
    @Setter
    private String extInfo;
}