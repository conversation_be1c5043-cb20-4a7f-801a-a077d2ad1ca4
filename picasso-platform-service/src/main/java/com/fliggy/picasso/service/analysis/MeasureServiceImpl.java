package com.fliggy.picasso.service.analysis;

import com.fliggy.olap.client.domain.Result;
import com.fliggy.olap.client.domain.meta.MeasureDTO;
import com.fliggy.olap.client.service.OlapQueryService;
import com.fliggy.picasso.client.service.MeasureService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class MeasureServiceImpl implements MeasureService {

    @Resource
    OlapQueryService olapQueryService;



    @Override
    public List<MeasureDTO> listAllCrowdMeasure() {
        Result<List<MeasureDTO>> measures = olapQueryService.getMeasures(true);
        if (!measures.isSuccess()) {
            throw new RuntimeException("获取人群相关指标失败！！！" + measures.getMsg());
        }
        return measures.getData();
    }


}
