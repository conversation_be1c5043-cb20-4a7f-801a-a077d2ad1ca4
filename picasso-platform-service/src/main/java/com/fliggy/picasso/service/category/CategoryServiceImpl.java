package com.fliggy.picasso.service.category;

import com.alibaba.metrics.StringUtils;
import com.fliggy.picasso.common.domain.Employee;
import com.fliggy.picasso.common.domain.category.*;
import com.fliggy.picasso.common.domain.label.bizentity.BizEntityDO;
import com.fliggy.picasso.common.enums.group.ProfileEnum;
import com.fliggy.picasso.common.exception.ParamErrorException;
import com.fliggy.picasso.mapper.picasso.CategoryMapper;
import com.fliggy.picasso.service.label.LabelInfoService;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.fliggy.picasso.common.Constant.SERVICE_LOG;

/**
 * <AUTHOR>
 * @date 2019-12-02 16:47
 */
@Component
public class CategoryServiceImpl implements CategoryService {

    private static final Logger log = LoggerFactory.getLogger(SERVICE_LOG);

    private final CategoryMapper categoryDAO;
    private final LabelInfoService labelInfoService;

    @Autowired
    public CategoryServiceImpl(CategoryMapper categoryDAO, LabelInfoService labelInfoService) {
        this.categoryDAO = categoryDAO;
        this.labelInfoService = labelInfoService;
    }

    @Override
    public CategoryDO queryById(Long id) {
        if (id == null || id < 0) {
            throw new ParamErrorException(String.format("参数错误，id: %d", id));
        }

        return categoryDAO.queryById(id);
    }

    @Override
    public CategoryDO queryByName(String name) {
        return null;
    }

    @Override
    public List<CategoryDO> listAll() {
        return categoryDAO.listAll();
    }

    @Override
    public List<FlatCategory> queryAllFlatCategory(String bizEntityName) {
        return categoryDAO.queryAllFLatCategory(bizEntityName);
    }

    @Override
    public CategoryTree queryCategoryTree(String bizEntityName) {
        if (StringUtils.isBlank(bizEntityName)) {
            throw new ParamErrorException("构建目录出错，业务实体名称为空！");
        }
        List<FlatCategory> data = queryAllFlatCategory(bizEntityName);
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        // 使用新分类
        List<FlatCategory> newData = data.stream().filter(flatCategory -> Objects.nonNull(flatCategory.getThirdId())
                && flatCategory.getThirdId() > 220).collect(Collectors.toList());
        return CategoryTree.build(newData);

        // 不需要统计分类数量
//        Map<Long, Long> labelNumOfCategory = labelInfoService.queryAllNumByCategory();
//        if (tree.getRoot() != null) {
//            setTagNum(tree.getRoot(), labelNumOfCategory);
//        }
//        return tree;
    }

    /**
     * 给当前节点及其子节点设置挂载的标签数量，当前节点挂载的标签数量包括其所有子节点挂载标签数量。
     *
     * @param categoryNode     根节点
     * @param tagNumOfCategory 叶子目录和包含的标签数量的对应关系
     * @return 返回值是当前（叶子）节点挂载的标签数量，递归时需要该值。
     */
    private long setTagNum(CategoryNode categoryNode, Map<Long, Long> tagNumOfCategory) {
        long tagNum = 0L;
        if (CollectionUtils.isEmpty(categoryNode.getChildren())) {
            Long num = tagNumOfCategory.get(categoryNode.getId());
            tagNum = num == null ? 0L : num;
        } else {
            for (CategoryNode node : categoryNode.getChildren()) {
                tagNum += setTagNum(node, tagNumOfCategory);
            }
        }
        categoryNode.setTagNum(tagNum);
        return tagNum;
    }

    @Override
    public Boolean update(CategoryDO categoryDO) {
        if (categoryDO == null) {
            throw new ParamErrorException("缺少必要参数，categoryDO为null。");
        }

        return categoryDAO.update(categoryDO);
    }

    @Override
    public Long insert(CategoryDO categoryDO) {
        if (categoryDO == null) {
            throw new ParamErrorException("缺少必要参数，categoryDO为null。");
        }
        return categoryDAO.insert(categoryDO);
    }

    @Override
    public Boolean delete(Long id) {
        if (id == null) {
            throw new ParamErrorException("缺少必要参数，id为null。");
        }

        return categoryDAO.delete(id);
    }

    @Override
    public String queryCategoryName(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }

        CategoryDO categoryDO = queryById(id);
        if (Objects.isNull(categoryDO)) {
            return null;
        }

        String name = categoryDO.getPropertyName();
        while (Objects.nonNull(categoryDO) && Objects.nonNull(categoryDO.getParentId()) && categoryDO.getParentId() > 0) {
            categoryDO = queryById(categoryDO.getParentId());
            if (Objects.nonNull(categoryDO) && Objects.nonNull(categoryDO.getPropertyName())) {
                name = categoryDO.getPropertyName() + "-" + name;
            }
        }
        return name;
    }

    @AteyeInvoker(description = "新增标签类型", paraDesc = "propertyName&propertyDesc&empId&empName&parentId&isLeaf&level")
    public void ateyeInsert(String propertyName, String propertyDesc, String empId, String empName, Long parentId, Short isLeaf, String level) {
        CategoryDO categoryDO = new CategoryDO();
        categoryDO.setBizEntityName(ProfileEnum.TAOBAO_USER.name());
        categoryDO.setPropertyName(propertyName);
        categoryDO.setPropertyDescription(propertyDesc);
        Employee employee = new Employee(empId, empName);
        categoryDO.setCreator(employee);
        categoryDO.setOperator(Collections.singletonList(employee));
        categoryDO.setStatus(CategoryStatusEnum.ACTIVATE);
        categoryDO.setParentId(parentId);
        categoryDO.setIsLeaf(isLeaf);
        categoryDO.setLevel(level);
        Long insert = categoryDAO.insert(categoryDO);
        Ateye.out.print("insert result: " + insert);
    }

    @AteyeInvoker(description = "删除标签类型", paraDesc = "id")
    public void ateyeDelete(Long id) {
        Boolean delete = categoryDAO.delete(id);
        Ateye.out.print("delete result: " + delete);
    }
}

