package com.fliggy.picasso.service.enumvalue;

import com.fliggy.picasso.common.domain.label.enumvalue.*;
import com.fliggy.picasso.common.domain.label.enumvalue.vo.LabelEnumDimVO;
import com.fliggy.picasso.common.domain.label.enumvalue.vo.LabelEnumValueCreateVO;
import com.fliggy.picasso.common.domain.label.enumvalue.vo.LabelEnumValueUpdateVO;
import org.springframework.dao.DataAccessException;

import java.util.List;

/**
 * <p>
 *
 * <AUTHOR>
 * @date 2020-01-13 12:14
 */
public interface LabelEnumValueService {

    /**
     * 根据标签Code查询所有枚举值(叶子节点)
     *
     * @param labelCode
     * @return
     */
    List<EnumDimDataDTO> queryLeafByLabelCode(String labelCode);

    /**
     * @param labelCode
     * @param enumCode
     * @return
     */
    EnumDimDataDTO queryEnumValue(String labelCode, String enumCode);

    /**
     *
     * @param param
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<EnumDimMetaInfoDO> pageQueryDimMetaInfo(EnumDimMetaInfoParameter param, Integer pageNum,
                                                   Integer pageSize);

    /**
     *
     * @param code
     * @return
     */
    EnumDimMetaInfoDO queryByCode(String code);

    /**
     * @param enumMetaInfoId
     * @return
     */
    EnumDimMetaInfoDO queryByEnumMetaInfoId(Long enumMetaInfoId);

    /**
     *
     * @param enumDimMetaInfoDO
     * @return
     */
    Long addLabelEnumDimMetaInfo(EnumDimMetaInfoDO enumDimMetaInfoDO);

    /**
     * @param enumDimMetaInfoDO
     * @return
     */
    Boolean update(EnumDimMetaInfoDO enumDimMetaInfoDO);

    /**
     *
     * @param labelEnumMetaInfoDO
     * @return
     */
    Boolean updateLabelEnumDimMetaInfo(LabelEnumDimVO labelEnumMetaInfoDO);

    /**
     *
     * @param labelEnumValueDO
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<LabelEnumValueDO> pageQueryDimData(LabelEnumValueDO labelEnumValueDO, Integer pageNum, Integer pageSize);

    /**
     * 新增标签枚举值
     *
     * @param labelEnumValueCreateVO 标签枚举值信息
     * @return 复合索引 dimMetaId_enumId
     */
    String addLabelEnumDimData(LabelEnumValueCreateVO labelEnumValueCreateVO);

    /**
     * 更新单条枚举值信息
     *
     * @param labelEnumValueUpdateVO
     * @return
     */
    String updateLabelEnumDimData(LabelEnumValueUpdateVO labelEnumValueUpdateVO);

    Boolean updateLabelEnumDataDesc(Long id, String enumDesc);

    /**
     * 更新通过ODPS表定义的枚举值对象。
     *
     * 以enumCode为key进行增量更新：
     *  1. 如果key存在且value(enumDesc)不变则不更新，否则更新。
     *  2. 如果key不存在则新增。
     *  3. 如果新表中不存在该key，则从数据库中逻辑删除该条数据。
     *
     * @param id
     * @return
     */
    Boolean updateLabelEnumDimDataFromODPS(Long id);

    /**
     * 新增枚举值信息
     *
     * @param labelEnumDimVO
     * @return
     */
    Boolean addDimMeta(LabelEnumDimVO labelEnumDimVO);

    /**
     * 构建枚举值树形结构
     *
     * @param dimMetaId 枚举对象ID
     * @return 枚举值树
     */
    LabelEnumValueTree buildTree(Long dimMetaId);

    /**
     * 根据标签Code构建枚举值树形结构
     * @param labelCode
     * @return
     */
    LabelEnumValueTree buildLabelEnumTreeByLabelCode(String labelCode);

    /**
     * 通过index查询
     *
     * @return
     */
    EnumDimDataDO queryDimDataByIndex(Long dimMetaId, Long enumId);

    /**
     * 删除枚举值
     *
     * @param index 枚举值索引
     * @return 是否成功
     */
    Boolean deleteDimData(String index);

    /**
     * 查询所有枚举对象的数据业务日期，其中业务日期取该枚举对象的所有枚举值记录中的最小值。
     *
     * @return 枚举对象id -> 枚举对象的数据业务日期最小值
     * @throws DataAccessException 查询异常
     */
    List<LabelEnumValueAggregateByMetaId> queryEnumDataBizDs();

    /**
     * 使用Odps表导入枚举值
     *
     * @param dimMetaId 枚举值id
     * @param projectName odps项目名
     * @param tableName 表名
     * @param enumColumn 枚举code列名
     * @param enumDescColumn 枚举描述名，如果为空直接使用code
     * @return 导入数量
     */
    int importEnumValueByOdps(Long dimMetaId, String projectName, String tableName, String enumColumn, String enumDescColumn);

}
