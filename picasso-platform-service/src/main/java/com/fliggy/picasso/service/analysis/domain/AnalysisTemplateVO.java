package com.fliggy.picasso.service.analysis.domain;

import java.util.Date;
import java.util.List;

import com.fliggy.picasso.common.domain.Employee;
import lombok.Data;

/**
 * <AUTHOR>
 * 分析模板
 */
@Data
public class AnalysisTemplateVO {
    /**
     *   主键
     *
     * This field corresponds to the database column analysis_template.id
     */
    private Long id;

    /**
     *   创建时间
     *
     * This field corresponds to the database column analysis_template.gmt_create
     */
    private Date gmtCreate;

    /**
     *   修改时间
     *
     * This field corresponds to the database column analysis_template.gmt_modified
     */
    private Date gmtModified;

    /**
     *   名称
     *
     * This field corresponds to the database column analysis_template.name
     */
    private String name;

    /**
     *   描述
     *
     * This field corresponds to the database column analysis_template.description
     */
    private String description;

    /**
     *   创建人
     *
     * This field corresponds to the database column analysis_template.creator
     */
    private Employee creator;

    /**
     *   公开类型，0-公开，1-私有
     *
     * This field corresponds to the database column analysis_template.public_type
     */
    private Integer publicType;

    /**
     *   配置，json格式字符串
     *
     * This field corresponds to the database column analysis_template.config
     */
    private List<AnalysisTemplateSingleConfigBO> config;
}
