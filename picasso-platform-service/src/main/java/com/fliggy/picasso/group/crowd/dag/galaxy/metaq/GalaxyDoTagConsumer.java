package com.fliggy.picasso.group.crowd.dag.galaxy.metaq;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.Vector;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.common.message.MessageExt;

import com.fliggy.picasso.alarm.dingcard.DingTalkEventTypeEnum;
import com.fliggy.picasso.alarm.dingcard.DingTalkNotifyManager;
import com.fliggy.picasso.alarm.dingcard.DingTalkReq;
import com.fliggy.picasso.common.utils.DateUtils;
import com.fliggy.picasso.dao.ActivityDO;
import com.fliggy.picasso.dao.TravelActivityTagDO;
import com.fliggy.picasso.group.crowd.dag.galaxy.config.GalaxySwitchConfig;
import com.fliggy.picasso.group.crowd.dag.galaxy.ha3.constants.Constants;
import com.fliggy.picasso.group.crowd.dag.galaxy.metaq.domain.GalaxyDoTagMsg;
import com.fliggy.picasso.group.crowd.dag.galaxy.service.ActivityService;
import com.fliggy.picasso.group.crowd.dag.galaxy.service.TravelActivityTagManager;
import com.fliggy.picasso.group.crowd.dag.galaxy.utils.GalaxyItemBizConvertor;
import com.fliggy.picasso.group.crowd.dag.galaxy.utils.LoggerUtil;
import com.fliggy.picasso.group.dispatch.CommonDistributeLockManager;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.taobao.ateye.annotation.Switch;
import com.taobao.metaq.client.MetaPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


/**
 * 星辰打标消费者
 *
 * <AUTHOR>
 * Created on 2021/8/17 下午12:52
 */
@Component
@Slf4j
public class GalaxyDoTagConsumer {

    public static final String TOPIC = "TRIP_GALAXY_DO_TAG_V2";
    public static final String GROUP_NAME = "TRIP_GALAXY_DO_TAG_PICASSO_CONSUMER";


    public static final String UPDATE_DO = "update";
    public static final String INSERT_DO = "insert";
    public static final String UPDATE_ACT_STATUS = "updateActStatus";
    public static final String ADD_TAG = "addTag";
    public static final String DEL_TAG = "delTag";

    private MetaPushConsumer consumer;

    @Switch(name = "publishSwitch", description = "星辰走新发布")
    public static boolean newPublish = true;

    @Resource
    private ActivityService activityService;

    @Resource
    private TravelActivityTagManager travelActivityTagManager;

    @Resource
    private CommonDistributeLockManager lockManager;


    public static final ExecutorService threadPoolExecutor = new ThreadPoolExecutor(50, 60,
            300, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(4096),
            runnable -> {
                Thread t = new Thread(runnable);
                t.setName("do_tag_executor");
                return t;
            }, new ThreadPoolExecutor.CallerRunsPolicy());


    @PostConstruct
    public void initConsumer() throws MQClientException {
        if (consumer != null) {
            return;
        }
        //创建Consumer、ConsumerGroupName需要由应用来保证唯一
        consumer = new MetaPushConsumer(GROUP_NAME);
//        consumer.getMetaPushConsumerImpl().setConsumeTimeout(60L);
        //订阅消息总线消息
        consumer.subscribe(TOPIC, "*");
        consumer.registerMessageListener((MessageListenerConcurrently) (msgList, context) -> {
            int reConsumeTime;
            for (MessageExt msg : msgList) {
                reConsumeTime = msg.getReconsumeTimes();
                if (reConsumeTime >= 5) {
                    DingTalkReq req = DingTalkReq.builder().msg("星辰打标重试三次依然执行失败.请查看并处理: " + new String(msg.getBody(), StandardCharsets.UTF_8)).build();
                    DingTalkNotifyManager.sendDingMsg(req, DingTalkEventTypeEnum.ALARM);
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                try {
                    handleMessage(msg);
                } catch (Exception e) {
                    // 出现异常设置重试，每次失败在进行重试相隔时间增加
                    LoggerUtil.errorV2("do tag metaq error. msg:{}", e.getMessage(), e);
                    // 10秒后重试
                    context.setDelayLevelWhenNextConsume(3);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
        // 一定要调用start
        consumer.start();
    }

    private void handleMessage(MessageExt msg) {
        String body = new String(msg.getBody(), StandardCharsets.UTF_8);
        GalaxyDoTagMsg doTagMsg = JSON.parseObject(body, GalaxyDoTagMsg.class);
        if (Objects.isNull(doTagMsg)) {
            LoggerUtil.errorV2("doTagMsg is null. msg: {}", msg);
            return;
        }
        String operationType = doTagMsg.getOperationType();
        if (Objects.nonNull(doTagMsg.getActivityId()) && CollectionUtils.isNotEmpty(doTagMsg.getBizIdList())) {
            LoggerUtil.errorV2("活动发布打标. actId:{},type:{},offset:{},count:{}", doTagMsg.getActivityId(), doTagMsg.getOperationType(), doTagMsg.getOffset(), doTagMsg.getBizIdList().size());
        }
        // 这里对有问题池子进行过滤
        if (!checkActivity(doTagMsg)) {
            LoggerUtil.infoV2("活动发布节点, 对有问题池子进行过滤, 请联系选品池owner并处理. actId:{},type:{},offset:{},count:{}", doTagMsg.getActivityId(), doTagMsg.getOperationType(), doTagMsg.getOffset(), doTagMsg.getBizIdList().size());
            return;
        }

        if (ADD_TAG.equals(operationType)) {
            handleAddTag(doTagMsg);
        } else if (DEL_TAG.equals(operationType)) {
            handleDelTag(doTagMsg);
        } else if (UPDATE_ACT_STATUS.equals(operationType)) {
            handleActStatus(doTagMsg);
        }
    }


    private boolean checkActivity(GalaxyDoTagMsg doTagMsg) {
        if (Objects.nonNull(doTagMsg.getActivityId())) {
            return !Arrays.asList(GalaxySwitchConfig.needIgnoreActIdList.split(",")).contains(doTagMsg.getActivityId().toString());
        }
        return true;
    }

    private void handleActStatus(GalaxyDoTagMsg doTagMsg) {
        // 处理活动状态更新消息, 为什么要放到这里， 因为尽可能保证活动状态更新与打标进度gap较小
        if (Objects.nonNull(doTagMsg.getActivityId()) && Objects.nonNull(doTagMsg.getActStatus())) {
            long publishEndTime = System.currentTimeMillis();
            ActivityDO newActivityDO = new ActivityDO();
            newActivityDO.setId(doTagMsg.getActivityId());
            newActivityDO.setStatus(doTagMsg.getActStatus());
            newActivityDO.setChangeTime(String.valueOf(System.currentTimeMillis()));
            Long res = activityService.saveOrUpdate(newActivityDO);
            LoggerUtil.doTraceLog("发布耗时统计", "活动id:" + doTagMsg.getActivityId(), "商品数:" + doTagMsg.getItemCount(), "耗时ms:" + (publishEndTime - doTagMsg.getStartTime()));
            if (GalaxySwitchConfig.publishActDingDingSwitch) {
//                dingTalkManager.sendMessage("星辰发布耗时统计" + ",活动id:" + doTagMsg.getActivityId() + ",活动名称:" + doTagMsg.getActTitle() + ",活动类型:" + doTagMsg.getActType() + ",商品数:" + doTagMsg.getItemCount() + ", 操作人:" + doTagMsg.getOperator() + ",耗时ms:" + (publishEndTime - doTagMsg.getStartTime()));
                String cardMsg = String.format("###  星辰选品通知  \n您的选品发布完成  \n选品池id: %s  \n选品池名称: %s  \n选品池类型: %s  \n选品数量: %s  \n操作人: %s  \n发布耗时: %s"
                        , doTagMsg.getActivityId(), doTagMsg.getActTitle(), doTagMsg.getActType(), doTagMsg.getItemCount(), doTagMsg.getOperator(), DateUtils.formatTime(publishEndTime - doTagMsg.getStartTime()));
                if ("RP".equals(doTagMsg.getActType())) {
                    cardMsg += "RP选品为接口调用, 耗时较高.";
                }
                ;
                DingTalkNotifyManager.sendDingMsg(DingTalkReq.builder().groupId(doTagMsg.getActivityId()).msg(cardMsg).empId(doTagMsg.getEmpId()).build(), DingTalkEventTypeEnum.TEXT, DingTalkEventTypeEnum.ACT_PUBLISH);
            }
            if (res != 0) {
                return;
            }
            throw new RuntimeException("activity update status error!" + doTagMsg);
        }
    }


    /**
     * 从活动池子中删除商品
     *
     * @param doTagMsg
     */
    private void handleDelTag(GalaxyDoTagMsg doTagMsg) {

        List<String> bizIdList = doTagMsg.getBizIdList();
        String dataSource = doTagMsg.getDataSource();
        String tag = doTagMsg.getType() + Constants.Symbol.COLON + doTagMsg.getActivityId();

        List<TravelActivityTagDO> travelActivityTagDOs = travelActivityTagManager.getAllByBizIds(bizIdList);
        // 这里要根据物料类型进行过滤
        travelActivityTagDOs = travelActivityTagDOs.stream().filter(p -> StringUtils.equals(p.getType(), dataSource)).collect(Collectors.toList());

        Vector<String> delErrorItemIdList = new Vector<>();
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(travelActivityTagDOs)) {
            travelActivityTagDOs.forEach(p -> futureList.add(CompletableFuture.supplyAsync(handleDoTag(UPDATE_DO, p, tag, true), threadPoolExecutor)
                    .thenAccept(result -> handleThenAccept(result, delErrorItemIdList))));
        }
        try {
            if (CollectionUtils.isNotEmpty(futureList)) {
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            }
        } catch (Exception e) {
            LoggerUtil.errorV2("do tag handleDelTag打标CompletableFuture.allOf get异常." + JSON.toJSONString(doTagMsg), e);
        }
        if (CollectionUtils.isNotEmpty(delErrorItemIdList)) {
            doTagMsg.setBizIdList(delErrorItemIdList);
            throw new RuntimeException("handleDelTag need retry." + JSON.toJSONString(doTagMsg));
        }
    }

    public void handleAddTag(GalaxyDoTagMsg doTagMsg) {
        List<String> bizIdList = doTagMsg.getBizIdList();
        String dataSource = doTagMsg.getDataSource();
        List<CompletableFuture<Void>> updateFutures = new ArrayList<>();
        Vector<String> handleErrorItemIds = new Vector<>();
        String tag = doTagMsg.getType() + Constants.Symbol.COLON + doTagMsg.getActivityId();

        List<TravelActivityTagDO> travelActivityTagDOs = travelActivityTagManager.getAllByBizIds(bizIdList);
        // 这里要根据物料类型进行过滤
        travelActivityTagDOs = travelActivityTagDOs.stream().filter(p -> StringUtils.equals(p.getType(), dataSource)).collect(Collectors.toList());
        List<String> curExistBizIdList = travelActivityTagDOs.stream().map(TravelActivityTagDO::getBizId).collect(Collectors.toList());
        Set<String> needInsertItemList = bizIdList.stream().filter(o -> !curExistBizIdList.contains(o)).collect(Collectors.toSet());

        // 处理需要更新的商品记录
        travelActivityTagDOs.forEach(p -> updateFutures.add(CompletableFuture.supplyAsync(handleDoTag(UPDATE_DO, p, tag, false), threadPoolExecutor)
                .thenAccept(result -> handleThenAccept(result, handleErrorItemIds))));
        // 处理需要增加的商品记录
        needInsertItemList.forEach(bizId -> {
            TravelActivityTagDO travelActivityTagDO = new TravelActivityTagDO();
            travelActivityTagDO.setItemId(GalaxyItemBizConvertor.convert2ItemIdWithPre(bizId));
            travelActivityTagDO.setSelectionTag(tag);
            travelActivityTagDO.setType(dataSource);
            travelActivityTagDO.setBizId(bizId);
            updateFutures.add(CompletableFuture.supplyAsync(handleDoTag(INSERT_DO, travelActivityTagDO, tag, false), threadPoolExecutor)
                    .thenAccept(result -> handleThenAccept(result, handleErrorItemIds)));
        });

        try {
            if (CollectionUtils.isNotEmpty(updateFutures)) {
                CompletableFuture.allOf(updateFutures.toArray(new CompletableFuture[0])).join();
            }
        } catch (Exception e) {
            LoggerUtil.errorV2("do tag handleAddTag打标CompletableFuture.allOf get异常." + JSON.toJSONString(doTagMsg), e);
        }

        if (CollectionUtils.isNotEmpty(handleErrorItemIds)) {
            doTagMsg.setBizIdList(handleErrorItemIds);
            throw new RuntimeException("handleDelTag need retry." + JSON.toJSONString(doTagMsg));
        }
    }

    private Supplier<Result> handleDoTag(String operationType, TravelActivityTagDO travelActivityTagDO, String tag, boolean isDel) {
        return () -> {
            String lockKey = "galaxy_do_tag_" + travelActivityTagDO.getBizId();
            boolean locked = false;
            try {
                // 处理打标消息
                if (INSERT_DO.equals(operationType)) {
                    locked = lockManager.tryGetDistributedLock(lockKey, "1", 60);
                    if (!locked) {
                        return Result.buildFail(travelActivityTagDO.getBizId());
                    }
                    travelActivityTagDO.setVersion(1L);
                    // 加分布式锁，避免并发对一个商品打多个标走到这里导致数据覆盖
                    // 分布式锁锁定处理
                    TravelActivityTagDO originDO = travelActivityTagManager.getByBizId(travelActivityTagDO.getBizId());
                    Integer res = 0;
                    if (Objects.nonNull(originDO)) {
                        travelActivityTagDO.setId(originDO.getId());
                        res = doUpdateTag(travelActivityTagDO, tag, isDel, originDO);
                    } else {
                        res = travelActivityTagManager.batchInsert(Collections.singletonList(travelActivityTagDO));
                    }
                    if (Objects.isNull(res) || res != 1) {
                        log.error("handleDoTag INSERT_DO error.bizId:{}, travelActivityTagDO:{}", travelActivityTagDO.getBizId(), JSON.toJSON(travelActivityTagDO));
                        LoggerUtil.errorV2("insert error. bizId:{}, travelActivityTagDO:{}", travelActivityTagDO.getBizId(), JSON.toJSON(travelActivityTagDO));
                        new GalaxyDoTagConsumer.Result();
                        return Result.buildFail(travelActivityTagDO.getBizId());
                    }
                }
                if (UPDATE_DO.equals(operationType)) {
                    Integer doUpdateResult = doUpdateTag(travelActivityTagDO, tag, isDel, travelActivityTagDO);
                    if (Objects.isNull(doUpdateResult) || doUpdateResult != 1) {
                        Integer update = updateRetryer.call(() -> {
                            TravelActivityTagDO activityTagDO = travelActivityTagManager.getByBizId(travelActivityTagDO.getBizId());
                            if (Objects.isNull(activityTagDO)) {
                                LoggerUtil.errorV2("handleDoTag update error.isDel:{}, tag:{} ", isDel, tag);
                                return 0;
                            }
                            return doUpdateTag(travelActivityTagDO, tag, isDel, activityTagDO);
                        });
                        if (Objects.isNull(update) || update != 1) {
                            LoggerUtil.errorV2("update error. bizId:{}, travelActivityTagDO:{}", travelActivityTagDO.getBizId(), JSON.toJSON(travelActivityTagDO));
                            return Result.buildFail(travelActivityTagDO.getBizId());
                        }
                    }
                }
                return Result.buildSuccess();
            } catch (Exception e) {
                return Result.buildFail(travelActivityTagDO.getBizId());
            } finally {
                if (locked) {
                    lockManager.releaseDistributedLock(lockKey, "1");
                }
            }
        };
    }

    private Integer doUpdateTag(TravelActivityTagDO travelActivityTagDO, String tag, boolean isDel, TravelActivityTagDO activityTagDO) {
        boolean isContains = (Constants.Symbol.MULTI_SPLITOR + activityTagDO.getSelectionTag() + Constants.Symbol.MULTI_SPLITOR).contains(Constants.Symbol.MULTI_SPLITOR + tag + Constants.Symbol.MULTI_SPLITOR);
        // 判断是删除还是增加标
        if (isDel) {
            // 如果已经没有这个tag
            if (!isContains) {
                return 1;
            }
            String newTag = (activityTagDO.getSelectionTag() + Constants.Symbol.MULTI_SPLITOR).replace(tag + Constants.Symbol.MULTI_SPLITOR, "");
            if (StringUtils.isNotBlank(newTag) && newTag.endsWith(String.valueOf(Constants.Symbol.MULTI_SPLITOR))) {
                newTag = newTag.substring(0, newTag.length() - 1);
            }
            activityTagDO.setSelectionTag(newTag);
            if (StringUtils.isBlank(travelActivityTagDO.getSelectionTag())) {
                // 删除
                return travelActivityTagManager.batchDelete(Collections.singletonList(activityTagDO.getId()));
            }
        } else {
            if (isContains) {
                return 1;
            }
            activityTagDO.setSelectionTag(activityTagDO.getSelectionTag() + Constants.Symbol.MULTI_SPLITOR + tag);
        }
        long curVersion = Objects.isNull(activityTagDO.getVersion()) ? Long.valueOf(1) : activityTagDO.getVersion();
        return travelActivityTagManager.updateByCurVersion(activityTagDO, curVersion);
    }


    static class Result {
        private Boolean success;
        private String bizId;

        public static Result buildSuccess() {
            Result result = new Result();
            result.setSuccess(true);
            return result;
        }

        public static Result buildFail(String bizId) {
            Result result = new Result();
            result.setSuccess(false);
            result.setBizId(bizId);
            return result;
        }

        public Result() {
        }

        public Boolean getSuccess() {
            return success;
        }

        public void setSuccess(Boolean success) {
            this.success = success;
        }

        public String getBizId() {
            return bizId;
        }

        public void setBizId(String bizId) {
            this.bizId = bizId;
        }

    }

    private BiConsumer<Result, ? super Throwable> handleComplete(Vector<String> itemList) {
        return (result, e) -> {
            if (!result.getSuccess() && Objects.nonNull(result.getBizId())) {
                itemList.add(result.getBizId());
            }
        };
    }


    private void handleThenAccept(Result result, Vector<String> itemList) {
        if (!result.getSuccess() && Objects.nonNull(result.getBizId())) {
            itemList.add(result.getBizId());
        }
    }


    @PreDestroy
    public void destroy() {
        if (consumer != null) {
            consumer.shutdown();
        }
    }


    /**
     * 更新重试器
     */
    private static final Retryer<Integer> updateRetryer =
            RetryerBuilder.<Integer>newBuilder()
                    .retryIfResult(p -> Objects.isNull(p) || p != 1)
                    .retryIfException()
                    .retryIfRuntimeException()
                    .withWaitStrategy(WaitStrategies.fixedWait(2, TimeUnit.SECONDS))
                    .withStopStrategy(StopStrategies.stopAfterAttempt(10))
                    .build();


    public void handleTagMsg(GalaxyDoTagMsg doTagMsg) {
        if (Objects.isNull(doTagMsg)) {
            LoggerUtil.errorV2("doTagMsg is null. msg: {}", doTagMsg);
            return;
        }
        String operationType = doTagMsg.getOperationType();
        if (Objects.nonNull(doTagMsg.getActivityId()) && CollectionUtils.isNotEmpty(doTagMsg.getBizIdList())) {
            LoggerUtil.errorV2("活动发布打标. actId:{},type:{},offset:{},count:{}", doTagMsg.getActivityId(), doTagMsg.getOperationType(), doTagMsg.getOffset(), doTagMsg.getBizIdList().size());
        }
        // 这里对有问题池子进行过滤
        if (!checkActivity(doTagMsg)) {
            LoggerUtil.infoV2("活动发布节点, 对有问题池子进行过滤, 请联系选品池owner并处理. actId:{},type:{},offset:{},count:{}", doTagMsg.getActivityId(), doTagMsg.getOperationType(), doTagMsg.getOffset(), doTagMsg.getBizIdList().size());
            return;
        }

        if (ADD_TAG.equals(operationType)) {
            handleAddTag(doTagMsg);
        } else if (DEL_TAG.equals(operationType)) {
            handleDelTag(doTagMsg);
        } else if (UPDATE_ACT_STATUS.equals(operationType)) {
            handleActStatus(doTagMsg);
        }
    }

    public static void main(String[] args) {
        System.out.println(1);
    }
}