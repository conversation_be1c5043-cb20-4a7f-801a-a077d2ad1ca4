package com.fliggy.picasso.datachange;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.fliggy.picasso.client.entity.enums.CrowdDataChangeTag;
import com.fliggy.picasso.client.entity.metaq.CrowdDataChangeDTO;
import com.fliggy.picasso.common.domain.crowd.CrowdMetaInfoDO;
import com.fliggy.picasso.common.domain.label.LabelInfoDO;
import com.fliggy.picasso.msg.MetaqMessageSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * 人群数据变更 aop
 * 投递 metaq 消息让 tripcrowd 感知后做 tair 缓存刷新
 *
 * <AUTHOR>
 * @date 2024/12/6
 */
@Aspect
@Component
@Slf4j
public class CrowdDataChangeAspect {
    @Resource
    private MetaqMessageSender crowdDataChangeSender;

    @AfterReturning("execution(* com.fliggy.picasso.mapper.tripcrowd.CrowdMapper.update(..))")
    public void crowdAfterUpdate(JoinPoint joinPoint) {
        try {
            CrowdMetaInfoDO crowdMetaInfoDO = (CrowdMetaInfoDO)joinPoint.getArgs()[0];
            sendMessage(crowdMetaInfoDO.getId().toString(), CrowdDataChangeTag.CROWD);
            log.info("crowdAfterUpdate crowdMetaInfoDO:{}", JSON.toJSONString(crowdMetaInfoDO));
        } catch (Exception e) {
            log.error("crowdAfterUpdate error", e);
        }
    }

    @AfterReturning("execution(* com.fliggy.picasso.mapper.picasso.LabelInfoDAO.updateByParamSelective(..))")
    public void labelInfoAfterUpdate(JoinPoint joinPoint) {
        try {
            LabelInfoDO labelInfoDO = (LabelInfoDO)joinPoint.getArgs()[0];
            sendMessage(labelInfoDO.getCode(), CrowdDataChangeTag.LABEL);
            log.info("labelInfoAfterUpdate labelInfoDO:{}", JSON.toJSONString(labelInfoDO));
        } catch (Exception e) {
            log.error("labelInfoAfterUpdate error", e);
        }
    }

    private void sendMessage(String dataId, CrowdDataChangeTag tag) {
        if (StringUtils.isBlank(dataId)) {
            return;
        }
        String key = tag.name() + "_" + dataId + "_" + System.currentTimeMillis();
        crowdDataChangeSender.sendMessage(getData(dataId), key, tag.name());
    }

    private String getData(String dataId) {
        CrowdDataChangeDTO crowdDataChangeDTO = new CrowdDataChangeDTO();
        crowdDataChangeDTO.setDataId(dataId);
        return JSON.toJSONString(crowdDataChangeDTO);
    }
}
