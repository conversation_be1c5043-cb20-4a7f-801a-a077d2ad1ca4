<#-- HOLO物理画像表外表分区表同步 -->
SET hg_experimental_affect_row_multiple_times_keep_first = on;
set hg_experimental_query_batch_size = 1024;
set hg_experimental_foreign_table_executor_max_dop = 8;
INSERT INTO ${tableName}_${partitionValue}
SELECT
<#list columns as column>
    ${column.oriName} AS ${column.aliasName}  <#if column_has_next>,</#if>
</#list>
FROM foreign_sample_${tableName}
WHERE ${partitionCode} = '${partitionValue}'
ON CONFLICT(${primaryKey},${partitionCode}) do nothing;
