<#-- HOLO创建内表 -->
BEGIN;
CREATE TABLE IF NOT EXISTS ${tableName}
(
<#list columns as column>
    ${column.name}  ${column.type.typeStr} ,
</#list>
PRIMARY KEY (${primaryKey},${partitionKey})
)
PARTITION BY LIST (${partitionKey})
;
CALL SET_TABLE_PROPERTY('${tableName}', 'time_to_live_in_seconds', '${ttlInSeconds?c}');
CALL SET_TABLE_PROPERTY('${tableName}', 'distribution_key', '${distributionKey}');
CALL SET_TABLE_PROPERTY('${tableName}', 'clustering_key', '${distributionKey}');
<#if bitmapColumns??>CALL SET_TABLE_PROPERTY('${tableName}', 'bitmap_columns', '${bitmapColumns}');<#else></#if>
COMMIT;