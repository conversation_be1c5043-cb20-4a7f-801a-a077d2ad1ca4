
<#if create==true??>
    CREATE TABLE IF NOT EXISTS profile_ha3_table
    (
    biz_id STRING COMMENT '物料id',
    type STRING COMMENT '类型标识',
    value STRING COMMENT '大value'
    )
    COMMENT 'ha3大宽表'
    PARTITIONED BY
    (
    ds STRING COMMENT '分区'
    )
    LIFECYCLE 15
    ;
</#if>

INSERT OVERWRITE TABLE trip_profile.profile_ha3_table PARTITION (ds='${bizdate}.done')

select *
from (
    <#list profileInfos as profile>
    select ${profile.profileTableInfo.primaryKey},'${profile.profileTableInfo.typeKey}' as type,${profile.flatField} as value
    from (
        select ${profile.profileTableInfo.tableAlias}.${profile.profileTableInfo.primaryKey}
            ,'${profile.profileTableInfo.typeKey}'
            ,<#list profile.labelFields as labelField>
                ${labelField} <#if labelField_has_next>,</#if>
            </#list>
        from (
            select ${profile.baseTableInfo.primaryKey}
            from ${profile.baseTableInfo.project}.${profile.baseTableInfo.table}
<#--            where ${profile.baseTableInfo.partitionKey} = ${bizdate}-->
            where ${profile.baseTableInfo.partitionKey} = max_pt('${profile.baseTableInfo.project}.${profile.baseTableInfo.table}')
        )as ${profile.baseTableInfo.tableAlias}
        left join (
            SELECT  *
            FROM    ${profile.profileTableInfo.project}.${profile.profileTableInfo.table}
            WHERE   ${profile.profileTableInfo.partitionKey} = ${bizdate}
        ) AS ${profile.profileTableInfo.tableAlias}
        ON   ${profile.profileTableInfo.tableAlias}.${profile.profileTableInfo.primaryKey} = ${profile.baseTableInfo.tableAlias}.${profile.baseTableInfo.primaryKey}
    )
    <#if profile_has_next>UNION ALL</#if>
    </#list>
    )
;

