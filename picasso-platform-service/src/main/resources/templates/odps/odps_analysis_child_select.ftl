<#-- ODPS建表 -->

SELECT ${childTagNames},COUNT(a.user_id) AS value
from
<#--查询当前人群下上级指标分析的人群列表-->
(
    (
        select user_id from trip_profile.dwd_trip_crowd_data_snapshot where group_id = '${crowdId}' group by user_id
    ) a
    INNER JOIN
    <#--在上一级的画像条件命中人群中 查询符合当前子画像的人群-->
    (
        SELECT DISTINCT user_id , ${childTagNames} FROM trip_profile.trip_picasso_taobao_user_profile
        WHERE ds = MAX_PT('trip_profile.trip_picasso_taobao_user_profile')
        <#-- 父tag值细分-->
        and
        <#list parentTagList as pt>
            ${pt.name}  = '${pt.value}'
            <#if pt_has_next>and</#if>
        </#list>
    ) b
    on a.user_id = b.user_id
)
GROUP BY ${childTagNames}
<#if sc??>ORDER BY value ${sc}</#if>
<#if limitCount??>LIMIT ${limitCount} OFFSET 0</#if>
;