<#-- ODPS SQL 根据群组快照构建分页索引数据 -->
INSERT OVERWRITE TABLE trip_profile.trip_zhuge_group_ids_page PARTITION (group_id= '${groupId}', time='${version}')
SELECT
CONCAT(SUBSTR(MD5(CONCAT_WS('-','${groupId}','${version}',page_num)),1,4),'-',CONCAT_WS('-','${groupId}','${version}',page_num)),
wm_concat(',',user_id),
page_num
FROM (
SELECT user_id,t.num,FLOOR((t.num-1)/1000)+1 as page_num
FROM (
SELECT user_id,ROW_NUMBER() OVER() as num
FROM trip_profile.dwd_trip_crowd_snapshot_${groupId}
) t
ORDER BY t.num
) a
GROUP BY page_num;