INSERT OVERWRITE TABLE ${syncPreTable} PARTITION (ds='${bizdate}')

select concat('${profileInfo.profileTableInfo.typePrefix}', ${profileInfo.profileTableInfo.primaryKey}) as biz_id
<#if emptyProfile==true ??>
    ,''
<#else>
    ,${profileInfo.flatField} as value
</#if>
from (
    <#if emptyProfile==true ??>
        select ${profileInfo.baseTableInfo.primaryKey}
        from ${profileInfo.baseTableInfo.project}.${profileInfo.baseTableInfo.table}
        where ${profileInfo.baseTableInfo.partitionKey} = '${profileInfo.baseTableInfo.partitionValue}'
        and ${profileInfo.baseTableInfo.primaryKey} is not null
    <#else>
        select ${profileInfo.profileTableInfo.tableAlias}.${profileInfo.profileTableInfo.primaryKey}
        ,<#list profileInfo.labelFields as labelField>
        ${labelField} <#if labelField_has_next>,</#if>
        </#list>
        from (
            select ${profileInfo.baseTableInfo.primaryKey}
            from ${profileInfo.baseTableInfo.project}.${profileInfo.baseTableInfo.table}
            where ${profileInfo.baseTableInfo.partitionKey} = '${profileInfo.baseTableInfo.partitionValue}'
            and ${profileInfo.baseTableInfo.primaryKey} is not null
        )as ${profileInfo.baseTableInfo.tableAlias}
        left join (
            select *
            from ${profileInfo.profileTableInfo.project}.${profileInfo.profileTableInfo.table}
            where ${profileInfo.profileTableInfo.partitionKey} = '${profileInfo.profileTableInfo.partitionValue}'
        ) as ${profileInfo.profileTableInfo.tableAlias}
        on ${profileInfo.profileTableInfo.tableAlias}.${profileInfo.profileTableInfo.primaryKey} = ${profileInfo.baseTableInfo.tableAlias}.${profileInfo.baseTableInfo.primaryKey}
    </#if>
)
;