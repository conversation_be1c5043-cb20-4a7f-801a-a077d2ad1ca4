<#-- ODPS物理画像标签JSON表数据加载 -->
<#-- ODPS物理画像表数据加载 -->
INSERT OVERWRITE TABLE ${jsonTable} PARTITION (ds = '${ds}', physical_profile = '${physicalProfileCode}')
SELECT
CONCAT(SUBSTR(MD5(CONCAT_WS('-','${tairKeyPrefix}',${primaryKey})),1,4),'-',CONCAT_WS('-','${tairKeyPrefix}',${primaryKey})) AS key,
resolve_data_as_json_filter_null(
<#list columns as column>
    ${column} <#if column_has_next>,</#if>
</#list>
) AS value
FROM ${physicalProfileTable}
WHERE ds = '${ds}'
;