<#-- ODPS建表 -->

<#--SELECT purchase_level_htl , tq_score_type , COUNT(a.object_id) AS picasso_group_user_cnt-->
<#--FROM-->
<#--    ( SELECT 1 ,a.object_id FROM-->
<#--        ( SELECT 1 , a.object_id FROM (select object_id from fliggy_data.public.trip_zhuge_group_data where group_id = '78677') a WHERE 1 = 1 ) a-->
<#--    ) a-->
<#--INNER JOIN-->
<#--    ( SELECT user_id , purchase_level_htl AS purchase_level_htl , tq_score_type AS tq_score_type FROM fliggy_data.public.trip_picasso_taobao_user_profile-->
<#--        WHERE ds = MAX_PT('fliggy_data.public.trip_picasso_taobao_user_profile')-->
<#--    ) b0-->
<#--ON CAST(a.object_id AS TEXT) = CAST(b0.user_id AS TEXT) GROUP BY purchase_level_htl , tq_score_type LIMIT 100 OFFSET 0-->
<#--ORDER BY purchase_level_htl , tq_score_type DESC-->


SELECT ${tagNames},COUNT(a.user_id) AS value
FROM
    (
        select user_id from trip_profile.dwd_trip_crowd_data_snapshot where group_id = '${crowdId}' group by user_id
    ) a
INNER JOIN
    (
        SELECT user_id , ${tagNames} FROM trip_profile.trip_picasso_taobao_user_profile
        WHERE ds = MAX_PT('trip_profile.trip_picasso_taobao_user_profile')
        group by user_id,${tagNames}
    ) b
ON a.user_id  = b.user_id
GROUP BY ${tagNames}
<#if sc??>ORDER BY value ${sc}</#if>
<#if limitCount??>LIMIT ${limitCount} OFFSET 0</#if>
;