<#-- ODPS建表 -->
CREATE TABLE IF NOT EXISTS ${projectName}.${tableName}
(
<#list columns as column>
${column.name}  ${column.type.typeStr} COMMENT '${column.comment}'  <#if column_has_next>,</#if>
</#list>
)
<#if comment??>COMMENT '${comment}' </#if>
<#if partitionColumn??>PARTITIONED BY
    (
    ${partitionColumn.name}  ${partitionColumn.type} COMMENT '${partitionColumn.comment}'
    ) </#if>
<#if clusteredColumn??>CLUSTERED BY (${clusteredColumn.name}) SORTED by (${clusteredColumn.name}) INTO ${bucketNum!512} BUCKETS </#if>
<#if lifecycle??>lifecycle ${lifecycle} </#if>
;