SELECT  relation_uid AS user_id
FROM    (
            SELECT  relation_uid
                    ,min(rk) AS rk
                    ,max(score) AS score
            FROM    (
                        SELECT  origin_uid
                                ,relation_uid
                                ,ROW_NUMBER()OVER (PARTITION BY origin_uid ORDER BY score DESC ) AS rk
                                ,score
                        FROM    (
                                    SELECT  zhuge.user_id AS origin_uid
                                            ,u2u.userid_2 AS relation_uid
                                            ,u2u.score AS score
                                    FROM    (
                                                SELECT  user_id
                                                FROM    trip_profile.dwd_trip_crowd_data_snapshot
                                                WHERE   group_id = ${seedCrowdId}
                                                AND     user_id IS NOT NULL
                                            ) AS zhuge INNER
                                    JOIN    (
                                                SELECT  cast(userid_1 AS STRING ) AS userid_1
                                                        ,cast(userid_2 AS STRING ) AS userid_2
                                                        ,score
                                                FROM    trip_platform.wh_user_relation
                                                WHERE   ds = MAX_PT('trip_platform.wh_user_relation')
                                                AND     userid_1 IS NOT NULL
                                                AND     userid_2 IS NOT NULL
                                            ) AS u2u
                                    ON      u2u.userid_1 = zhuge.user_id
                                    <#if containsOriginCrowd == false>
                                        -- 如果要剔除原始人群, 增加条件
                                        AND     u2u.userid_2 != zhuge.user_id
                                    </#if>
                                )
                        <#if containsOriginCrowd == true>
                            -- 如果要包含原始人群 则需要union all以下
                            UNION ALL
                            SELECT  user_id AS origin_uid
                            ,user_id relation_uid
                            ,cast(1 AS DOUBLE)
                            ,cast(1 AS DOUBLE)
                            FROM    trip_profile.dwd_trip_crowd_data_snapshot
                            WHERE   group_id = ${seedCrowdId}
                            AND     user_id IS NOT NULL
                        </#if>

                    )
            <#if exceptCrowdId??>
                -- 如果要剔除except人群
                LEFT ANTI join (
                SELECT  user_id
                FROM    trip_profile.dwd_trip_crowd_data_snapshot
                WHERE   group_id = ${exceptCrowdId}
                AND     user_id IS NOT NULL
                ) AS except_crowd
                ON      except_crowd.user_id = relation_uid
            </#if>
            GROUP BY relation_uid
        )
        ORDER BY rk ASC
        ,score DESC
        LIMIT   ${limitCount?c}

