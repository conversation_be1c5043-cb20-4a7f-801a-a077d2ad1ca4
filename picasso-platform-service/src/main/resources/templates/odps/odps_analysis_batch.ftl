<#-- 批量分析 -->
set odps.sql.hive.compatible=true;
select
    <#list tagList as column>
        ${column},
    </#list>
    value
from (
    select
        <#list tagList as column>
            ${column},
        </#list>
        value
        , ROW_NUMBER() OVER(PARTITION BY GROUPING__ID ORDER BY value <#if sc??>${sc}</#if>) AS rk
    from (
        SELECT
            <#list tagList as column>
                ${column},
            </#list>
            COUNT(distinct a.user_id) AS value
            , GROUPING__ID
        FROM
        (
            select user_id from trip_profile.dwd_trip_crowd_data_snapshot where group_id = '${crowdId}'
        ) a
        INNER JOIN
        (
            SELECT user_id
                <#list tagList as column>
                    , coalesce(${column}, '未知' ) as ${column}
                </#list>
            FROM trip_profile.trip_picasso_taobao_user_profile
            WHERE ds = MAX_PT('trip_profile.trip_picasso_taobao_user_profile')
        ) b
        ON a.user_id  = b.user_id
        GROUP BY grouping sets (
            <#list groupList as column>
                (${column}) <#if column_has_next>,</#if>
            </#list>
        )
    ) t
) t
<#if limitCount??>where rk <= ${limitCount}</#if>
;