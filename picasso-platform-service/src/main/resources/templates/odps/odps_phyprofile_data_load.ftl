<#-- ODPS物理画像表数据加载 -->
INSERT OVERWRITE TABLE ${project}.${table} PARTITION (${partitionKey} = '${partitionValue}')
SELECT
<#list selectedColumns as column>
    ${column} <#if column_has_next>,</#if>
</#list>
FROM (
    SELECT
    <#list masterTable.columns as column>
         ${column.oriName} AS ${column.aliasName}  <#if column_has_next>,</#if>
    </#list>
    FROM ${masterTable.project}.${masterTable.table}
    WHERE ${masterTable.partitionKey} = '${masterTable.partitionValue}'
) ${masterTable.alias}
<#list associatedTables as associatedTable>
LEFT JOIN (
    SELECT
    <#list associatedTable.columns as column>
        ${column.oriName} AS ${column.aliasName}  <#if column_has_next>,</#if>
     </#list>
    FROM ${associatedTable.project}.${associatedTable.table}
    WHERE ${associatedTable.partitionKey} = '${associatedTable.partitionValue}'
    <#if associatedTable.condition??>and ${associatedTable.condition}</#if>
    ) ${associatedTable.alias}
ON ${masterTable.alias}.${masterTable.columns[0].aliasName} = ${associatedTable.alias}.${associatedTable.columns[0].aliasName}
</#list>
;