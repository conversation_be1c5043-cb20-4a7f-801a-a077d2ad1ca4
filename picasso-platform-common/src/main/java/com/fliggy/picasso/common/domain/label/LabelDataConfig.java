package com.fliggy.picasso.common.domain.label;

import com.fliggy.picasso.common.enums.label.LabelDataDescEnum;
import com.fliggy.picasso.common.enums.label.LabelDataTimeTypeEnum;
import com.fliggy.picasso.common.enums.label.LabelDataValueTypeEnum;
import com.fliggy.picasso.common.enums.label.LabelSeperatorEnum;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class LabelDataConfig {

    /**
     * 数据值类型，string/bigint等
     */
    private LabelDataValueTypeEnum dataValueType;

    /**
     * 单值/多值枚举
     * 取值描述，手动输入/自动配置/通用枚举等
     */
    private LabelDataDescEnum dataDesc;

    /**
     * 单值/多值枚举
     * 取值范围，手动输入/自动配置时有值
     */
    private Map<String, String> dataValue;

    /**
     * 多值枚举
     * 多值枚举的分隔符，默认逗号
     */
    private LabelSeperatorEnum multiEnumSep;

    /**
     * 日期类型
     * 时间类型，默认年月日
     */
    private LabelDataTimeTypeEnum timeType;

    /**
     * KV类型
     * k与v之间分隔符，默认冒号
     */
    private LabelSeperatorEnum kvSep;

    /**
     * KV类型
     * kv组之间分隔符，默认逗号
     */
    private LabelSeperatorEnum kvGroupSep;

    /**
     * KV类型
     * kv的展示key
     */
    private String kvDisplayKey;

    /**
     * KV类型
     * kv的展示value
     */
    private String kvDisplayValue;

    /**
     * key-value型标签的最小值、最大值和步长（最小取值范围）
     * [0, 1, 0.2]
     */
    private List<Double> kvWeightRange;
}
