package com.fliggy.picasso.common.enums.label;

import lombok.Getter;

@Getter
public enum LabelTypeEnum {

    SINGLE_ATTRIBUTE("single_attribute", "单一属性标签"),
    COMBINE_DIMENSION("combine_dimension", "组合维度标签"),
    BEHAVIOR("behavior", "行为标签"),
    SELF_DEFINED_SQL("self_defined_sql", "自定义SQL标签"),
    ;

    private final String code;
    private final String desc;

    LabelTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LabelTypeEnum findByCode(String code) {
        for (LabelTypeEnum labelTypeEnum : values()) {
            if (labelTypeEnum.getCode().equals(code)) {
                return labelTypeEnum;
            }
        }
        return null;
    }

}
