package org.jeasy.flows.work;


import org.jeasy.flows.workflow.WorkFlow;

/**
 * 空work，用来组装空动作到FLow里
 */
public class BlankFlowWork implements WorkFlow {

    private final String name;
    private final WorkStatus status;
    private boolean executed;

    public BlankFlowWork(String name, WorkStatus status) {
        this.name = name;
        this.status = status;
    }

    public BlankFlowWork() {
        this.name  = "blank";
        this.status = WorkStatus.COMPLETED;
    }


    @Override
    public String getName() {
        return name;
    }

    @Override
    public WorkReport execute(WorkContext workContext) {
        executed = true;
        return new DefaultWorkReport(status, workContext);
    }

    public boolean isExecuted() {
        return executed;
    }
}